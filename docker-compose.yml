version: '3.8'
services:
  project-base:
    image: "${IMAGE_REGISTRY}${PROJECT_NAME}/base-image:${IMAGE_TAG}"
    container_name: "${PROJECT_NAME}-base"
    pull_policy: build
    build:
      context: ./docker/base/
      dockerfile: Dockerfile
      args:
        PHP_VERSION: $PHP_VERSION
        NODE_VERSION: $NODE_VERSION
        SESSION_TIMEOUT: $SESSION_TIMEOUT
    tty: true

  project-app:
    image: "${IMAGE_REGISTRY}${PROJECT_NAME}/app-image:${IMAGE_TAG}"
    container_name: "${PROJECT_NAME}-app"
    build:
      context: ./
      dockerfile: Dockerfile
      args:
        IMAGE_REGISTRY: $IMAGE_REGISTRY
        PROJECT_NAME: $PROJECT_NAME
        IMAGE_TAG: $IMAGE_TAG
        APP_DIR: $APP_DIR
        HTTP_PORT: $HTTP_PORT
    restart: on-failure
    tty: true
    working_dir: $APP_DIR
    env_file:
      - .env
      - project/.env
      - project/client/.env
    volumes:
      - "${PWD}/project:${APP_DIR}"
    ports:
      - "${HTTP_PORT}:${HTTP_PORT}"
    networks:
      - project-network
    depends_on:
      - project-database

  project-database:
    image: ${MYSQL_IMAGE}:${MYSQL_VERSION}
    container_name: "${PROJECT_NAME}-database"
    platform: ${PLATFORM}
    restart: on-failure
    tty: true
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: $MYSQL_PASSWD
      MYSQL_DATABASE: $MYSQL_DB
    command: --init-file /data/application/init.sql
    volumes:
      - "database-volume:/var/lib/mysql"
      - ./docker/mysql/init.sql:/data/application/init.sql
    networks:
      - project-network

networks:
  project-network:
    name: $PROJECT_NAME-network
    driver: bridge

volumes:
  database-volume:
