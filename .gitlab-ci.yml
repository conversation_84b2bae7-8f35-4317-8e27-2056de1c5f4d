stages:
  - base
  - build
  - staging
  - deploy
  - retire

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH

## TEMPLATES: #####

.build_template: &build_template
  image: google/cloud-sdk:418.0.0-alpine
  stage: build
  before_script:
    - /bin/bash bin/gcloud_helper.sh --prepare
  variables:
    option: --app-image
    environment: dev
  script:
    - /bin/bash bin/build.sh $option $environment
  allow_failure: false

.deploy_template: &deploy_template
  image: google/cloud-sdk:418.0.0-alpine
  stage: deploy
  before_script:
    - /bin/bash bin/gcloud_helper.sh --prepare
  variables:
    option: --dev
    environment: dev
  script:
    - /bin/bash bin/deploy.sh $option
  allow_failure: false

.retire_template: &retire_template
  image: google/cloud-sdk:418.0.0-alpine
  stage: retire
  before_script:
    - /bin/bash bin/gcloud_helper.sh --prepare
  variables:
    option: --dev
    environment: dev
  script:
    - /bin/bash bin/retire.sh $option
  allow_failure: false

## BUILD: #####

build-base-image:
  image: google/cloud-sdk:418.0.0-alpine
  stage: base
  before_script:
    - /bin/bash bin/gcloud_helper.sh --prepare
  variables:
    option: --base-image
    environment: base
  script:
    - /bin/bash bin/build.sh $option $environment
  rules:
    - changes:
        - docker/base/**/*
      when: always
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE != "merge_request_event"
      when: never
    - if: $CI_COMMIT_BRANCH
      when: manual
      allow_failure: true

build-dev-app-image:
  <<: *build_template
  variables:
    option: --app-image
    environment: dev
  when: on_success
  needs:
    - job: build-base-image
      optional: true
  except:
    - main

build-prod-app-image:
  <<: *build_template
  variables:
    option: --app-image
    environment: prod
  when: manual
  needs:
    - job: build-base-image
      optional: true
  only:
    - main

build-staging-app-image:
  <<: *build_template
  variables:
    option: --app-image
    environment: staging
  when: on_success
  needs:
    - job: build-base-image
      optional: true
  only:
    - main

## DEPLOY: #####

deploy-dev:
  <<: *deploy_template
  variables:
    option: --dev
    environment: dev
  when: on_success
  needs:
    - build-dev-app-image
  except:
    - main

deploy-production:
  <<: *deploy_template
  variables:
    option: --prod
    environment: prod
  when: manual
  needs:
    - build-prod-app-image
    - deploy-staging
  only:
    - main

deploy-staging:
  <<: *deploy_template
  stage: staging
  variables:
    option: --staging
    environment: staging
  when: on_success
  needs:
    - build-staging-app-image
  only:
    - main

## RETIRE: #####

retire-dev:
  <<: *retire_template
  variables:
    option: --dev
    environment: dev
  when: manual
  allow_failure: true
  needs:
    - deploy-dev
  except:
    - main

retire-older-dev-instances:
  <<: *retire_template
  variables:
    option: --old
    environment: dev
  when: on_success
  allow_failure: true
  needs:
    - deploy-dev
  except:
    - main

retire-production:
  <<: *retire_template
  variables:
    option: --prod
    environment: prod
  when: manual
  allow_failure: true
  needs:
    - deploy-production
  only:
    - main

retire-staging:
  <<: *retire_template
  variables:
    option: --staging
    environment: staging
  when: manual
  allow_failure: true
  needs:
    - deploy-staging
  only:
    - main
