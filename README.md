# Mailroom

Mailroom is our department responsible for handling mail for customers. Also, we refer to the system they use to perform important actions with the same name.

> 💡 Check [this other page](https://www.notion.so/Mailroom-59c53dca8b3940dcb8f844c0bb58df98) so understand their process, etc. Keep in mind that it might be outdated depending on when you are reading it since we are going to update the process.

## Application Dependencies

This application depends on these external services:

- Mantle Reloaded Scripts [see more here](https://www.notion.so/madesimplegroup/New-Mailroom-273d45285d4c4ae39c617df5b642ffd6?pvs=4#a328e2df416d4960acc1730f150df0bb):
  - [mr-bucket-api.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-bucket-api.rb)
  - [mr-bucket-cleaner.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-bucket-cleaner.rb)
  - [mr-convert-pdf-loop.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-convert-pdf-loop.rb)
  - [mr-ocr-image-function.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-ocr-image-function.rb)
  - [mr-predict-function.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-predict-function.rb)
  - [mr-find-company-loop.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-find-company-loop.rb)
  - [mr-match-api.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-match-api.rb)
  - [mr-completed-function.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-completed-function.rb)
  - [kofax-post-items-webhook.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/kofax-post-items-webhook.rb)
  - [mr-ui-status-api.rb](https://gitlab.com/madesimplegroup/mantle-reloaded/-/tree/master/ruby_scripts/mr-ui-status-api.rb)
- Mantle Reloaded Database (`cms_mantle` database)
  - The table `mailroom_match_events` is part of the `cms_mantle` database and is used for recording events related to company name matching. Details:
    - `id` (int): Primary key.
    - `predicted_company_name` (text): This column stores the predicted company name.
    - `guessed_company_number` and `guessed_company_name`: It stores a guessed company number/name (the one found in our systems based on the `predicted_company_name`).
    - `matched_company_number` and `matched_company_name`: It stores the matched company number/name (the one the user actually matched using Mailroom).
    - `prediction_score` (float): This field stores the prediction score (provided by the GCP AI service).
    - `predicted_guessed_similarity` (float): This field stores a similarity score between the *predicted* and *guessed* company names.
    - `predicted_matched_similarity` (float): This field stores a similarity score between the *predicted* and *matched* company names.
    - `guessed_matched_similarity` (float): This field stores a similarity score between the *guessed* and *matched* company names.
    - `kofax_item_id` (int): This column is a foreign key for the `kofax_post_items` table in CMS database.
    - `edited` (tinyint(4)): This column indicates whether the record has been edited or modified by the user in the Mailroom > Edit modal.
    - `batch_id` (text): This column stores the batch identifier.
    - `operator` (text): This column stores the identifier of the user responsible for the record.
    - `dtc` (datetime): This is a timestamp indicating the creation date of the record.
    - `dtm` (datetime): This is a timestamp indicating the modification date of the record.
    - `mail_type` (varchar(100)): This is the mail type or sender of each item.
- CMS API
  - Params: `companyName`, `officerName`, `lpNumber`
  - Method: `GET`
  - Endpoint: `https://www.companiesmadesimple.com/api/mail-scan/mailroom-search/`
- [GCP Pub/Sub](https://console.cloud.google.com/cloudpubsub/topic/list?project=automl-post-items)
  - Project: `automl-post-items`
    - mr_completed
    - mr_converted_to_image
    - mr_ocred
    - mr_pdf_uploaded
    - mr_predicted
    - mr_ui_status

## Development Setup

### Requirements

- WSL installed (Windows users only) [see more here](https://learn.microsoft.com/en-us/windows/wsl/install)
- Docker installed [see more here](https://docs.docker.com/engine/install/)
- Ports `8080` (for `nginx` web server) and `3306` (for `mysql` database) **MUST** be free (unused by other applications)
  - You can check that by running: `sudo lsof -i:8080` and `sudo lsof -i:3306` (on Linux)

### Setup

- **Clone this repo**
  - `<NAME_EMAIL>:madesimplegroup/mailroom.git && cd mailroom`

- **Setup Environment files**
  - Docker local development environment (mailroom/.env): 
    - Example: https://gitlab.com/madesimplegroup/mailroom/-/blob/153af65b2264e5d78f3791d71b1139f571c5410e/env-example
  - Symfony/Backend local development environment (mailroom/project/.env): 
    - Example: https://gitlab.com/madesimplegroup/mailroom/-/blob/153af65b2264e5d78f3791d71b1139f571c5410e/project/env-example
  - Node/Vue/Frontend local development environment (mailroom/project/client/.env): 
    - Example: https://gitlab.com/madesimplegroup/mailroom/-/blob/153af65b2264e5d78f3791d71b1139f571c5410e/project/client/env-example
  - **NOTE 1:** 
    - _If running on **ARM** architecture (e.g., newer MAC machines), in the `.env` file of the main (this) folder:_
      - _Comment out lines under Linux/AMD64_
      - _Uncomment lines under MAC/ARM)_
  - **NOTE 2:** 
    - _This must be done before running the `bin/dev.sh` script_

- **Build and Setup the Docker containers**
  - The following steps must be completed on the **first setup** and every time the `Dockerfiles`, `composer` files and node `package` files are changed:
    - Run: `bin/dev.sh --setup` to **build** the docker images for this project
    - Run: `bin/dev.sh --up` to **create** and **start** containers for this project

### Helper functions

- Run: `bin/dev.sh --up` to create and **start** containers for this project
- Run: `bin/dev.sh --down` to **stop and remove** containers for this project
- Run: `bin/dev.sh --reset` to **stop and remove all** containers, images and volumes for this project
  - This can be used to "purge" all the project dev. env. from your machine
- Other developer helper functions:
  - Run: `bin/dev.sh --access-app` to access the bash for the **application container**
  - Run: `bin/dev.sh --access-db` to access the bash for the **database container**

## Development Tools

- To use the debug menu on the postItems table view, make sure to pass `debug=on` as a parameter on the url like so: `http://localhost:8080/dashboard?debug=on`.
- Mailroom has a `test-mode` setting that can be toggled on/off to gain access to developer features.
  - To be able to see and use test-mode features, you need to add a row to the `developer_list` table of the `mailroom_dev` database. This row will need your `email`, `uid` and `enabled` status.
    - `email`: The email you use to log into Mailroom.
    - `uid`: You can get your `uid` value by looking into the Vuex `uid` value under the application tab in your browser's developer tools while logged into Mailroom.
    - `enabled`: This value should be set to `1` in order to be able to use `test-mode`. Set to `0` if you want to turn `test-mode` features off for that developer.
  - To hide/unhide HTML elements meant to only be seen with test-mode on/off simply add the `test-mode` or `test-mode-off` class to it. It's important to note that this functionality only works on non-production environments, so these elements should be hidden and disabled by default by adding the `hidden="hidden"` and `disabled="disabled"` attributes to it.
- As mentioned above as a dependency, Mailroom has a database that store the matching events for each post items and can be used to track the performance of the AI or potentially other unforeseen issues with the application/ecosystem.

## Creating APIs

- When creating APIs, make sure to annotate the endpoints of the new API method you create, following one of the examples.
- APIs that are meant to be accessed by applications other than Mailroom should have their endpoints added to the `EXTERNAL_API_ROUTES` constant array in `project/src/Middleware/Listener.php`, to ensure that the authentication method is set to the correct one.

## Pipelines

The [Gitlab CI file](https://gitlab.com/madesimplegroup/mailroom/-/blob/main/.gitlab-ci.yml) defines the rules and the structure for the CI pipelines.
We use pipelines only for Merge Requests, so make sure to create a MR to your branch.
You can visualize the pipeline structure and jobs dependencies here: https://gitlab.com/madesimplegroup/mailroom/-/ci/editor?branch_name=main&tab=1
There are two pipelines structures:

### Merge Request Pipelines (for dev. environment)

This type of pipeline is generated when you create/push any changes to the branch of the MR (git push origin [your-branch]).

- The following stages and jobs are created:
  - `base`: This stage builds the Docker `base-image` for the project.
    - `build-base-image`: This is created only when something in the `docker/base/` folder is changed.
  - `build`: This stage builds the Docker `dev-image` for the project based on the prev. `base-image`.
    - `build-dev-app-image`: This is created for all changes in the MR.
  - `deploy`: This stage deploys the application to the GCP Cloud Run using the `dev-image` created in prev. the stage.
    - `deploy-dev`: This is created for all changes in the MR (runs automatically after build is succeed).
  - `retire`: This stage stops and delete the application from Cloud Run.
    - `retire-dev`: This job deletes just the Cloud Run service related to the MR (runs manually).
    - `retire-older-dev-instances`: This job deletes all the dev Cloud Run services older than 7 days (runs automatically after deploy is succeed).

### Merged Pipelines (for staging and production environment)

This type of pipeline is generated when you create/push any changes to the branch of the MR (git push origin [your-branch]).

- The following stages and jobs are created:
  - `base`: This stage builds the Docker `base-image` for the project.
    - `build-base-image`: This is created only when something in the `docker/base/` folder is changed.
  - `build`: This stage builds the Docker images for the app based on the prev. `base-image`.
    - `build-staging-app-image`: This job builds the `staging-image`
    - `build-prod-app-image`: This job builds the `prod-image`
  - `staging`: This stage deploys the staging app to Cloud Run.
    - `deploy-staging`: This job deploys the Cloud Run service using the `staging-image` created previously.
  - `deploy`: This stage deploys the production app to Cloud Run.
    - `deploy-production`: This job deploys the Cloud Run service using the `prod-image` created previously.
  - `retire`: This stage stops and delete the application from Cloud Run.
    - `retire-production`: This job deletes just the Cloud Run service related to the production app (runs manually).
    - `retire-staging`: This job deletes just the Cloud Run service related to the staging app (runs manually).
