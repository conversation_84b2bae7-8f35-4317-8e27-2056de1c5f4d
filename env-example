# This is a dotenv file exclusively for docker and CI/CD environment variables
# It should have only the default values and development environment values

PROJECT_NAME=mailroom

PHP_VERSION=8.0-fpm
NODE_VERSION=20

# FOR MAC/ARM
# MYSQL_VERSION=8.0.32
# MYSQL_IMAGE=arm64v8/mysql
# PLATFORM=osx/arm64

# FOR Linux/AMD64
MYSQL_VERSION=8.0.32-debian
MYSQL_IMAGE=mysql
PLATFORM=linux/amd64

APP_DIR=/var/www/project

IMAGE_REGISTRY=
IMAGE_TAG=latest

GCP_PROJECT_REGION=
GCP_PROJECT_ID=
GCP_EMAIL_ACCOUNT=

SESSION_TIMEOUT=3600
HTTP_PORT=8080

MYSQL_PASSWD=root
MYSQL_DB=mailroom
