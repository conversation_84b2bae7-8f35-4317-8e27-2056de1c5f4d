steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'build', '--no-cache',
        '--build-arg', 'NODE_ENV=${_NODE_ENV}',
        '--build-arg', 'BUILD_DATE=${_BUILD_DATE}',
        '--build-arg', 'BUILD_COMMIT=${_COMMIT_SHA}',
        '--build-arg', 'IMAGE_REGISTRY=${LOCATION}-docker.pkg.dev/${PROJECT_ID}/',
        '-t', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_TAG}',
        '-t', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_COMMIT_SHA}',
        '.'
      ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'push', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_TAG}',
      ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'push', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_COMMIT_SHA}',
      ]
substitutions:
  _PROJECT_NAME: mailroom
  _IMAGE_NAME: dev-image
  _TAG: latest
  _COMMIT_SHA: latest
  _BUILD_DATE: ''
  _NODE_ENV: development
