# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    basic_auth_token: '%env(APP_BASIC_AUTH_TOKEN)%'
    storage_url: '%env(STORAGE_URL)%'
    restricted_actions: '%env(RESTRICTED_ACTIONS)%'
    environment: '%env(APP_ENV)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    App\Command\ReportAiLog:
        tags: ['console.command']

    acme_controller.listener:
        class: App\Middleware\Listener
        arguments: ['%basic_auth_token%']
        tags:
            - { name: kernel.event_listener, event: kernel.controller, method: onKernelController }

    application_server.listener.response_interceptor:
        class: App\Middleware\Listener
        arguments: ['%basic_auth_token%']
        tags:
            - { name: kernel.event_listener, event: kernel.response, method: onKernelResponse }

#    message_param_converter:
#        class: 'App\ParamConverter\MessageParamConverter'
#        tags: ['request.param_converter']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    discord_monitoring_webhook:
        class: App\Service\DiscordMonitoringWebhook
        arguments: ["%kernel.environment%", "%env(DISCORD_WEBHOOK_MONITORING)%"]

    published_message.listener:
        class: App\Listener\MonitoringListener
        arguments: ['@discord_monitoring_webhook']
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }