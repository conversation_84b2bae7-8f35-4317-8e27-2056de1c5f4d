when@prod:
    sentry:
        dsn: '%env(SENTRY_DSN)%'
        # this hooks into critical paths of the framework (and vendors) to perform
        # automatic instrumentation (there might be some performance penalty)
        # https://docs.sentry.io/platforms/php/guides/symfony/performance/instrumentation/automatic-instrumentation/
        tracing:
            enabled: true
        options:
          # Specify a fixed sample rate
          traces_sample_rate: 1.0

#        If you are using Monolog, you also need this additional configuration to log the errors correctly:
#        https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
#        register_error_listener: false
#        register_error_handler: false

    monolog:
        handlers:
            sentry:
                type: sentry
                level: !php/const Monolog\Logger::WARNING
                hub_id: Sentry\State\HubInterface
#                fill_extra_context: true # Enables sending monolog context to Sentry ## Option not available on this version
                process_psr_3_messages: false # Disables the resolution of PSR-3 placeholders
