monolog:
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks
        nested:
            type: stream
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
            file_permission: 0777
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]

        # Uncomment to log deprecations
        #deprecation:
        #    type: stream
        #    path: "%kernel.logs_dir%/%kernel.environment%.deprecations.log"
        #deprecation_filter:
        #    type: filter
        #    handler: deprecation
        #    max_level: info
        #    channels: ["php"]
