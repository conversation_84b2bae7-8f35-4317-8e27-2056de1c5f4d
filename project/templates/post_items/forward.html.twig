{% extends 'base.html.twig' %}

{% block styles %}
    <link href="{{ version_asset('/css/chunk-vendors.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/app.css') }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
    <script src="{{ version_asset('/js/chunk-vendors.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/app.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/ModalTools.js') }}"></script>
    <script src="{{ version_asset('/js/InjectModalValues.js') }}"></script>
    <script src="{{ version_asset('/js/StickyAlertTooltips.js') }}"></script>
{% endblock %}

{% block body %}
    <div id="header"></div>
    {% block container %}
        <main class="container-fluid container-xl my-4">
            <p class="fs-5">Forward Item to {{ postItem.companyName }}</p>

            {% for message in app.flashes('error') %}
                <div class="alert alert-danger">
                    {{ message }}
                </div>
            {% endfor %}

            {{ form_start(form, { 'attr' : { 'data-post-item' : "#{postItem.id}" } }) }}

            {% include '/post_items/forwarding-confirm-modal.html.twig' with { postItem: postItem } %}

                {% block forwardAddress %}
                    <section class="card my-4">
                        <h5 class="card-header">Forward Address</h5>
                        <div class="card-body">
                            {% if postItem.address.address1 is defined and postItem.address.address1 is not empty %}
                                {{ postItem.address.address1 }} <br/>
                            {% endif %}

                            {% if postItem.address.address2 is defined and postItem.address.address2 is not empty %}
                                {{ postItem.address.address2 }} <br/>
                            {% endif %}

                            {% if postItem.address.address3 is defined and postItem.address.address3 is not empty %}
                                {{ postItem.address.address3 }} <br/>
                            {% endif %}

                            {% if postItem.address.postcode is defined and postItem.address.postcode is not empty %}
                                {{ postItem.address.postcode }} <br/>
                            {% endif %}

                            {% if postItem.address.town is defined and postItem.address.town is not empty %}
                                {{ postItem.address.town }} <br/>
                            {% endif %}

                            {% if postItem.address.country is defined and postItem.address.country is not empty %}
                                {{ postItem.address.country }}
                            {% endif %}
                        </div>
                    </section>
                {% endblock %}

                {% block postInformation %}
                    <section class="card my-4">
                        <h5 class="card-header">Post Information</h5>
                        <div class="card-body">
                            {% if postItem.type == "statutory" %}
                                {{ form_row(form.statutory_item) }}
                            {% elseif postItem.type == "special" %}
                                {{ form_row(form.total_weight) }}
                            {% elseif postItem.type == "parcel" %}
                                {{ form_row(form.total_weight)}}
                                {{ form_row(form.dimensions) }}
                                {% if form.custom_price is defined %}
                                    {{ form_row(form.custom_price) }}
                                {% endif %}
                                {% if form.description is defined %}
                                    {{ form_row(form.description) }}
                                {% endif %}
                            {% else %}
                                {{ form_row(form.total_weight) }}
                            {% endif %}

                        </div>
                    </section>
                {% endblock %}

                {% block postageClass %}
                    <section class="card my-4">
                        <h5 class="card-header">Postage Class</h5>
                        <div class="card-body">
                            {% for child in form.postage_class %}
                                <div>
                                    {{ form_widget(child, {'attr':{'disabled' : alreadyForwarded } }) }}
                                    {{ form_label(child) }}
                                </div>
                            {% endfor %}
                        </div>
                    </section>
                {% endblock %}

                <div class="d-flex p-0 gap-3">
                    <a
                        type="button"
                        class="btn btn-outline-primary"
                        href="{{ path('app_post_items', filter) }}"
                    >
                    Back
                    </a>

                    <button
                        type="button"
                        class="btn btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#forwardConfirmModal"
                        onclick="InjectModalValuesHelper.updateForwardingModal(); StickyAlertTooltipsHelper.rigModalSubmitButton();"
                        {% if alreadyForwarded %}disabled="disabled"{% endif %}
                    >
                        Save &amp; Print
                    </button>
                </div>

            {{ form_end(form) }}
        </main>
    {% endblock %}
    <div id="footer"></div>
{% endblock %}
