<div class="modal fade" id="collectModal">
    <div class="modal-dialog d-flex justify-content-center">
        <div class="modal-content" style="max-width: 60em; padding: 48px;">
            <div class="modal-body d-flex flex-column p-0">
                {{ form_start(collectedForm) }}
                    <div class="container-fluid container-xl p-0">
                        <p class="fs-5 m-0">
                            Log post item as collected
                        </p>

                        <p style="margin-top: 48px; margin-bottom: 0px;">
                            You are about to collect the post item below for company <span id="collectItemCompanyName"></span>. Please enter the collector's information below.
                        </p>

                        <div class="table-responsive mx-5" style="margin-top: 24px; margin-bottom: 24px;">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th class="px-4"> Type </th>
                                        <th class="px-4"> Sender </th>
                                        <th class="px-4"> Added on </th>
                                    </tr>
                                </thead>
                                <tbody class="table-group-divider">
                                    <tr>
                                        <td class="px-4 d-none" id="collectItemBatchNumber"></td>
                                        <td class="px-4 d-none" id="collectItemCompanyNumber"></td>
                                        <td class="px-4" id="collectItemType"></td>
                                        <td class="px-4" id="collectItemSender"></td>
                                        <td class="px-4" id="collectItemDtc"> </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        {{ form_row(collectedForm.collected_name) }}
                        {{ form_row(collectedForm.collected_number) }}

                        <div class="d-flex justify-content-end" style="margin-top: 48px;">
                            <button
                                type="button"
                                class="btn
                                btn-secondary"
                                style="min-width: 5em; margin-right: 16px;"
                                data-bs-dismiss="modal"
                                aria-label="Close"
                            >
                                Cancel
                            </button>
                            {{ form_row(collectedForm.save) }}
                        </div>

                    </div>
                {{ form_end(collectedForm) }}
            </div>
        </div>
    </div>
</div>