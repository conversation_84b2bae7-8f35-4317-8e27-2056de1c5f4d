{% extends 'base.html.twig' %}

{% block styles %}
    <link href="{{ version_asset('/css/chunk-vendors.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/app.css') }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
    <script src="{{ version_asset('/js/chunk-vendors.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/app.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/AddPostItem.js') }}"></script>
    <script src="{{ version_asset('/js/AddItemParcelOption.js') }}"></script>
    <script src="{{ version_asset('/js/StatutoryItemOption.js') }}"></script>
    <script defer="defer">
        StatutoryItemHelper.initialize();
        AddPostItemHelper.initialize();
        AddItemParcelHelper.initialize();
    </script>
{% endblock %}

{% block body %}
    <div id="header"></div>
    {% include '/post_items/add-parcel-modal.html.twig' %}
    {% include '/post_items/loader.html.twig' %}
    {% include '/post_items/statutory-item-info-modal.twig' %}
    <main class="container-fluid container-xl my-4">
        <p class="fs-4" class="mb-3">Add Post Item</p>

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger">
                {{ message }}
            </div>
        {% endfor %}

        <div class="flex-fill">
            {{ form_start(searchForm) }}
                    {{ form_row(searchForm.companyName, {'label': 'Company search'}) }}
                    {{ form_row(searchForm.search) }}
            {{ form_end(searchForm, {'render_rest': false}) }}
        </div>

        {% if companyList is not empty %}
            <div class="mb-2">
                Please select a company below:
            </div>
            <div class="container border rounded-2 mb-3 p-0" style="max-height: 30em; overflow-y: scroll;">
                <table class="table table-sm table-condensed table-hover fixed-headers">
                    <thead class="sticky-top bg-light shadow-sm">
                        <tr>
                            <th colspan="1" scope="col" class="ps-3">Company Number</th>
                            <th colspan="3" scope="col" class="ps-3">Company Name</th>
                            <th colspan="3" scope="col" class="ps-3">Service</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in companyList %}
                            <tr onclick="AddPostItemHelper.setCompany('{{ company.companyNumber }}', '{{ company.companyName }}')" id="company_{{company.companyNumber}}" >
                                <td colspan="1" class="ps-3">
                                    {{ company.companyNumber }}
                                </td>
                                <td colspan="3" class="ps-3">
                                    {{ company.companyName }}
                                </td>
                                <td>
                                    {% if company.serviceName is not null and company.serviceExpiration is not null %}
                                        {{ company.serviceName ?? '' }}
                                        {% if "now"|date <= company.serviceExpiration|date %}
                                            <span class="text-success fst-italic">(active)</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-danger fst-italic">(No service)</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {{ form_start(addItemForm) }}

            {{ form_row(addItemForm.company_number) }}
            {{ form_row(addItemForm.company_name) }}
            {{ form_row(addItemForm.type) }}
            {{ form_row(addItemForm.total_weight) }}

            <div class="mb-3">
                <label class="form-label">Dimensions (length × width × height) in cm</label>
                <div class="d-flex gap-2">
                    <div class="flex-grow-1">
                        {{ form_widget(addItemForm.length, {'attr': {'placeholder': 'Length'}}) }}
                        <small class="form-text text-muted text-center d-block">Length (cm)</small>
                    </div>
                    <div class="flex-grow-1">
                        {{ form_widget(addItemForm.width, {'attr': {'placeholder': 'Width'}}) }}
                        <small class="form-text text-muted text-center d-block">Width (cm)</small>
                    </div>
                    <div class="flex-grow-1">
                        {{ form_widget(addItemForm.height, {'attr': {'placeholder': 'Height'}}) }}
                        <small class="form-text text-muted text-center d-block">Height (cm)</small>
                    </div>
                </div>
            </div>
            {{ form_row(addItemForm.custom_price_enabled) }}
            {{ form_row(addItemForm.custom_price) }}
            {{ form_row(addItemForm.description) }}
            {{ form_row(addItemForm.sender) }}

            <div class="d-flex p-0 gap-3 my-3">
                <a
                    type="button"
                    class="btn btn-outline-primary align-self-end"
                    href="{{ path('app_post_items', filter) }}"
                >
                Back
                </a>
                {{ form_widget(addItemForm.save) }}
                <div class="row p-0 m-0 test-mode" hidden="hidden" disabled="disabled">
                    {{ form_widget(addItemForm.add_multiple) }}
                    {{ form_label(addItemForm.add_multiple) }}
                </div>
            </div>

            {{ form_end(addItemForm) }}
        {% else %}
            <div class="mb-2">
                Please search for a company above.
            </div>
            {% if companyNotFound %}
                <div class="mb-2 alert alert-danger alert-dismissible" role="alert">
                    Company not found
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endif %}
        {% endif %}

    </main>
    <div id="footer"></div>
{% endblock %}