<!DOCTYPE html>
<html>
	<style>
		.loading-page {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 0.8);
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			z-index: 9999;
		}
		.spinner {
			border: 4px solid #8ebbde;
			border-top: 5px solid #0D6EFD;
			border-radius: 50%;
			width: 32px;
			height: 32px;
			animation: spin 0.8s linear infinite;
		}
		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}
		.loading-message {
			font-size: 16px;
			margin-left: 10px;
		}
		.spinner-container {
			display: flex;
			align-items: center;	
		}
	</style>

	<div id="loading-page" class="loading-page">
		<div class="spinner-container">
			<div class="spinner"></div>
			<div class="loading-message">Please wait</div>
		</div>
	</div>
</html>

<script type="text/javascript">
	function showLoader() {
		const loader = document.getElementById('loading-page');
		loader.style.display = 'flex';
	}
	function hideLoader() {
		const loader = document.getElementById('loading-page');
		loader.style.display = 'none';
	}
	window.addEventListener("beforeunload", function(event) {
		showLoader()
		
	})
	window.addEventListener("load", function(event) {
		hideLoader();
	})
</script>
