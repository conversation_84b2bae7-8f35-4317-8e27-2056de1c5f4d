<html>
    <style>@page{size:100mm 60mm}@media print { @page { margin: 0; padding: 0;}}</style>
    <div id='label' style='margin-top: 10px;font-size:18px;height: 210px;width: 400px; padding-left: 10px; padding-right: 10px;'>
        <div>
            <div style='margin-top: 20px; width: 30%; float: left;'>{{ postItem.companyNumber }}</div>
            <div style='margin-top: auto; margin-bottom: auto; width: 70%; float: left;'>
                <img src="{{ labelImage }}" style='width: 365px;'>
            </div>
        </div>
        <div style='line-height: 1;'>
            {% if postItem.companyName is defined and postItem.companyName is not empty %}
                {{ postItem.companyName }} <br/>
            {% endif %}

            {% if postItem.address.address1 is defined and postItem.address.address1 is not empty %}
                {{ postItem.address.address1 }} <br/>
            {% endif %}

            {% if postItem.address.address2 is defined and postItem.address.address2 is not empty %}
                {{ postItem.address.address2 }} <br/>
            {% endif %}

            {% if postItem.address.address3 is defined and postItem.address.address3 is not empty %}
                {{ postItem.address.address3 }} <br/>
            {% endif %}

            {% if postItem.address.postcode is defined and postItem.address.postcode is not empty %}
                {{ postItem.address.postcode }} <br/>
            {% endif %}

            {% if postItem.address.town is defined and postItem.address.town is not empty %}
                {{ postItem.address.town }} <br/>
            {% endif %}

            {% if postItem.address.country is defined and postItem.address.country is not empty %}
                {{ postItem.address.country }} <br/>
            {% endif %}
        </div>
    </div>
</html>

<script type="text/javascript">
    setTimeout(function(){ window.print(); }, 500);
</script>
