{% extends 'base.html.twig' %}

{% block styles %}
    <link href="{{ version_asset('/css/chunk-vendors.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/app.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/custom.css') }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
    <script src="{{ version_asset('/js/chunk-vendors.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/app.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/ModalTools.js') }}"></script>
    <script src="{{ version_asset('/js/InjectModalValues.js') }}"></script>
    <script src="{{ version_asset('/js/SelectMultipleItems.js') }}"></script>
    <script defer="defer">
        SelectMultipleItemsHelper.initialize();
    </script>
{% endblock %}

{% block body %}
    <div id="header"></div>
    <main class="container-fluid container-xl my-5">
        {% include '/post_items/rts-confirm-modal.html.twig' with { RTSForm: RTSForm } %}
        {% include '/post_items/securely-destroy-confirm-modal.html.twig' with { securelyDestroyForm: securelyDestroyForm } %}
        {% include '/post_items/collected-modal.html.twig' with { collectedForm: collectedForm } %}
        {% include '/post_items/post-items-details-modal.html.twig' %}
        {% include '/post_items/collected-bulk-modal.html.twig' %}
        {% include '/post_items/loader.html.twig' %}

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger">
                {{ message }}
            </div>
        {% endfor %}

        <div class="m-0 p-0 gap-3 d-flex flex-wrap justify-content-between">
            {{ form_start(searchForm) }}
                {{ form_row(searchForm.companyName) }}
                {{ form_row(searchForm.companyNumber) }}
                {{ form_row(searchForm.search) }}
            {{ form_end(searchForm, {'render_rest': false}) }}
            <div class="align-self-end my-3">
                {{ form(filteringStatusForm) }}
            </div>
            <div class="align-self-end my-3">
                {{ form(filteringTypeForm) }}
            </div>
            <a  id="add-post-item-button"
                href="{{ path('app_post_items_add', filter) }}"
                class="btn btn-outline-primary align-self-end my-3">
                <b>+</b> Add post item
            </a>
        </div>

        <div class="card">
            <div class="card-header d-flex">
                <div class="card-title h5 align-self-center">Post Items List</div>
                {% if filter.companyName is defined or filter.companyNumber is defined %}
                    <div class="ms-auto d-flex gap-3">
                        <div class="align-self-center">
                            <span id="selectedAmountLabel">
                                0 items selected
                            </span>
                        </div>
                        <div
                            class="align-self-center"
                            data-bs-toggle="tooltip"
                            data-bs-original-title="Please select one or more items."
                            data-bs-placement="right"
                        >
                            <button
                                id="bulkCollectButton"
                                type="button"
                                class="btn btn-primary"
                                data-bs-toggle="modal"
                                data-bs-target="#bulkCollectModal"
                                data-restricted-actions={{ blockedActions|json_encode|replace({"'":"&#39;"})|raw }}
                                disabled="true"
                                onclick="InjectModalValuesHelper.updateBulkCollectModal()"
                            >
                                Collect items
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="card-body p-0">
                {% block table %}

                    <table id="statusTable" class="table table-sm table-bordered table-hover table-striped align-middle">
                        <thead>
                        <tr>
                            {% if filter.companyName is defined or filter.companyNumber is defined %}
                                <th
                                    class="justify-content-center select-check-wrapper"
                                    data-bs-toggle="tooltip"
                                    data-bs-original-title="Select or unselect all items."
                                    data-bs-placement="left"
                                >
                                    <input class="form-check-input select-check-main p-2 mb-2" type="checkbox" value="">
                                </th>
                            {% endif %}
                            {{ column_header(filter, {0:'Batch ID', 1:'batchNumber'}, 'left') | raw }}
                            {{ column_header(filter, {0:'Company Number', 1:'companyNumber'}) | raw }}
                            {{ column_header(filter, {0:'Company Name', 1:'companyName'}, 'top', {0:'Sorting items in ', 1:'alphabetical', 2:'reverse alphabetical', 3:' order.'}) | raw }}
                            {{ column_header(filter, {0:'Type', 1:'type'}, 'top', {0:'Sorting items in ', 1:'alphabetical', 2:'reverse alphabetical', 3:' order.'}) | raw }}
                            {{ column_header(filter, {0:'Sender', 1:'sender'}, 'top', {0:'Sorting items in ', 1:'alphabetical', 2:'reverse alphabetical', 3:' order.'}) | raw }}
                            {{ column_header(filter, {0:'Added on', 1:'dtc'}, 'top', {0:'Sorting items from ', 1:'oldest to newest', 2:'newest to oldest', 3:'.'}, false) | raw }}
                            {{ column_header(filter, {0:'Status', 1:'status'}, 'top', {0:'Sorting items in ', 1:'alphabetical', 2:'reverse alphabetical', 3:' order.'}) | raw }}
                            {{ column_header(filter, {0:'Action', 1:'events'}, 'right', {0:'Sorting items from ', 1:'least to most', 2:'most to least', 3:' events.'}) | raw }}
                        </tr>
                        </thead>
                        <tbody>
                        {% for postItem in postItemList %}

                            <tr
                                    id="item-{{postItem.id}}"
                                    data-postItem='{{ postItem|json_encode|replace({"'":"&#39;"})|raw }}'
                            >
                                {% if filter.companyName is defined or filter.companyNumber is defined %}
                                    <th class="select-check-wrapper justify-content-center">
                                        <input class="form-check-input select-check p-2" type="checkbox" value="">
                                    </th>
                                {% endif %}
                                <td id="batch-number-cell" class="text-start ps-2">{{ postItem.batchNumber ?? 'N/A' }}</td>
                                <td id="company-number-cell" class="text-start ps-2">{{ postItem.companyNumber != '' ? postItem.companyNumber : 'N/A' }}</td>
                                <td id="company-name-cell" class="text-start ps-2">{{ postItem.companyName ?? 'N/A' }}</td>
                                <td id="type-cell" class="text-start ps-2">{{ postItem.type ?? 'N/A' }}</td>
                                <td id="sender-cell" class="text-start ps-2">{{ postItem.sender ?? 'N/A' }}</td>
                                <td id="dtc-cell" class="text-start ps-2">{{ postItem.dtc ? postItem.dtc|date('d/m/Y h:i') : 'N/A' }}</td>
                                <td id="status-cell" class="text-start ps-2">{{ postItem.parsedStatus ?? 'N/A' }}</td>
                                <td id="action-cell" class="text-start ps-2">
                                    <div class="dropend d-flex p-0 gap-3 justify-content-center">
                                        <button
                                            type="button"
                                            id="details-button"
                                            class="btn btn-secondary"
                                            data-bs-toggle="modal"
                                            data-bs-target="#itemDetailsModal"
                                            onclick="InjectModalValuesHelper.updateDetailModal('item-{{postItem.id}}')"
                                        >
                                            Details
                                        </button>
                                        {% if postItem.actionTaken == 'none' %}
                                            <button
                                                class="btn btn-primary dropdown-toggle"
                                                type="button"
                                                aria-expanded="false"
                                                data-bs-toggle="dropdown"
                                            >
                                        {% endif %}
                                        {% if postItem.actionTaken != 'none' %}
                                            <span
                                                class="d-inline-block"
                                                tabindex="0"
                                                data-bs-toggle="tooltip"
                                                data-bs-original-title="This post item has been {{ postItem.actionTaken }}. Please check the details."
                                                data-bs-placement="right"
                                            >
                                            <button
                                                class="btn btn-primary dropdown-toggle"
                                                type="button"
                                                disabled="disabled"
                                            >
                                        {% endif %}
                                                Action
                                            </button>
                                        {% if postItem.actionTaken != 'none' %}
                                            </span>
                                        {% endif %}
                                        <ul class="dropdown-menu shadow">
                                            {% if postItem.actionTaken == 'none' %}
                                                <li><a
                                                    href="{{ path('app_post_items_forward', filter|merge({'id': postItem.id}) ) }}"
                                                    class="dropdown-item link-primary"
                                                >
                                                    Post
                                                </a></li>
                                                <li><button
                                                        type="button"
                                                        class="dropdown-item link-success"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#collectModal"
                                                        onclick="InjectModalValuesHelper.updateCollectModal('item-{{postItem.id}}')"
                                                >
                                                    Collect
                                                </button></li>
                                                <li><button
                                                            type="button"
                                                            class="dropdown-item link-warning"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#rtsConfirmModal"
                                                            onclick="InjectModalValuesHelper.updateRTSModal('item-{{postItem.id}}')"
                                                    >
                                                        Return to Sender
                                                    </button></li>
                                                <li><button
                                                            type="button"
                                                            class="dropdown-item link-danger fw-bolder"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#securelyDestroyConfirmModal"
                                                            onclick="InjectModalValuesHelper.updateSecurelyDestroyModal('item-{{postItem.id}}')"
                                                    >
                                                        Securely Destroy
                                                    </button></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                {% endblock %}

                <section class="px-4 d-flex gap-3 align-items-start flex-row">
                    <span class=" mt-1 me-2">Number of items per page: </span>
                    {{ form(limitForm) }}
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item">
                                <a class="page-link" href="{{ first_page('app_post_items', filter) }}"
                                   aria-label="First Page">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {{ pages_before_current(3, {'filter': filter, 'url': 'app_post_items'}) | raw }}
                            <li class="page-item active"><a class="page-link" href="#">{{ filter.page }}</a></li>
                            {{ pages_after_current(totalPages, 3, {'filter': filter, 'url': 'app_post_items'}) | raw }}
                            <li class="page-item">
                                <a class="page-link" href="{{ last_page(totalPages, 'app_post_items', filter) }}"
                                   aria-label="Last Page">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <span class="align-items-end ms-auto mt-1">Showing {{ offset }} to {{ last_register_on_page }} of {{ total }} entries</span>
                </section>
            </div>
        </div>
    </main>
    <div id="footer"></div>
{% endblock %}