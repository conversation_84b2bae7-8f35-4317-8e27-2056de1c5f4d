<div class="modal fade" id="forwardConfirmModal">
    <div class="modal-dialog d-flex justify-content-center">
        <div class="modal-content" style="max-width: 60em; padding: 48px;">
            <div class="modal-body d-flex flex-column p-0 m-0">
                <div class="container-fluid container-xl p-0 m-0">
                    <p class="fs-5 m-0">
                        Log post item to be forwarded
                    </p>

                    <p style="margin-top: 48px; margin-bottom: 0px;">
                        You are about to forward the post item below:
                    </p>
                    
                    <div class="row p-0" style="margin-top: 24px;">
                        <div class="col-sm-6" style="padding-right: 24px;">
                            <div class="card h-100">
                                <h5 class="card-header">Item information</h5>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr class="align-middle">
                                            <th> Item ID </th> <td>{{ postItem.id ?? "N/A" }}</td>
                                        </tr>
                                        <tr class="align-middle">
                                            <th> Batch ID </th> <td>{{ postItem.batchNumber ?? "N/A" }}</td>
                                        </tr>
                                        <tr class="align-middle">
                                            <th> Company Number </th> <td>{{ postItem.companyNumber ?? "N/A" }}  </td>
                                        </tr>
                                        <tr class="align-middle">
                                            <th> Company Name </th> <td>{{ postItem.companyName ?? "N/A" }}</td>
                                        </tr>
                                        <tr class="align-middle">
                                            <th> Type </th> <td>{{ postItem.type ?? "N/A" }}</td>
                                        </tr>
                                        <tr class="align-middle">
                                            <th> Sender </th> <td>{{ postItem.sender ?? "N/A" }}</td>
                                        </tr>
                                        </tr class="align-middle">
                                            <th> Added on </th> <td>{{ postItem.dtc ? postItem.dtc|date('d/m/Y h:i') : "N/A" }}</td>
                                        </tr class="align-middle">
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 ps-0">
                            <div class="card">
                                <h5 class="card-header">Forward address</h5>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        {% if postItem.address.address1 is defined and postItem.address.address1 is not empty %}
                                            {{ postItem.address.address1 }} <br/>
                                        {% endif %}

                                        {% if postItem.address.address2 is defined and postItem.address.address2 is not empty %}
                                            {{ postItem.address.address2 }} <br/>
                                        {% endif %}

                                        {% if postItem.address.address3 is defined and postItem.address.address3 is not empty %}
                                            {{ postItem.address.address3 }} <br/>
                                        {% endif %}

                                        {% if postItem.address.postcode is defined and postItem.address.postcode is not empty %}
                                            {{ postItem.address.postcode }} <br/>
                                        {% endif %}

                                        {% if postItem.address.town is defined and postItem.address.town is not empty %}
                                            {{ postItem.address.town }} <br/>
                                        {% endif %}

                                        {% if postItem.address.country is defined and postItem.address.country is not empty %}
                                            {{ postItem.address.country }}
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                            <div class="card" style="margin-top: 24px">
                                <h5 class="card-header">Selected options</h5>
                                <div class="card-body">
                                    <table class="table table-sm" id="fwdFormDataTable">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end p-0" style="margin-top: 48px">
                        <button
                            type="button"
                            class="btn btn-secondary"
                            style="min-width: 6em; margin-right: 16px;"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                        >
                            Cancel
                        </button>
                        {{ form_row(form.save, { 'attr' : { 'data-bs-dismiss': 'modal'} } ) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
