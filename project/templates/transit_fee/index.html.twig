{% extends 'base.html.twig' %}

{% block styles %}
    <link href="{{ version_asset('/css/chunk-vendors.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/app.css') }}" rel="stylesheet">
    <link href="{{ version_asset('/css/custom.css') }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
    <script src="{{ version_asset('/js/chunk-vendors.js') }}" defer="defer"></script>
    <script src="{{ version_asset('/js/app.js') }}" defer="defer"></script>
{% endblock %}

{% block body %}
    <div id="header"></div>
    <main class="container-fluid container-xl my-5">
        <h1 class="mb-4">Royal Mail Fee Management</h1>

        {% for message in app.flashes('success') %}
            <div class="alert alert-success">
                {{ message }}
            </div>
        {% endfor %}

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger">
                {{ message }}
            </div>
        {% endfor %}

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Royal Mail Fees</h5>
            </div>
            <div class="card-body">
                {{ form_start(form, {'attr': {'novalidate': 'novalidate'}}) }}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Weight Range</th>
                                    <th>UK (£)</th>
                                    <th>Europe (£)</th>
                                    <th>World (£)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transitFee in transitFees %}
                                    <tr>
                                        <td>
                                            <strong>{{ transitFee.feeName|replace({'_': ' to '})|capitalize }} grams</strong>
                                        </td>
                                        <td>
                                            {{ form_widget(form['uk_' ~ transitFee.id]) }}
                                        </td>
                                        <td>
                                            {{ form_widget(form['europe_' ~ transitFee.id]) }}
                                        </td>
                                        <td>
                                            {{ form_widget(form['world_' ~ transitFee.id]) }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-end mt-3">
                        {{ form_widget(form.save) }}
                    </div>
                {{ form_end(form) }}
            </div>
        </div>
    </main>
    <div id="footer"></div>
{% endblock %}
