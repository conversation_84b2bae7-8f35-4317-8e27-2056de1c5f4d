<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250217133640 extends AbstractMigration
{
    private const STATUSES = [
        'added',
        'waiting_payment',
        'to_be_forwarded',
        'to_be_rts-d',
        'to_be_collected',
        'to_be_securely_destroyed',
        'forwarded',
        'rts-d',
        'collected',
        'securely_destroyed',
    ];

    public function getDescription(): string
    {
        return 'Adds the status column to the post_items table with "added" as the default value';
    }

    public function up(Schema $schema): void
    {
        $statuses = implode("', '", self::STATUSES);
        $this->addSql("ALTER TABLE post_items ADD status ENUM('$statuses') DEFAULT 'added' NOT NULL");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE post_items DROP COLUMN status');
    }
}