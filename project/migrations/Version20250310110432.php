<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250310110432 extends AbstractMigration
{
    private const STATUSES = [
        'added',
        'scan_only',
        'waiting_payment',
        'to_be_forwarded',
        'to_be_rts-d',
        'to_be_collected',
        'to_be_securely_destroyed',
        'forwarded',
        'rts-d',
        'collected',
        'securely_destroyed',
    ];

    private const OLD_STATUSES = [
        'added',
        'waiting_payment',
        'to_be_forwarded',
        'to_be_rts-d',
        'to_be_collected',
        'to_be_securely_destroyed',
        'forwarded',
        'rts-d',
        'collected',
        'securely_destroyed',
    ];

    public function getDescription(): string
    {
        return 'Update the status column to the post_items table with the additional status "scan_only"';
    }

    public function up(Schema $schema): void
    {
        $statuses = implode("', '", self::STATUSES);
        $this->addSql(
            "ALTER TABLE post_items MODIFY COLUMN status ENUM('$statuses') DEFAULT 'added' NOT NULL"
        );
    }

    public function down(Schema $schema): void
    {
        $statuses = implode("', '", self::OLD_STATUSES);
        $this->addSql(
            "ALTER TABLE post_items MODIFY COLUMN status ENUM('$statuses') DEFAULT 'added' NOT NULL"
        );
    }
}