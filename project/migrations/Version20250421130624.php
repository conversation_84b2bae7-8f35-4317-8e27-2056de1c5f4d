<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250421130624 extends AbstractMigration
{
    private const FEE_VALUES = [
        '0_100'     => ['uk' => 1.9, 'europe' => 3.05, 'world' => 5.18],
        '101_250'   => ['uk' => 2.7, 'europe' => 4.55, 'world' => 8.55],
        '251_500'   => ['uk' => 3.5, 'europe' => 5.65, 'world' => 12.5],
        '501_750'   => ['uk' => 4.0, 'europe' => 6.75, 'world' => 17.3],
        '751_1000'  => ['uk' => 5.2, 'europe' => 15.55, 'world' => 30.26],
        '1001_2000' => ['uk' => 6.0, 'europe' => 20.25, 'world' => 39.91],
    ];

    public function getDescription(): string
    {
        return 'Creates a table to store the Royal Mail fees per weight and destination';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<SQL
            CREATE TABLE `transit_fees` (
              `id` int NOT NULL AUTO_INCREMENT,
              `fee_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
              `uk` float DEFAULT NULL,
              `europe` float DEFAULT NULL,
              `world` float DEFAULT NULL,
              PRIMARY KEY (`id`)
            )
            ENGINE=InnoDB
            AUTO_INCREMENT=7
            DEFAULT CHARSET=utf8mb4
            ;
        SQL);

        foreach (self::FEE_VALUES as $feeName => $values) {
            $this->addSql(<<<SQL
                INSERT INTO `transit_fees` (`fee_name`, `uk`, `europe`, `world`)
                VALUES ('$feeName', {$values['uk']}, {$values['europe']}, {$values['world']});
            SQL);
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `transit_fees`');
    }
}