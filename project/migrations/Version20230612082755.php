<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230612082755 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'CREATE TABLE status_messages(
                id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
                data TEXT DEFAULT NULL,
                operator VARCHAR(255) NOT NULL,
                dtm DATETIME NOT NULL,
                dtc DATETIME NOT NULL,
                PRIMARY KEY(id)
            )
            DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE status_messages');
    }
}
