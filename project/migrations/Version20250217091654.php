<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250217091654 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Adds the transaction_id column to the post_items table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE post_items ADD transaction_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Revert the migration if needed
        $this->addSql('ALTER TABLE post_items DROP COLUMN transaction_id');
    }
}