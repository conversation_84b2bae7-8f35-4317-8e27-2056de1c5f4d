<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250610170320 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE post_items MODIFY sender enum(\'HMRC\', \'OTHER\', \'COMPANIES_HOUSE\', \'COURT_LETTER\')');
    }

    public function down(Schema $schema): void
    {

        $this->addSql('ALTER TABLE post_items MODIFY sender enum(\'HMRC\', \'OTHER\', \'COMPANIES_HOUSE\')');
    }
}
