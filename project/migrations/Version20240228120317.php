<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240228120317 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'CREATE TABLE ai_performance_reports(
                id INT AUTO_INCREMENT NOT NULL,
                period VARCHAR(255) DEFAULT NULL,
                total_matched INT DEFAULT NULL,
                total_correctly_matched INT DEFAULT NULL,
                total_prediction_score FLOAT DEFAULT NULL,
                total_predicted_guessed_similarity FLOAT DEFAULT NULL,
                total_predicted_matched_similarity FLOAT DEFAULT NULL,
                total_guessed_matched_similarity FLOAT DEFAULT NULL,
                ch_matched INT DEFAULT NULL,
                ch_correctly_matched INT DEFAULT NULL,
                ch_prediction_score FLOAT DEFAULT NULL,
                ch_predicted_guessed_similarity FLOAT DEFAULT NULL,
                ch_predicted_matched_similarity FLOAT DEFAULT NULL,
                ch_guessed_matched_similarity FLOAT DEFAULT NULL,
                hmrc_matched INT DEFAULT NULL,
                hmrc_correctly_matched INT DEFAULT NULL,
                hmrc_prediction_score FLOAT DEFAULT NULL,
                hmrc_predicted_guessed_similarity FLOAT DEFAULT NULL,
                hmrc_predicted_matched_similarity FLOAT DEFAULT NULL,
                hmrc_guessed_matched_similarity FLOAT DEFAULT NULL,
                other_matched INT DEFAULT NULL,
                other_correctly_matched INT DEFAULT NULL,
                other_prediction_score FLOAT DEFAULT NULL,
                other_predicted_guessed_similarity FLOAT DEFAULT NULL,
                other_predicted_matched_similarity FLOAT DEFAULT NULL,
                other_guessed_matched_similarity FLOAT DEFAULT NULL,
                non_statutory_matched INT DEFAULT NULL,
                non_statutory_correctly_matched INT DEFAULT NULL,
                non_statutory_prediction_score FLOAT DEFAULT NULL,
                non_statutory_predicted_guessed_similarity FLOAT DEFAULT NULL,
                non_statutory_predicted_matched_similarity FLOAT DEFAULT NULL,
                non_statutory_guessed_matched_similarity FLOAT DEFAULT NULL,
                dtm DATETIME NOT NULL,
                dtc DATETIME NOT NULL,
                PRIMARY KEY(id)
            )
            DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE ai_performance_reports');
    }
}
