<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221102135210 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE post_items (id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', company_number VARCHAR(255) NOT NULL, company_name VARCHAR(255) NOT NULL, type VARCHAR(255) DEFAULT NULL, sender enum(\'HRMC\', \'OTHER\', \'COMPANIES_HOUSE\'), file_name VARCHAR(255) NOT NULL, batch_number VARCHAR(255) DEFAULT NULL, operator VARCHAR(255) NOT NULL, dtm DATETIME NOT NULL, dtc DATETIME NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE post_items_details (id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', post_items_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', detail_key VARCHAR(255) NOT NULL, detail_value VARCHAR(255) NOT NULL, dtc DATETIME NOT NULL, dtm DATETIME DEFAULT NULL, INDEX IDX_11D7ED98D45D2725 (post_items_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE post_items_events_log (id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', post_items_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', event_name VARCHAR(255) NOT NULL, operator VARCHAR(255) NOT NULL, dtc DATETIME NOT NULL, dtm DATETIME DEFAULT NULL, INDEX IDX_4921E0CD45D2725 (post_items_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE post_items_details ADD CONSTRAINT FK_11D7ED98D45D2725 FOREIGN KEY (post_items_id) REFERENCES post_items (id)');
        $this->addSql('ALTER TABLE post_items_events_log ADD CONSTRAINT FK_4921E0CD45D2725 FOREIGN KEY (post_items_id) REFERENCES post_items (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE post_items_details DROP FOREIGN KEY FK_11D7ED98D45D2725');
        $this->addSql('ALTER TABLE post_items_events_log DROP FOREIGN KEY FK_4921E0CD45D2725');
        $this->addSql('DROP TABLE post_items');
        $this->addSql('DROP TABLE post_items_details');
        $this->addSql('DROP TABLE post_items_events_log');
    }
}
