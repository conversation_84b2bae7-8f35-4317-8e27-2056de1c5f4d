<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230130183626 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("UPDATE post_items SET sender = SUBSTR(sender, 1, 255) WHERE LENGTH(sender) > 255");
        $this->addSql('UPDATE post_items SET sender = \'HMRC\' WHERE sender = \'HRMC\'');
        $this->addSql('ALTER TABLE post_items MODIFY sender enum(\'HMRC\', \'OTHER\', \'COMPANIES_HOUSE\')');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE post_items MODIFY sender VARCHAR(255)');
        $this->addSql("UPDATE post_items SET sender = SUBSTR(sender, 1, 255) WHERE LENGTH(sender) > 255");
        $this->addSql('UPDATE post_items SET sender = \'HRMC\' WHERE sender = \'HMRC\'');
    }
}
