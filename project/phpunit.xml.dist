<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="bin/.phpunit/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="tests/bootstrap.php"
>
    <php>
        <ini name="error_reporting" value="-1" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <server name="SYMFONY_PHPUNIT_REMOVE" value="" />
        <server name="SYMFONY_PHPUNIT_VERSION" value="7.5" />

        <!-- ###+ google/apiclient ### -->
        <env name="GOOGLE_API_KEY" value=""/>
        <env name="GOOGLE_CLIENT_ID" value=""/>
        <env name="GOOGLE_CLIENT_SECRET" value=""/>
        <env name="GOOGLE_AUTH_CONFIG" value="%kernel.project_dir%/path/to/file.json"/>
        <!-- ###- google/apiclient ### -->

        <!-- ###+ sentry/sentry-symfony ### -->
        <env name="SENTRY_DSN" value=""/>
        <!-- ###- sentry/sentry-symfony ### -->
    </php>

    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
        </whitelist>
    </filter>

    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener" />
    </listeners>
</phpunit>
