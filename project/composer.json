{"type": "project", "license": "proprietary", "require": {"php": "^8.0", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "composer/package-versions-deprecated": "1.11.99.1", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.7", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.13", "firebase/php-jwt": "6.1.*", "google/apiclient": "^2.0", "google/cloud": "^0.133.0", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.4", "ramsey/uuid-doctrine": "^1.8", "sensio/framework-extra-bundle": "^6.2", "sentry/sentry-symfony": "^4.8", "symfony/asset": "6.0.*", "symfony/console": "6.0.*", "symfony/dotenv": "6.0.*", "symfony/expression-language": "6.0.*", "symfony/flex": "^1.18.7", "symfony/form": "6.0.*", "symfony/framework-bundle": "6.0.*", "symfony/http-client": "6.0.*", "symfony/intl": "6.0.*", "symfony/mailer": "6.0.*", "symfony/monolog-bundle": "^3.1", "symfony/notifier": "6.0.*", "symfony/process": "6.0.*", "symfony/property-access": "6.0.*", "symfony/property-info": "6.0.*", "symfony/proxy-manager-bridge": "6.0.*", "symfony/security-bundle": "6.0.*", "symfony/serializer": "6.0.*", "symfony/string": "6.0.*", "symfony/translation": "6.0.*", "symfony/twig-bundle": "6.0.*", "symfony/uid": "6.0.*", "symfony/validator": "6.0.*", "symfony/web-link": "6.0.*", "symfony/yaml": "6.0.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.0.*", "symfony/css-selector": "6.0.*", "symfony/debug-bundle": "6.0.*", "symfony/maker-bundle": "^1.47", "symfony/phpunit-bridge": "^6.0", "symfony/stopwatch": "6.0.*", "symfony/web-profiler-bundle": "6.0.*"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.0.*"}}}