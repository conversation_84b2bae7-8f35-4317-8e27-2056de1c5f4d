{"brick/math": {"version": "0.8.15"}, "composer/package-versions-deprecated": {"version": "*********"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "2.2.0"}, "doctrine/collections": {"version": "1.8.0"}, "doctrine/common": {"version": "3.4.3"}, "doctrine/dbal": {"version": "3.4.5"}, "doctrine/deprecations": {"version": "v1.0.0"}, "doctrine/doctrine-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "da713d006953b90d1c085c1be480ecdd6c4a95e0"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.2.0"}, "doctrine/inflector": {"version": "1.4.2"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.5.2"}, "doctrine/orm": {"version": "2.13.3"}, "doctrine/persistence": {"version": "3.0.4"}, "doctrine/sql-formatter": {"version": "1.1.3"}, "egulias/email-validator": {"version": "2.1.17"}, "firebase/php-jwt": {"version": "v5.2.0"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.12"}, "google/apiclient": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.10", "ref": "07a97ec434d43b7903c78069a04c92adb6442e52"}, "files": ["config/packages/google_apiclient.yaml"]}, "google/apiclient-services": {"version": "v0.335.0"}, "google/auth": {"version": "v1.9.0"}, "google/cloud": {"version": "v0.133.0"}, "google/common-protos": {"version": "1.2"}, "google/crc32": {"version": "v0.1.0"}, "google/gax": {"version": "1.3.0"}, "google/grpc-gcp": {"version": "0.1.5"}, "google/protobuf": {"version": "v3.12.2"}, "grpc/grpc": {"version": "1.27.0"}, "guzzlehttp/guzzle": {"version": "6.5.4"}, "guzzlehttp/promises": {"version": "v1.3.1"}, "guzzlehttp/psr7": {"version": "1.6.1"}, "laminas/laminas-code": {"version": "4.7.0"}, "monolog/monolog": {"version": "2.1.0"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nikic/php-parser": {"version": "v4.4.0"}, "paragonie/constant_time_encoding": {"version": "v2.6.3"}, "phar-io/manifest": {"version": "2.0.1"}, "phar-io/version": {"version": "3.1.0"}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpdocumentor/reflection-common": {"version": "2.1.0"}, "phpdocumentor/reflection-docblock": {"version": "5.1.0"}, "phpdocumentor/type-resolver": {"version": "1.1.0"}, "phpseclib/phpseclib": {"version": "3.0.35"}, "phpspec/prophecy": {"version": "v1.15.0"}, "phpstan/phpdoc-parser": {"version": "1.4.3"}, "phpunit/php-code-coverage": {"version": "9.2.6"}, "phpunit/php-file-iterator": {"version": "3.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.3"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/collection": {"version": "1.0.1"}, "ramsey/uuid": {"version": "4.0.1"}, "ramsey/uuid-doctrine": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "471aed0fbf5620b8d7f92b7a5ebbbf6c0945c27a"}}, "rize/uri-template": {"version": "0.3.2"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.3"}, "sebastian/global-state": {"version": "5.0.3"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/resource-operations": {"version": "3.0.3"}, "sebastian/type": {"version": "2.3.4"}, "sebastian/version": {"version": "3.0.2"}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sentry/sentry-symfony": {"version": "4.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.6", "ref": "3cbed34d60ded4d22d10b43cd08bb463a6f4ce26"}, "files": ["config/packages/sentry.yaml"]}, "symfony/asset": {"version": "v5.0.8"}, "symfony/browser-kit": {"version": "v5.0.8"}, "symfony/cache": {"version": "v5.0.8"}, "symfony/cache-contracts": {"version": "v2.0.1"}, "symfony/config": {"version": "v5.0.8"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "ea8c0eda34fda57e7d5cd8cbd889e2a387e3472c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/css-selector": {"version": "v5.0.8"}, "symfony/debug-bundle": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "f8863cbad2f2e58c4b65fa1eac892ab189971bea"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.0.8"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v6.0.14"}, "symfony/dom-crawler": {"version": "v5.0.8"}, "symfony/dotenv": {"version": "v5.0.8"}, "symfony/error-handler": {"version": "v5.0.8"}, "symfony/event-dispatcher": {"version": "v5.0.8"}, "symfony/event-dispatcher-contracts": {"version": "v2.0.1"}, "symfony/expression-language": {"version": "v5.0.8"}, "symfony/filesystem": {"version": "v5.0.8"}, "symfony/finder": {"version": "v5.0.8"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v5.0.8"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "36d3075b2b8e0c4de0e82356a86e4c4a4eb6681b"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.0.8"}, "symfony/http-client-contracts": {"version": "v2.1.1"}, "symfony/http-foundation": {"version": "v5.0.8"}, "symfony/http-kernel": {"version": "v5.0.8"}, "symfony/intl": {"version": "v5.0.8"}, "symfony/mailer": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "15658c2a0176cda2e7dba66276a2030b52bd81b2"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v5.0.8"}, "symfony/monolog-bridge": {"version": "v5.0.8"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "a89f4cd8a232563707418eea6c2da36acd36a917"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/notifier": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v5.0.8"}, "symfony/password-hasher": {"version": "v6.0.3"}, "symfony/phpunit-bridge": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "6d0e35f749d5f4bfe1f011762875275cd3f9874f"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.17.0"}, "symfony/polyfill-intl-icu": {"version": "v1.17.0"}, "symfony/polyfill-intl-idn": {"version": "v1.17.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.17.0"}, "symfony/polyfill-mbstring": {"version": "v1.17.0"}, "symfony/polyfill-php80": {"version": "v1.26.0"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/polyfill-uuid": {"version": "v1.26.0"}, "symfony/process": {"version": "v5.0.8"}, "symfony/property-access": {"version": "v5.0.8"}, "symfony/property-info": {"version": "v5.0.8"}, "symfony/proxy-manager-bridge": {"version": "v6.0.6"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.0.8"}, "symfony/security-csrf": {"version": "v5.0.8"}, "symfony/security-http": {"version": "v5.0.8"}, "symfony/serializer": {"version": "v5.0.8"}, "symfony/service-contracts": {"version": "v2.0.1"}, "symfony/stopwatch": {"version": "v5.0.8"}, "symfony/string": {"version": "v5.0.8"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.0.1"}, "symfony/twig-bridge": {"version": "v5.0.8"}, "symfony/twig-bundle": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "fab9149bbaa4d5eca054ed93f9e1b66cc500895d"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "v6.0.13"}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.0.8"}, "symfony/var-exporter": {"version": "v5.0.8"}, "symfony/web-link": {"version": "v5.0.8"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/yaml": {"version": "v5.0.8"}, "theseer/tokenizer": {"version": "1.2.0"}, "twig/extra-bundle": {"version": "v3.0.3"}, "twig/twig": {"version": "v3.0.3"}, "webmozart/assert": {"version": "1.8.0"}}