<?php

namespace App\Entity;

use App\Repository\AiPerformanceReportRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: AiPerformanceReportRepository::class)]
#[ORM\Table(name: "ai_performance_reports")]
class AiPerformanceReport
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $period = null;

    // Total values
    #[ORM\Column(nullable: true)]
    private ?int $totalMatched = null;

    #[ORM\Column(nullable: true)]
    private ?int $totalCorrectlyMatched = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalPredictionScore = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalPredictedGuessedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalPredictedMatchedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalGuessedMatchedSimilarity = null;

    // Companies House values
    #[ORM\Column(nullable: true)]
    private ?int $chMatched = null;

    #[ORM\Column(nullable: true)]
    private ?int $chCorrectlyMatched = null;

    #[ORM\Column(nullable: true)]
    private ?float $chPredictionScore = null;

    #[ORM\Column(nullable: true)]
    private ?float $chPredictedGuessedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $chPredictedMatchedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $chGuessedMatchedSimilarity = null;

    // HMRC values
    #[ORM\Column(nullable: true)]
    private ?int $hmrcMatched = null;

    #[ORM\Column(nullable: true)]
    private ?int $hmrcCorrectlyMatched = null;

    #[ORM\Column(nullable: true)]
    private ?float $hmrcPredictionScore = null;

    #[ORM\Column(nullable: true)]
    private ?float $hmrcPredictedGuessedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $hmrcPredictedMatchedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $hmrcGuessedMatchedSimilarity = null;

    // Other values
    #[ORM\Column(nullable: true)]
    private ?int $otherMatched = null;

    #[ORM\Column(nullable: true)]
    private ?int $otherCorrectlyMatched = null;

    #[ORM\Column(nullable: true)]
    private ?float $otherPredictionScore = null;

    #[ORM\Column(nullable: true)]
    private ?float $otherPredictedGuessedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $otherPredictedMatchedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $otherGuessedMatchedSimilarity = null;

    // Non-statutory values
    #[ORM\Column(nullable: true)]
    private ?int $nonStatutoryMatched = null;

    #[ORM\Column(nullable: true)]
    private ?int $nonStatutoryCorrectlyMatched = null;

    #[ORM\Column(nullable: true)]
    private ?float $nonStatutoryPredictionScore = null;

    #[ORM\Column(nullable: true)]
    private ?float $nonStatutoryPredictedGuessedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $nonStatutoryPredictedMatchedSimilarity = null;

    #[ORM\Column(nullable: true)]
    private ?float $nonStatutoryGuessedMatchedSimilarity = null;

    
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtc = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtm = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getPeriod(): ?string
    {
        return $this->period;
    }

    public function setPeriod(?string $period): self
    {
        $this->period = $period;

        return $this;
    }

    // Total getters and setters
    public function getTotalMatched(): ?int
    {
        return $this->totalMatched;
    }

    public function setTotalMatched(?int $totalMatched): self
    {
        $this->totalMatched = $totalMatched;

        return $this;
    }

    public function getTotalCorrectlyMatched(): ?int
    {
        return $this->totalCorrectlyMatched;
    }

    public function setTotalCorrectlyMatched(?int $totalCorrectlyMatched): self
    {
        $this->totalCorrectlyMatched = $totalCorrectlyMatched;

        return $this;
    }
    
    public function getTotalPredictionScore(): ?float
    {
        return $this->totalPredictionScore;
    }

    public function setTotalPredictionScore(?float $totalPredictionScore): self
    {
        $this->totalPredictionScore = $totalPredictionScore;

        return $this;
    }

    public function getTotalPredictedGuessedSimilarity(): ?float
    {
        return $this->totalPredictedGuessedSimilarity;
    }

    public function setTotalPredictedGuessedSimilarity(?float $totalPredictedGuessedSimilarity): self
    {
        $this->totalPredictedGuessedSimilarity = $totalPredictedGuessedSimilarity;

        return $this;
    }

    public function getTotalPredictedMatchedSimilarity(): ?float
    {
        return $this->totalPredictedMatchedSimilarity;
    }

    public function setTotalPredictedMatchedSimilarity(?float $totalPredictedMatchedSimilarity): self
    {
        $this->totalPredictedMatchedSimilarity = $totalPredictedMatchedSimilarity;

        return $this;
    }

    public function getTotalGuessedMatchedSimilarity(): ?float
    {
        return $this->totalGuessedMatchedSimilarity;
    }

    public function setTotalGuessedMatchedSimilarity(?float $totalGuessedMatchedSimilarity): self
    {
        $this->totalGuessedMatchedSimilarity = $totalGuessedMatchedSimilarity;

        return $this;
    }

    // Companies House getters and setters
    public function getChMatched(): ?int
    {
        return $this->chMatched;
    }

    public function setChMatched(?int $chMatched): self
    {
        $this->chMatched = $chMatched;

        return $this;
    }

    public function getChCorrectlyMatched(): ?int
    {
        return $this->chCorrectlyMatched;
    }

    public function setChCorrectlyMatched(?int $chCorrectlyMatched): self
    {
        $this->chCorrectlyMatched = $chCorrectlyMatched;

        return $this;
    }
    
    public function getChPredictionScore(): ?float
    {
        return $this->chPredictionScore;
    }

    public function setChPredictionScore(?float $chPredictionScore): self
    {
        $this->chPredictionScore = $chPredictionScore;

        return $this;
    }

    public function getChPredictedGuessedSimilarity(): ?float
    {
        return $this->chPredictedGuessedSimilarity;
    }

    public function setChPredictedGuessedSimilarity(?float $chPredictedGuessedSimilarity): self
    {
        $this->chPredictedGuessedSimilarity = $chPredictedGuessedSimilarity;

        return $this;
    }

    public function getChPredictedMatchedSimilarity(): ?float
    {
        return $this->chPredictedMatchedSimilarity;
    }

    public function setChPredictedMatchedSimilarity(?float $chPredictedMatchedSimilarity): self
    {
        $this->chPredictedMatchedSimilarity = $chPredictedMatchedSimilarity;

        return $this;
    }

    public function getChGuessedMatchedSimilarity(): ?float
    {
        return $this->chGuessedMatchedSimilarity;
    }

    public function setChGuessedMatchedSimilarity(?float $chGuessedMatchedSimilarity): self
    {
        $this->chGuessedMatchedSimilarity = $chGuessedMatchedSimilarity;

        return $this;
    }

    // HMRC getters and setters
    public function getHmrcMatched(): ?int
    {
        return $this->hmrcMatched;
    }

    public function setHmrcMatched(?int $hmrcMatched): self
    {
        $this->hmrcMatched = $hmrcMatched;

        return $this;
    }

    public function getHmrcCorrectlyMatched(): ?int
    {
        return $this->hmrcCorrectlyMatched;
    }

    public function setHmrcCorrectlyMatched(?int $hmrcCorrectlyMatched): self
    {
        $this->hmrcCorrectlyMatched = $hmrcCorrectlyMatched;

        return $this;
    }
    
    public function getHmrcPredictionScore(): ?float
    {
        return $this->hmrcPredictionScore;
    }

    public function setHmrcPredictionScore(?float $hmrcPredictionScore): self
    {
        $this->hmrcPredictionScore = $hmrcPredictionScore;

        return $this;
    }

    public function getHmrcPredictedGuessedSimilarity(): ?float
    {
        return $this->hmrcPredictedGuessedSimilarity;
    }

    public function setHmrcPredictedGuessedSimilarity(?float $hmrcPredictedGuessedSimilarity): self
    {
        $this->hmrcPredictedGuessedSimilarity = $hmrcPredictedGuessedSimilarity;

        return $this;
    }

    public function getHmrcPredictedMatchedSimilarity(): ?float
    {
        return $this->hmrcPredictedMatchedSimilarity;
    }

    public function setHmrcPredictedMatchedSimilarity(?float $hmrcPredictedMatchedSimilarity): self
    {
        $this->hmrcPredictedMatchedSimilarity = $hmrcPredictedMatchedSimilarity;

        return $this;
    }

    public function getHmrcGuessedMatchedSimilarity(): ?float
    {
        return $this->hmrcGuessedMatchedSimilarity;
    }

    public function setHmrcGuessedMatchedSimilarity(?float $hmrcGuessedMatchedSimilarity): self
    {
        $this->hmrcGuessedMatchedSimilarity = $hmrcGuessedMatchedSimilarity;

        return $this;
    }

    // Other getters and setters
    public function getOtherMatched(): ?int
    {
        return $this->otherMatched;
    }

    public function setOtherMatched(?int $otherMatched): self
    {
        $this->otherMatched = $otherMatched;

        return $this;
    }

    public function getOtherCorrectlyMatched(): ?int
    {
        return $this->otherCorrectlyMatched;
    }

    public function setOtherCorrectlyMatched(?int $otherCorrectlyMatched): self
    {
        $this->otherCorrectlyMatched = $otherCorrectlyMatched;

        return $this;
    }
    
    public function getOtherPredictionScore(): ?float
    {
        return $this->otherPredictionScore;
    }

    public function setOtherPredictionScore(?float $otherPredictionScore): self
    {
        $this->otherPredictionScore = $otherPredictionScore;

        return $this;
    }

    public function getOtherPredictedGuessedSimilarity(): ?float
    {
        return $this->otherPredictedGuessedSimilarity;
    }

    public function setOtherPredictedGuessedSimilarity(?float $otherPredictedGuessedSimilarity): self
    {
        $this->otherPredictedGuessedSimilarity = $otherPredictedGuessedSimilarity;

        return $this;
    }

    public function getOtherPredictedMatchedSimilarity(): ?float
    {
        return $this->otherPredictedMatchedSimilarity;
    }

    public function setOtherPredictedMatchedSimilarity(?float $otherPredictedMatchedSimilarity): self
    {
        $this->otherPredictedMatchedSimilarity = $otherPredictedMatchedSimilarity;

        return $this;
    }

    public function getOtherGuessedMatchedSimilarity(): ?float
    {
        return $this->otherGuessedMatchedSimilarity;
    }

    public function setOtherGuessedMatchedSimilarity(?float $otherGuessedMatchedSimilarity): self
    {
        $this->otherGuessedMatchedSimilarity = $otherGuessedMatchedSimilarity;

        return $this;
    }

    // Non-statutory getters and setters
    public function getNonStatutoryMatched(): ?int
    {
        return $this->nonStatutoryMatched;
    }

    public function setNonStatutoryMatched(?int $nonStatutoryMatched): self
    {
        $this->nonStatutoryMatched = $nonStatutoryMatched;

        return $this;
    }

    public function getNonStatutoryCorrectlyMatched(): ?int
    {
        return $this->nonStatutoryCorrectlyMatched;
    }

    public function setNonStatutoryCorrectlyMatched(?int $nonStatutoryCorrectlyMatched): self
    {
        $this->nonStatutoryCorrectlyMatched = $nonStatutoryCorrectlyMatched;

        return $this;
    }
    
    public function getNonStatutoryPredictionScore(): ?float
    {
        return $this->nonStatutoryPredictionScore;
    }

    public function setNonStatutoryPredictionScore(?float $nonStatutoryPredictionScore): self
    {
        $this->nonStatutoryPredictionScore = $nonStatutoryPredictionScore;

        return $this;
    }

    public function getNonStatutoryPredictedGuessedSimilarity(): ?float
    {
        return $this->nonStatutoryPredictedGuessedSimilarity;
    }

    public function setNonStatutoryPredictedGuessedSimilarity(?float $nonStatutoryPredictedGuessedSimilarity): self
    {
        $this->nonStatutoryPredictedGuessedSimilarity = $nonStatutoryPredictedGuessedSimilarity;

        return $this;
    }

    public function getNonStatutoryPredictedMatchedSimilarity(): ?float
    {
        return $this->nonStatutoryPredictedMatchedSimilarity;
    }

    public function setNonStatutoryPredictedMatchedSimilarity(?float $nonStatutoryPredictedMatchedSimilarity): self
    {
        $this->nonStatutoryPredictedMatchedSimilarity = $nonStatutoryPredictedMatchedSimilarity;

        return $this;
    }

    public function getNonStatutoryGuessedMatchedSimilarity(): ?float
    {
        return $this->nonStatutoryGuessedMatchedSimilarity;
    }

    public function setNonStatutoryGuessedMatchedSimilarity(?float $nonStatutoryGuessedMatchedSimilarity): self
    {
        $this->nonStatutoryGuessedMatchedSimilarity = $nonStatutoryGuessedMatchedSimilarity;

        return $this;
    }


    public function getDtc(): ?\DateTimeInterface
    {
        return $this->dtc;
    }

    public function setDtc(\DateTimeInterface $dtc): self
    {
        $this->dtc = $dtc;

        return $this;
    }

    public function getDtm(): ?\DateTimeInterface
    {
        return $this->dtm;
    }

    public function setDtm(\DateTimeInterface $dtm): self
    {
        $this->dtm = $dtm;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'report_id' => $this->getId(),
            'period' => $this->getPeriod(),
            'total' => [
                'matched' => $this->getTotalMatched(),
                'correctly_matched' => $this->getTotalCorrectlyMatched(),
                'scores' => [
                    'prediction_score' => $this->getTotalPredictionScore(),
                    'predicted_guessed_similarity' => $this->getTotalPredictedGuessedSimilarity(),
                    'predicted_matched_similarity' => $this->getTotalPredictedMatchedSimilarity(),
                    'guessed_matched_similarity' => $this->getTotalGuessedMatchedSimilarity(),
                ],
            ],
            'companies_house' => [
                'matched' => $this->getChMatched(),
                'correctly_matched' => $this->getChCorrectlyMatched(),
                'scores' => [
                    'prediction_score' => $this->getChPredictionScore(),
                    'predicted_guessed_similarity' => $this->getChPredictedGuessedSimilarity(),
                    'predicted_matched_similarity' => $this->getChPredictedMatchedSimilarity(),
                    'guessed_matched_similarity' => $this->getChGuessedMatchedSimilarity(),
                ],
            ],
            'hmrc' => [
                'matched' => $this->getHmrcMatched(),
                'correctly_matched' => $this->getHmrcCorrectlyMatched(),
                'scores' => [
                    'prediction_score' => $this->getHmrcPredictionScore(),
                    'predicted_guessed_similarity' => $this->getHmrcPredictedGuessedSimilarity(),
                    'predicted_matched_similarity' => $this->getHmrcPredictedMatchedSimilarity(),
                    'guessed_matched_similarity' => $this->getHmrcGuessedMatchedSimilarity(),
                ],
            ],
            'other' => [
                'matched' => $this->getOtherMatched(),
                'correctly_matched' => $this->getOtherCorrectlyMatched(),
                'scores' => [
                    'prediction_score' => $this->getOtherPredictionScore(),
                    'predicted_guessed_similarity' => $this->getOtherPredictedGuessedSimilarity(),
                    'predicted_matched_similarity' => $this->getOtherPredictedMatchedSimilarity(),
                    'guessed_matched_similarity' => $this->getOtherGuessedMatchedSimilarity(),
                ],
            ],
            'non_statutory' => [
                'matched' => $this->getNonStatutoryMatched(),
                'correctly_matched' => $this->getNonStatutoryCorrectlyMatched(),
                'scores' => [
                    'prediction_score' => $this->getNonStatutoryPredictionScore(),
                    'predicted_guessed_similarity' => $this->getNonStatutoryPredictedGuessedSimilarity(),
                    'predicted_matched_similarity' => $this->getNonStatutoryPredictedMatchedSimilarity(),
                    'guessed_matched_similarity' => $this->getNonStatutoryGuessedMatchedSimilarity(),
                ],
            ],
            'dtc' => $this->getDtc()->format('Y-m-d H:i:s'),
            'dtm' => $this->getDtm()->format('Y-m-d H:i:s'),
        ];
    }
}
