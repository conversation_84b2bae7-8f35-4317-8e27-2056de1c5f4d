<?php

namespace App\Entity;

use App\Repository\TransitFeeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TransitFeeRepository::class)]
#[ORM\Table(name: "transit_fees")]
class TransitFee
{
    public const FEE_0_100_NAME = '0_100';
    public const FEE_101_250_NAME = '101_250';
    public const FEE_251_500_NAME = '251_500';
    public const FEE_501_750_NAME = '501_750';
    public const FEE_751_1000_NAME = '751_1000';
    public const FEE_1001_2000_NAME = '1001_2000';

    public const FEE_BRACKETS = [
        self::FEE_0_100_NAME,
        self::FEE_101_250_NAME,
        self::FEE_251_500_NAME,
        self::FEE_501_750_NAME,
        self::FEE_751_1000_NAME,
        self::FEE_1001_2000_NAME,
    ];

    public const REGION_UK = 'uk';
    public const REGION_EUROPE = 'europe';
    public const REGION_WORLD = 'world';

    public const REGIONS = [
        self::REGION_UK,
        self::REGION_EUROPE,
        self::REGION_WORLD,
    ];

    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id = null;

    #[ORM\Column(length: 100, nullable: false)]
    private ?string $feeName = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $uk = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $europe = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $world = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFeeName(): ?string
    {
        return $this->feeName;
    }

    public function setFeeName(string $feeName): self
    {
        $this->feeName = $feeName;

        return $this;
    }

    public function getUkFee(): ?float
    {
        return $this->uk;
    }

    public function setUkFee(?float $uk): self
    {
        $this->uk = $uk;

        return $this;
    }

    public function getEuropeFee(): ?float
    {
        return $this->europe;
    }

    public function setEuropeFee(?float $europe): self
    {
        $this->europe = $europe;

        return $this;
    }

    public function getWorldFee(): ?float
    {
        return $this->world;
    }

    public function setWorldFee(?float $world): self
    {
        $this->world = $world;

        return $this;
    }

    /**
     * Convert entity to array
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'feeName' => $this->feeName,
            'uk' => $this->uk,
            'europe' => $this->europe,
            'world' => $this->world,
        ];
    }
}
