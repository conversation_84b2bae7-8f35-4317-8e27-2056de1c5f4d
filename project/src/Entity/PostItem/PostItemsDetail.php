<?php

namespace App\Entity\PostItem;

use App\Repository\PostItem\PostItemsDetailRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator;

#[ORM\Entity(repositoryClass: PostItemsDetailRepository::class)]
#[ORM\Table(name: "post_items_details")]
class PostItemsDetail
{
    #[ORM\Id]
    #[ORM\Column(type: 'uuid', unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: UuidGenerator::class)]
    private $id;

    #[ORM\ManyToOne(inversedBy: "details", targetEntity:'PostItem')]
    #[ORM\JoinColumn(name:"post_items_id", nullable: false)]
    private ?PostItem $postItemId = null;

    #[ORM\Column(length: 255, name:"detail_key")]
    private ?string $key = null;

    #[ORM\Column(length: 255, name:"detail_value")]
    private ?string $value = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtc = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dtm = null;

    public function getId()
    {
        return $this->id;
    }

    public function getPostItemId(): ?PostItem
    {
        return $this->postItemId;
    }

    public function setPostItemId(?PostItem $postItemId): self
    {
        $this->postItemId = $postItemId;

        return $this;
    }

    public function getKey(): ?string
    {
        return $this->key;
    }

    public function setKey(string $key): self
    {
        $this->key = $key;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getDtc(): ?\DateTimeInterface
    {
        return $this->dtc;
    }

    public function setDtc(\DateTimeInterface $dtc): self
    {
        $this->dtc = $dtc;

        return $this;
    }

    public function getDtm(): ?\DateTimeInterface
    {
        return $this->dtm;
    }

    public function setDtm(?\DateTimeInterface $dtm): self
    {
        $this->dtm = $dtm;

        return $this;
    }
    
    public function toArray(bool $asObject=false)
    {
        $data = [
            'id' => $this->id,
            'postItemId' => $this->postItemId,
            'key' => $this->key,
            'value' => $this->value,
            'dtc' => $this->dtc->format("Y-m-d H:i:s"),
            'dtm' => (!empty($this->dtm) ? $this->dtm->format("Y-m-d H:i:s") : ''),
        ];
        return $asObject ? (object) $data : $data;
    }
}
