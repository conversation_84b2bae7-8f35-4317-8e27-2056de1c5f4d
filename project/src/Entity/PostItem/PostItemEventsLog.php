<?php

namespace App\Entity\PostItem;

use App\Repository\PostItem\PostItemEventsLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator;

#[ORM\Entity(repositoryClass: PostItemEventsLogRepository::class)]
#[ORM\Table(name: "post_items_events_log")]
class PostItemEventsLog
{
    #[ORM\Id]
    #[ORM\Column(type: 'uuid', unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: UuidGenerator::class)]
    private $id;

    #[ORM\ManyToOne(inversedBy: "events", targetEntity:'PostItem')]
    #[ORM\JoinColumn(name:"post_items_id", nullable: false)]
    private ?PostItem $postItemId = null;

    #[ORM\Column(length: 255)]
    private ?string $eventName = null;

    #[ORM\Column(length: 255)]
    private ?string $operator = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtc = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dtm = null;

    public function getId()
    {
        return $this->id;
    }

    public function getEventName(): ?string
    {
        return $this->eventName;
    }

    public function setEventName(?string $eventName): self
    {
        $this->eventName = $eventName;

        return $this;
    }

    public function getPostItemId(): ?PostItem
    {
        return $this->postItemId;
    }

    public function setPostItemId(?PostItem $postItemId): self
    {
        $this->postItemId = $postItemId;

        return $this;
    }

    public function getOperator(): ?string
    {
        return $this->operator;
    }

    public function setOperator(string $operator): self
    {
        $this->operator = $operator;

        return $this;
    }

    public function getDtc(): ?\DateTimeInterface
    {
        return $this->dtc;
    }

    public function setDtc(\DateTimeInterface $dtc): self
    {
        $this->dtc = $dtc;

        return $this;
    }

    public function getDtm(): ?\DateTimeInterface
    {
        return $this->dtm;
    }

    public function setDtm(?\DateTimeInterface $dtm): self
    {
        $this->dtm = $dtm;

        return $this;
    }

    public function extract(bool $toArray = false)
    {
        $data = [
            'id' => $this->id,
            'eventName' => $this->eventName,
            'postItemId' => $this->postItemId,
            'operator' => $this->operator,
            'dtm' => (!empty($this->dtm) ? $this->dtm->format("Y-m-d H:i:s") : ''),
            'dtc' => $this->dtc->format("Y-m-d H:i:s"),
            'isPosted' => $this->isPosted(),
            'isRTS' => $this->isRTS(),
            'isCollected' => $this->isCollected()
        ];

        return ($toArray ? $data : (object) $data);
    }

    public function isPosted () : bool
    {
        return $this->eventName == 'address_label_generated';
    }

    public function isRTS () : bool
    {
        return $this->eventName == 'post_returned_to_sender';
    }

    public function isCollected () : bool
    {
        return $this->eventName == 'post_collected';
    }
}
