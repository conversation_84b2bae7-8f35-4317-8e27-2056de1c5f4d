<?php

namespace App\Entity\PostItem;

use App\Model\PostItem as ModelPostItem;
use App\Repository\PostItem\PostItemRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator;

#[ORM\Entity(repositoryClass: PostItemRepository::class)]
#[ORM\Table(name: "post_items")]
class PostItem
{
    const SENDER_HMRC = 'HMRC';
    const SENDER_OTHER = 'OTHER';
    const SENDER_COMPANIES_HOUSE = 'COMPANIES_HOUSE';
    const SENDER_COURT_LETTER = 'COURT_LETTER';

    public const STATUS_ADDED = 'added';
    public const STATUS_SCAN_ONLY = 'scan_only';
    public const STATUS_WAITING_PAYMENT = 'waiting_payment';
    public const STATUS_TO_BE_FORWARDED = 'to_be_forwarded';
    public const STATUS_TO_BE_RTS_D = 'to_be_rts-d';
    public const STATUS_TO_BE_COLLECTED = 'to_be_collected';
    public const STATUS_TO_BE_SECURELY_DESTROYED = 'to_be_securely_destroyed';
    public const STATUS_FORWARDED = 'forwarded';
    public const STATUS_RTS_D = 'rts-d';
    public const STATUS_COLLECTED = 'collected';
    public const STATUS_SECURELY_DESTROYED = 'securely_destroyed';

    const TYPE_PARCEL = 'parcel';
    const TYPE_STATUTORY = 'statutory';
    const TYPE_NON_STATUTORY = 'non-statutory';

    #[ORM\Id]
    #[ORM\Column(type: 'uuid', unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: UuidGenerator::class)]
    private $id;

    #[ORM\Column(length: 255)]
    private ?string $companyNumber = null;

    #[ORM\Column(length: 255)]
    private ?string $companyName = null;

    #[ORM\OneToMany(mappedBy: "postItemId", targetEntity: PostItemsDetail::class)]
    #[ORM\OrderBy(['dtc'=>'DESC'])]
    private $details;

    #[ORM\OneToMany(mappedBy: "postItemId", targetEntity: PostItemEventsLog::class)]
    #[ORM\OrderBy(['dtc'=>'DESC'])]
    private $events;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $type = null;

    #[ORM\Column(length: 255, columnDefinition: "enum('HMRC', 'OTHER', 'COMPANIES_HOUSE', 'COURT_LETTER')")]
    private ?string $sender = null;

    #[ORM\Column(length: 255)]
    private ?string $fileName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $batchNumber = null;

    #[ORM\Column(length: 255)]
    private ?string $operator = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtm = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtc = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $transactionId = null;

    #[ORM\Column(
        length: 255,
        columnDefinition: "enum(
                                'added',
                                'scan_only',
                                'waiting_payment',
                                'to_be_forwarded',
                                'to_be_rts-d',
                                'to_be_collected',
                                'to_be_securely_destroyed',
                                'forwarded',
                                'rts-d',
                                'collected',
                                'securely_destroyed',
                            )")]

    private string $status;

    public const PROCESSING_SCRIPT_STATUSES = [
        self::STATUS_ADDED,
    ];

    public const UPDATE_SCRIPT_STATUSES = [
        self::STATUS_WAITING_PAYMENT,
        self::STATUS_TO_BE_COLLECTED,
    ];

    public const COMPANY_INBOX_STATUSES = [
        self::STATUS_ADDED,
        self::STATUS_SCAN_ONLY,
        self::STATUS_WAITING_PAYMENT,
        self::STATUS_TO_BE_FORWARDED,
        self::STATUS_TO_BE_COLLECTED,
        self::STATUS_FORWARDED,
        self::STATUS_COLLECTED,
    ];

    public const PARSED_STATUSES = [
        self::STATUS_ADDED => 'Added',
        self::STATUS_SCAN_ONLY => 'Scan only',
        self::STATUS_WAITING_PAYMENT => 'Waiting payment',
        self::STATUS_TO_BE_FORWARDED => 'To be forwarded',
        self::STATUS_TO_BE_RTS_D => 'To be RTS\'d',
        self::STATUS_TO_BE_COLLECTED => 'To be collected',
        self::STATUS_TO_BE_SECURELY_DESTROYED => 'To be securely destroyed',
        self::STATUS_FORWARDED => 'Forwarded',
        self::STATUS_RTS_D => 'RTS\'d',
        self::STATUS_COLLECTED => 'Collected',
        self::STATUS_SECURELY_DESTROYED => 'Securely destroyed',
    ];

    public function getId()
    {
        return $this->id;
    }

    public function getCompanyNumber(): ?string
    {
        return $this->companyNumber;
    }

    public function setCompanyNumber(?string $companyNumber): self
    {
        $this->companyNumber = $companyNumber ?? "";

        return $this;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): self
    {
        $this->companyName = $companyName;

        return $this;
    }

    public function getDetails(): Collection
    {
        return $this->details;
    }

    public function getEvents(): Collection
    {
        return $this->events;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getSender(): ?string
    {
        return $this->sender;
    }

    public function setSender(string $sender): self
    {
        if (!in_array($sender, [self::SENDER_HMRC, self::SENDER_OTHER, self::SENDER_COMPANIES_HOUSE, self::SENDER_COURT_LETTER])) {
            throw new \InvalidArgumentException("Invalid param sender: Param " . $sender);
        }

        $this->sender = $sender;

        return $this;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function setFileName(string $fileName): self
    {
        $this->fileName = $fileName;

        return $this;
    }

    public function getBatchNumber(): ?string
    {
        return $this->batchNumber;
    }

    public function setBatchNumber(?string $batchNumber): self
    {
        $this->batchNumber = $batchNumber;

        return $this;
    }

    public function getOperator(): ?string
    {
        return $this->operator;
    }

    public function setOperator(string $operator): self
    {
        $this->operator = $operator;

        return $this;
    }

    public function getDtm(): ?\DateTimeInterface
    {
        return $this->dtm;
    }

    public function setDtm(\DateTimeInterface $dtm): self
    {
        $this->dtm = $dtm;

        return $this;
    }

    public function getDtc(): ?\DateTimeInterface
    {
        return $this->dtc;
    }

    public function setDtc(\DateTimeInterface $dtc): self
    {
        $this->dtc = $dtc;

        return $this;
    }

    public function getTransactionId(): ?int
    {
        return $this->transactionId;
    }

    public function setTransactionId(?int $transactionId): self
    {
        $this->transactionId = $transactionId;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @throws \Exception
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'companyNumber' => $this->companyNumber,
            'companyName' => $this->companyName,
            'details' => array_map(function ($detail) {
                return $detail->toArray(true);
            }, $this->details->getValues()),
            'events' => array_map(function ($event) {
                return $event->extract();
            }, $this->events->getValues()),
            'actionTaken' => $this->actionTaken(),
            'parsedStatus' => $this->getParsedStatus(),
            'type' => $this->type,
            'sender' => $this->sender,
            'fileName' => $this->fileName,
            'batchNumber' => $this->batchNumber,
            'operator' => $this->operator,
            'dtc' => $this->dtc->format("Y-m-d H:i:s"),
            'dtm' => (!empty($this->dtm) ? $this->dtm->format("Y-m-d H:i:s") : ''),
            'address' => [
                'country' => '',
                'city' => '',
                'postcode' => '',
                'address' => '',
            ]
        ];
    }

    public function actionTaken(): string
    {
        return match ($this->status) {
            self::STATUS_COLLECTED => 'collected',
            self::STATUS_FORWARDED => 'forwarded',
            self::STATUS_RTS_D => 'returned to sender',
            self::STATUS_SECURELY_DESTROYED => 'securely destroyed',
            default => 'none',
        };
    }

    public function dataWithAddress($doctrineManager): array
    {
        $data = $this->toArray();
        $data['address'] = (new ModelPostItem($doctrineManager, $this))->getPostItemAddress();

        return $data;
    }

    public function hasBeenForwarded(){
        $alreadyForwarded = false;
        foreach ($this->events as $event) {
            if ($event->isPosted()) {
                $alreadyForwarded = true;
                break;
            }
        }
        return $alreadyForwarded;
    }

    public function getDetailByKey(string $key): ?PostItemsDetail
    {
        foreach ($this->getDetails() as $detail) {
            if ($detail->getKey() == $key) {
                return $detail;
            }
        }

        return null;
    }

    /**
     * @throws \Exception
     */
    public function getParsedStatus(): ?string
    {
        return self::PARSED_STATUSES[$this->status];
    }

}
