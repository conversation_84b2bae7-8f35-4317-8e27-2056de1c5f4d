<?php

namespace App\Entity;

use App\Repository\StatusMessageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use InvalidArgumentException;
use Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator;

#[ORM\Entity(repositoryClass: StatusMessageRepository::class)]
#[ORM\Table(name: "status_messages")]
class StatusMessage
{
    #[ORM\Id]
    #[ORM\Column(type: 'uuid', unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: UuidGenerator::class)]
    private $id = null;

    #[ORM\Column(nullable: true)]
    private ?string $data = null;

    #[ORM\Column(length: 255)]
    private ?string $operator = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtc = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dtm = null;

    public function getId()
    {
        return $this->id;
    }

    public function getOperator(): string
    {
        return $this->operator;
    }

    public function setOperator(string $operator): self
    {
        $this->operator = $operator;
        return $this;
    }

    public function getData($asArray = false): string|array
    {
        if ($asArray) {
            $result = json_decode($this->data, true);
            unset($result['message']);
            return $result;
        }
        return $this->data;
    }

    public function setData(array|string $data): self
    {
        if (is_array($data)) {
            $this->data = json_encode($data);
        } elseif (is_string($data)) {
            $this->data = $data;
        } else {
            throw new InvalidArgumentException('Data must be an array or string');
        }

        return $this;
    }

    public function getDtc(): ?\DateTimeInterface
    {
        return $this->dtc;
    }

    public function setDtc(\DateTimeInterface $dtc): self
    {
        $this->dtc = $dtc;

        return $this;
    }

    public function getDtm(): ?\DateTimeInterface
    {
        return $this->dtm;
    }

    public function setDtm(\DateTimeInterface $dtm): self
    {
        $this->dtm = $dtm;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'message_id' => $this->getId(),
            'operator' => $this->getOperator(),
            'data' => $this->getData($asArray = true),
            'dtc' => $this->getDtc()->format('Y-m-d H:i:s'),
            'dtm' => $this->getDtm()->format('Y-m-d H:i:s'),
        ];
    }
}
