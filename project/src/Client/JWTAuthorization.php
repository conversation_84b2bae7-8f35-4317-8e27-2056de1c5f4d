<?php

namespace App\Client;

use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use stdClass;

class JWTAuthorization
{
    public const AUTH_COOKIE_NAME = 'Authorization';
    public const REFRESH_TOKEN_COOKIE_NAME = 'X-Auth';

    private array $firebaseConf;
    private string $ApiJwtKey;

    public function __construct()
    {
        $this->firebaseConf = [
            'key' => $_ENV['FIREBASE_KEY'],
            'project_id' => $_ENV['FIREBASE_PROJECT_ID'],
            'iss_pattern' => $_ENV['FIREBASE_ISS_PATTERN'],
            'public_keys_link' => $_ENV['FIREBASE_PUBLIC_KEYS_LINK'],
        ];

        $this->ApiJwtKey = $_ENV['API_JWT_KEY'];
    }

    /**
     * @param string $token
     * @return array
     */
    public function validateToken(string|null $token): array
    {
        $result = [
            "valid" => false,
            "message" => "The authorization informed is not valid.",
            "data" => null
        ];

        if (empty($token) || $token == "null") {
            return $result;
        }
        
        // Recover the keys from google
        $keys = json_decode(file_get_contents($this->firebaseConf['public_keys_link']), true);

        // Recovering the kid from the user token. This proccess is made so we can extract
        // the key returned from the google request validate the token
        $tokenData = explode(".", $token);
        $headTokenB64 = $tokenData[0];
        
        $header = json_decode(Jwt::urlsafeB64Decode($headTokenB64));

        $key = $keys[$header->kid];

        return $this->attemptValidation($token, new Key($key, 'RS256'));
    }

    public function validateApiToken(?string $token)
    {
        $result = [
            "valid" => false,
            "message" => "The authorization informed is not valid.",
            "data" => null
        ];

        if (!isset($token)) {
            return $result;
        }

        return $this->attemptValidation($token, new Key($this->ApiJwtKey, 'HS256'));
    }

    private function attemptValidation($token, Key $key)
    {
        try {
            $payload = JWT::decode($token, $key);

            return [
                'valid' => true,
                'data' => $payload,
                'message' => 'Validation successful.',
            ];
        } catch (ExpiredException $e) {
            return [
                "valid" => false,
                "message" => 'Token expired: ' . $e->getMessage(),
                "data" => null
            ];
        } catch (\Exception $e) {
            return [
                "valid" => false,
                "message" => 'Exception: ' . $e->getMessage(),
                "data" => null
            ];
        }
    }

    public function getAuthToken(Request $request): string|null {
        $authToken = $request->headers->get(self::AUTH_COOKIE_NAME);

        if (!empty($authToken)){
            return str_replace(["Basic","Bearer"," "],"",$authToken);
        }
        
        $authToken = $request->cookies->get(self::AUTH_COOKIE_NAME);

        return $authToken;

    }

    public function getRefreshToken(Request $request): string|null {
        $refreshToken = $request->headers->get(self::REFRESH_TOKEN_COOKIE_NAME);

        if (!empty($refreshToken)){
            return $refreshToken;
        }
        
        $refreshToken = $request->cookies->get(self::REFRESH_TOKEN_COOKIE_NAME);

        return $refreshToken;
    }

    public function getUser(Request $request): stdClass|null {
        $user = $this->validateToken($this->getAuthToken($request));

        if (isset($user['data'])) {
            return $user['data'];
        }

        return null;
    }

    public function createCookie(string $name, string $content): Cookie{
        return new Cookie(
            $name,                                    // Cookie name
            $content,                                 // Cookie content
            (new \DateTime('now'))->modify("+1 day"), // Expiration date
            "/",                                      // Path
            $_SERVER['SERVER_NAME'],                  // Domain
            $_SERVER['REQUEST_SCHEME'] === 'https',   // Secure
            false,                                    // HttpOnly
            true,                                     // Raw
            'Strict'                                  // SameSite policy
        );
    }

    public function destroyCookie(Request $request, Response $response, string $key = self::AUTH_COOKIE_NAME): void{
        if ($request->cookies->has($key)) {
            $response->headers->clearCookie($key, '/', null);
        }
    }

    /**
     * Verifying if the token is valid. The verification is made by using the basics fields,
     * and comparing to the pattern of the firebase which can be checked in the documentation
     * https://firebase.google.com/docs/auth/admin/verify-id-tokens
     *
     * @param stdClass $tokenData
     * @return boolean
     */
    public function tokenDataIsValid(stdClass $tokenData): bool
    {
        if (!$this->validateTokenField($tokenData, "iss", $this->firebaseConf["iss_pattern"])) {
            return false;
        }
        if (!$this->validateTokenField($tokenData, "aud", $this->firebaseConf["project_id"])) {
            return false;
        }
        return true;
    }

    /**
     * @param string $field
     * @param mixed $value
     * @return boolean
     */
    private function validateTokenField(stdClass $tokenData, string $field, $value): bool
    {
        if (!isset($tokenData->$field) || $tokenData->$field != $value) {
            return false;
        }

        return true;
    }

    /**
     * Refreshing the JWT token when expires
     *
     * @return array
     */
    public function refreshToken(string $refreshToken): array
    {
        $client = HttpClient::create();

        // Making the request to refresh the token
        $response = $client->request('POST', "https://securetoken.googleapis.com/v1/token?key=" . $this->firebaseConf["key"], [
            "headers" => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            "body" => [
                "grant_type" => "refresh_token",
                "refresh_token" => $refreshToken
            ]
        ]);

        if ($response->getStatusCode() == 200) {
            $content = $response->toArray();

            return [$content["access_token"],$content["refresh_token"]];
        }

        return [null,null];
    }
}