<?php

namespace App\Client;

use Google\Cloud\Storage\Bucket;
use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Storage\StorageObject;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Storage
{
    private const ROOT_DIRECTORY = 'post_items';
    private StorageClient $storage;
    private Bucket $bucket;

    public function __construct()
    {
        $config = [
            'keyFilePath' => __DIR__ . "/" . "storage-config.json",
            'projectId' => $_ENV['KOFAX_PROJECT_NAME'],
        ];

        $this->storage = new StorageClient($config);
        $this->bucket = $this->storage->bucket('kofax-msg');
    }

    public function uploadFile(UploadedFile $file): StorageObject
    {
        return $this->bucket->upload(
            fopen($file->getPathname(), 'r'),
            [
                'name' => self::ROOT_DIRECTORY . '/pdf_uploaded/' . $file->getClientOriginalName()
            ]
        );
    }

    public function getFile(string $file): StorageObject
    {
        return $this->bucket->object(self::ROOT_DIRECTORY . '/pdf_uploaded/' . $file);
    }
}