<?php

namespace App\Client;

use PDO;

class DeveloperList
{
    private $pdo;
    private $databaseDsn;
    private $tableName;
    private $databaseUser;
    private $databasePassword;

    public function __construct()
    {
        $this->databaseDsn = getenv('DEVELOPER_LIST_DATABASE_DSN');
        $this->tableName = getenv('DEVELOPER_LIST_TABLE_NAME');
        $this->databasePassword = getenv('DATABASE_PASSWORD');
        $this->databaseUser = getenv('DATABASE_USER');
        
        $this->pdo = new PDO($this->databaseDsn, $this->databaseUser, $this->databasePassword);
    }

    public function query(string $sql): array
    {
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function isDeveloper(string $uid)
    {
        $sql = sprintf(
            "SELECT * FROM %s WHERE uid = \"%s\"",
            $this->tableName,
            $uid
        );

        $result = $this->query($sql);
        
        if (empty($result) || is_null($result)) {
            return false;
        }

        if (!isset($result[0]['enabled'])) {
            return false;
        }

        return $result[0]['enabled'] == 1;
    }
}