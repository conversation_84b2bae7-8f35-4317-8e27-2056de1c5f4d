<?php

namespace App\Client;

use App\Dto\Completed;
use App\Dto\Topic;
use Google\Cloud\PubSub\Message;
use Google\Cloud\PubSub\PubSubClient;

class PubSub
{
    private PubSubClient $pubSub;
    /**
     * @var Message[]
     */
    private array $messages;

    public function __construct()
    {
        $config = [
            'keyFilePath' => __DIR__ . "/" . "pubsub-config.json",
            'projectId' => 'automl-post-items',
        ];

        $this->pubSub = new PubSubClient($config);
    }

    /**
     * @return array
     */
    public function getMessages(): array
    {
        return $this->messages;
    }

    /**
     * @param mixed $subscription
     */
    public function setMessages(
        mixed $subscription
    ): void
    {
        $this->messages = $subscription;
    }

    public function publishToWaitingForResolution(
        Message $message
    )
    {
        $this->pubSub
            ->topic(Topic::waitingForResolution())
            ->publish($message);
    }

    public function rePublishMessagesToUiStatus($messages)
    {
        foreach ($messages as $message) {
            $this->pubSub->topic(Topic::uiStatus())->publish($message);
        }
    }

    public function pushPdfUploaded(
        string $name,
        string $typeMail,
        string $operator,
        string $dtc
    ): array
    {
        return $this->pubSub
            ->topic(Topic::pdfUploaded())
            ->publish([
                'data' => json_encode([
                    "pdf_filename" => $name,
                    "operation_status" => "PDF_UPLOADED",
                    "type_mail" => $typeMail,
                    "operator" => $operator,
                    "dtc" => $dtc
                ])
            ]);
    }

    public function pushToUiStatus(
        string $status,
        string $fileName
    )
    {
        $topic = $this->pubSub->topic(Topic::uiStatus());

        $data = [
            "pdf_filename" => $fileName,
            "operation_status" => $status,
        ];

        $topic->publish([
            'data' => json_encode($data),
        ]);
    }

    public function pushToMrPredictions(
        string $name,
        string $companyName,
        string $imagePath,
        string $confidence
    )
    {
        $topic = $this->pubSub->topic(Topic::mrPredictions());

        $data = [
            "pdf_filename" => $name,
            "operation_status" => "COMPANY_NAME_FOUND",
            "predictions" => [
                [
                    "companyName" => $companyName,
                    "imagePath" => $imagePath,
                    "confidence" => rand(30, 98) . "%"
                ],
                [
                    "companyName" => $companyName . '  ' . 'b',
                    "imagePath" => $imagePath . '  ' . 'b',
                    "confidence" => rand(30, 98) . "%"
                ]
            ],
        ];

        $topic->publish([
            'data' => json_encode($data),
        ]);
    }

    public function pushToMrCompleted(
        array $completed
    )
    {
        $topic = $this->pubSub->topic(Topic::mrCompleted());

        $topic->publish([
            'data' => json_encode($completed),
        ]);
    }

    /**
     * @return Message[]
     */
    public function pullFromUiStatus(): array
    {
        $subscription =  $this->pubSub->subscription(Topic::uiStatus());
        $this->setMessages($subscription->pull());
        return $this->messages;
    }

    /**
     * @param Message[] $messages
     * @param Topic $topic
     * @return void
     */
    public function ackMessages(
        array $messages,
        Topic $topic
    ): void
    {
        foreach ($messages as $message) {
            $this->pubSub->subscription($topic)->acknowledge($message);
        }
    }
    /**
     * @return Message[]
     */
    public function pullWaiting(): array
    {
        $subscription =  $this->pubSub->subscription(Topic::waitingForResolution());

        $messages = $subscription->pull();

        return $messages;
    }

    /**
     * @return Message[]
     */
    public function pullMrPredictions(): array
    {

        $subscription =  $this->pubSub->subscription(Topic::mrPredictions());

        $messages = $subscription->pull();

        foreach ($messages as $message) {
            $this->publishToWaitingForResolution($message);
            $subscription->acknowledge($message);
        }

        return $messages;
    }

    public function acknowledge(
        Topic $topic,
        Message $message
    ): void
    {
        $this->pubSub
            ->subscription($topic->getName())
            ->acknowledge($message);
    }
}
