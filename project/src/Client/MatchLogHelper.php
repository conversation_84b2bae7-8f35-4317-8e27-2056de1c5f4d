<?php

namespace App\Client;

use PDO;

class MatchLogHelper
{
    private const DATABASE_DSN = 'mysql:host=**************;dbname=cms_mantle;charset=utf8;port=3306';
    private const DATABASE_USER = 'mailroom_dev_user';
    private const DATABASE_PASSWORD = 'nAnjnO8cpIH0R8op';
    private const TABLE_NAME = 'mailroom_match_events';
    private const MAIL_TYPES = [
        'total',
        'COMPANIES_HOUSE',
        'HMRC',
        'OTHER',
        'NON_STATUTORY',
    ];

    public static function getTotalCount(int $month, int $year, bool $onlySuccessful = null, string $type = null): int
    {
        $pdo = new PDO(self::DATABASE_DSN, self::DATABASE_USER, self::DATABASE_PASSWORD);

        $query = implode(' ', [
            'SELECT COUNT(1)',
            'FROM', self::TABLE_NAME,
            'WHERE',
                sprintf('(EXTRACT(MONTH FROM dtc) = %d AND EXTRACT(YEAR FROM dtc) = %d)', $month, $year),
                $onlySuccessful ? 'AND edited = 0' : null,
                $type != 'total' ? sprintf('AND mail_type = "%s"', $type) : null,
            ';'
        ]);

        $stmt = $pdo->query($query);
        return intval($stmt->fetchColumn());
    }

    public static function getScores(int $month, int $year, ?string $type = null): array
    {
        $pdo = new PDO(self::DATABASE_DSN, self::DATABASE_USER, self::DATABASE_PASSWORD);

        $query = implode(' ', [
            'SELECT',
                'AVG(prediction_score),',
                'AVG(predicted_guessed_similarity),',
                'AVG(predicted_matched_similarity),',
                'AVG(guessed_matched_similarity)',
            'FROM', self::TABLE_NAME,
            'WHERE',
                sprintf('(EXTRACT(MONTH FROM dtc) = %d AND EXTRACT(YEAR FROM dtc) = %d)', $month, $year),
                $type != 'total' ? sprintf('AND mail_type = "%s"', $type) : null,
            ';'
        ]);

        $stmt = $pdo->query($query);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return [
            'prediction_score'             => round($result['AVG(prediction_score)'], 2),
            'predicted_guessed_similarity' => round($result['AVG(predicted_guessed_similarity)'], 2),
            'predicted_matched_similarity' => round($result['AVG(predicted_matched_similarity)'], 2),
            'guessed_matched_similarity'   => round($result['AVG(guessed_matched_similarity)'], 2),
        ];
    }

    public static function buildAiReportByType(int $month, int $year, ?string $type = null): array
    {
        $totalMatched = self::getTotalCount(
            month: $month,
            year: $year,
            type: $type
        );

        if (!$totalMatched) {
            return [
                'No items matched found.'
            ];
        }

        $totalMatchedCorrectly = self::getTotalCount(
            month: $month,
            year: $year,
            type: $type,
            onlySuccessful: true
        );

        return [
            'items_matched' => $totalMatched,
            'items_matched_correctly' => $totalMatchedCorrectly,
            'correct_percentage' => round($totalMatchedCorrectly / $totalMatched * 100, 2),
            'scores' => self::getScores(
                month: $month,
                year: $year,
                type: $type
            ),
        ];
    }

    public static function buildAiReport(int $month, int $year): array
    {
        $report = [];
        $report['period'] = sprintf('%s/%s', str_pad($month, 2, '0', STR_PAD_LEFT), $year);

        foreach (self::MAIL_TYPES as $type) {
            $report[$type] = self::buildAiReportByType(
                month: $month,
                year: $year,
                type: $type
            );
        }

        return $report;
    }

    public static function formatReportText(array $report): string
    {
        $lines = [];
        $typeTitles = [
            'total' => 'Total',
            'COMPANIES_HOUSE' => 'Companies House',
            'HMRC' => 'HMRC',
            'OTHER' => 'Other',
            'NON_STATUTORY' => 'Non-statutory',
        ];

        $lines[] = sprintf("# Mailroom AI Performance Report (%s)", $report['period']);
        
        foreach (self::MAIL_TYPES as $type) {
            $lines[] = sprintf("## %s Results:", $typeTitles[$type]);
            
            if (!isset($report[$type]) || $report[$type] == ['No items matched found.']) {
                $lines[] = "### No items matched found.";
                continue;
            }
            
            $lines[] = sprintf("- **Total items matched:** %s", $report[$type]['items_matched']);
            $lines[] = sprintf("- **Correct matches:** %s (%s%%)",
                $report[$type]['items_matched_correctly'], $report[$type]['correct_percentage']);
            $lines[] =         "- **Scores:**";
            $lines[] = sprintf(" - *Prediction score:* %s", $report[$type]['scores']['prediction_score']);
            $lines[] = sprintf(" - *Predicted/guessed similarity:* %s", $report[$type]['scores']['predicted_guessed_similarity']);
            $lines[] = sprintf(" - *Predicted/matched similarity:* %s", $report[$type]['scores']['predicted_matched_similarity']);
            $lines[] = sprintf(" - *Guessed/matched similarity:* %s", $report[$type]['scores']['guessed_matched_similarity']);
        }

        $lines[] = '## Score definitions:';
        $lines[] = '- **Prediction score:** AI score (confidence level)';
        $lines[] = '- **Predicted/guessed similarity:** AI predicted string compared to the company name found in the database (in that order)';
        $lines[] = '- **Predicted/matched similarity:** AI predicted string compared to the name that the item was matched with (in that order)';
        $lines[] = '- **Guessed/matched similarity:** company name found in the database compared to the name that the item was matched with (in that order)';

        return implode("\n", $lines);
    }
}