<?php

namespace App\Client;

use App\Entity\PostItem\PostItem;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class MatchClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => $_ENV['MANTLE_API_MATCH_URL'],
            'headers' => [
                'Authorization' => sprintf('Basic %s', $_ENV['MANTLE_API_MATCH_AUTH_TOKEN']),
            ],
        ]);
    }

    /**
     * @throws \Exception
     */
    public function match(array $data, PostItem $postItem): void
    {
        try {
            $this->client->post('/publish', [
                'multipart' => $this->buildMultipart($data, $postItem),
            ]);
        } catch (RequestException $e) {
            $this->debug($e);
            throw new \Exception($e->getMessage());
        }
    }

    private function buildMultipart(array $data, PostItem $postItem): array
    {
        $multipart = [];

        $data['id'] = $postItem->getId();

        $multipart['data'] = [
            'name' => 'data',
            'contents' => json_encode($data),
        ];

        return $multipart;
    }

    private function debug($error): void
    {
        // replace with your debug logic
        error_log($error);
    }
}
