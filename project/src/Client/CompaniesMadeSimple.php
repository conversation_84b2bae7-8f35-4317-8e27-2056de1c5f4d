<?php

namespace App\Client;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Response;

class CompaniesMadeSimple 
{
    private $client;
    private string $companiesMadeSimpleUrl;
    private array $companiesMadeSimpleAuthorization;
    private string $kofaxDatabaseUrl;
    private array $kofaxDatabaseAuthorization;
    private string $mailroomSearchEndpoint;
    private string $oldMailroomSearchEndpoint;
    private string $companyAddressSearchEndpoint;
    private string $courtLetterNotificationEndpoint;

    public function __construct(HttpClientInterface $client)
    {
        $this->companiesMadeSimpleUrl = $_ENV['COMPANIES_MADE_SIMPLE_URL'];
        $this->companiesMadeSimpleAuthorization = ['X-Msg-Auth' => $_ENV['COMPANIES_MADE_SIMPLE_AUTHORIZATION']];
        $this->kofaxDatabaseUrl = $_ENV['KOFAX_DATABASE_URL'];
        $this->kofaxDatabaseAuthorization = ['Authorization' => sprintf('Basic %s', $_ENV['KOFAX_DATABASE_AUTHORIZATION'])];
        $this->mailroomSearchEndpoint = $_ENV['MAILROOM_SEARCH_ENDPOINT'];
        $this->oldMailroomSearchEndpoint = $_ENV['OLD_MAILROOM_SEARCH_ENDPOINT'];
        $this->companyAddressSearchEndpoint = $_ENV['COMPANY_ADDRESS_SEARCH_ENDPOINT'];
        $this->courtLetterNotificationEndpoint = $_ENV['COURT_LETTER_NOTIFICATION_ENDPOINT'];
        
        $this->client = $client;
    }

    private function request($method, $endpoint, $headers = [])
    {
        return $this->client->request(
            $method,
            $this->companiesMadeSimpleUrl . $endpoint,
            [
                'headers' => $headers + $this->companiesMadeSimpleAuthorization
            ]
        );
    }

    public function companySearch(string $queryString)
    {
        return $this->request('GET', sprintf(
            "%s?%s",
            $this->mailroomSearchEndpoint,
            $queryString
        ));
    }

    public function oldCompanySearch(string $queryString)
    {
        return $this->request('GET', sprintf(
            "%s?%s",
            $this->oldMailroomSearchEndpoint,
            $queryString
        ));
    }

    public function getCompanyAddressByCompanyNumber(string $companyNumber): array
    {
        /** @var array $addressArray */
        $addressArray = json_decode(
            $this->request(
                'GET',
                sprintf("%s%s", $this->companyAddressSearchEndpoint, $companyNumber)
            )->getContent(),
             true
        );

        try {
            $this->validateAddressArray($addressArray);
        } catch (\Exception $e) {
            return [];
        }

        return $addressArray;
    }

    public function getCompanyInformation(string $companyName)
    {
        return [
            // TODO: Make use of the API to get the company information
            'active_id_check' => true,
            'active_mf_service' => true
        ];
    }

    public function insertIntoKofaxPostItemsDatabase($companyName, $mailType, $companyNumber, $batchId, $batchName, $operator, $dryRun = false)
    {
        $formData = [];
        if ($companyName) {
            $formData['company_name'] = $companyName;
        }
        if ($mailType) {
            $formData['type_id'] = $mailType;
        }
        if ($companyNumber) {
            $formData['company_number'] = $companyNumber;
        }
        if ($batchId) {
            $formData['batch_id'] = $batchId;
        }
        if ($batchName) {
            $formData['batch_name'] = $batchName;
        }
        if ($operator) {
            $formData['agent'] = $operator;
        }
        $formData['dry_run'] = $dryRun;

        $response = $this->client->request('POST', $this->kofaxDatabaseUrl, [
            'headers' => $this->kofaxDatabaseAuthorization,
            'body' => $formData,
        ]);

        $responseBody = $response->getContent();

        if ($response->getStatusCode() >= Response::HTTP_BAD_REQUEST) {
            throw new Exception("Kofax API error! $responseBody");
        }

        try {
            return json_decode($responseBody, true);
        } catch (\Exception $e) {
            $error_message = "{$e->getClass()}: {$e->getMessage()}";
            trigger_error($error_message);
            return [
                'error' => $error_message,
                'response_body' => $responseBody,
            ];
        }
    }

    public function sendCourtLetterEmail(string $getCompanyNumber)
    {
        return $this->request(
            'GET',
            sprintf("%s/%s/", $this->courtLetterNotificationEndpoint, $getCompanyNumber)
        );
    }

    private function validateAddressArray(?array $addressArray): void
    {
        if (is_null($addressArray)) {
            throw new \Exception('Address not found');
        }

        if (
            (!isset($addressArray['address1']) && !isset($addressArray['address2']) && !isset($addressArray['address3']))
            || !isset($addressArray['town'])
            || !isset($addressArray['postcode'])
            || !isset($addressArray['country'])
        ) {
            throw new \Exception('Invalid address data');
        }
    }
}