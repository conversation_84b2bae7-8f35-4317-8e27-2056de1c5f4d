<?php

namespace App\Dto\Search;

class SearchQuery implements ISearchQuery
{
    const TYPE_COMPANY_NAME = 'COMPANY_NAME';
    const TYPE_OFFICER_NAME = 'OFFICER_NAME';
    const TYPE_LP_NUMBER = 'LP_NUMBER';
    const TYPE_COMPANY_NAME_AND_OFFICER_NAME = 'COMPANY_NAME_AND_OFFICER_NAME';
    const TYPE_COMPANY_NAME_AND_LP_NUMBER = 'COMPANY_NAME_AND_LP_NUMBER';

    const DB_CMS = 'cms';
    const DB_VO = 'vo';

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var string
     */
    private $officerName;

    /**
     * @var string
     */
    private $lpNumber;

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): SearchQuery
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getOfficerName(): ?string
    {
        return $this->officerName;
    }

    public function setOfficerName(string $officerName): SearchQuery
    {
        $this->officerName = $officerName;
        return $this;
    }

    public function getLpNumber(): ?string
    {
        return $this->lpNumber;
    }

    public function setLpNumber(string $lpNumber): SearchQuery
    {
        $this->lpNumber = $lpNumber;
        return $this;
    }

    public function getType(): string
    {
        $nonNullTypes = array_filter(
            [
                $this->companyName ? self::TYPE_COMPANY_NAME : NULL,
                $this->officerName ? self::TYPE_OFFICER_NAME : NULL,
                $this->lpNumber ? self::TYPE_LP_NUMBER : NULL,
            ]
        );

        return implode('_AND_', $nonNullTypes);
    }

    public function mapTypesToValues(): array
    {
        return [
            self::TYPE_COMPANY_NAME => $this->companyName,
            self::TYPE_OFFICER_NAME => $this->officerName,
            self::TYPE_LP_NUMBER => $this->lpNumber
        ];
    }
}
