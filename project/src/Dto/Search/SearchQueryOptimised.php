<?php

namespace App\Dto\Search;

class SearchQueryOptimised implements ISearchQuery
{
    /**
     * @var ISearchQuery
     */
    private $decorated;

    public function __construct(ISearchQuery $query)
    {
        $this->decorated = $query;
    }

    public static function from(ISearchQuery $query): self
    {
        return new self($query);
    }

    public function getCompanyName(): ?string
    {
        if ($name = $this->decorated->getCompanyName()) {
            return $this->toWildCards($name);
        }

        return NULL;
    }

    public function getOfficerName(): ?string
    {
        if ($name = $this->decorated->getOfficerName()) {
            return $this->toWildCards($name);
        }

        return NULL;
    }

    public function getLpNumber(): ?string
    {
        if ($lpNumber = $this->decorated->getLpNumber()) {
            return $this->removeLP($lpNumber);
        }

        return NULL;
    }

    private function toWildCards(string $value): string
    {
        return "%" . preg_replace("/[\s-]+/", "%", $value) . "%";
    }

    private function removeLP(string $value): string
    {
        if (substr($value, 0, 2) == 'LP') {
            return substr($value, strlen('LP'));
        }

        return $value;
    }
}
