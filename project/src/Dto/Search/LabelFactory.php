<?php

namespace App\Dto\Search;

use Utils\Helpers\ArrayHelper;

class LabelFactory
{
    public static function fromArray(array $data): Label
    {
        return new Label(
            ArrayHelper::get($data, 'id'),
            ArrayHelper::get($data, 'site'),
            ArrayHelper::get($data, 'companyName'),
            $data['serviceTYpe'] ?? NULL,
            ArrayHelper::get($data, 'operator'),
            ArrayHelper::get($data, 'dateScanned')
        );
    }
}