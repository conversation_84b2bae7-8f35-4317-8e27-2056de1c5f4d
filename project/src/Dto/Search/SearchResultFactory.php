<?php

namespace App\Dto\Search;

use Utils\Helpers\ArrayHelper;

class SearchResultFactory
{
    public static function createFromSqlData(array $data): SearchResult
    {
        return SearchResult::from(
            array_reduce(
                $data,
                function (array $result, $row) {
                    $result[] = new SearchResultRow(
                        ArrayHelper::get($row, 'id'),
                        ArrayHelper::get($row, 'type'),
                        ArrayHelper::get($row, 'name'),
                        $row['companyName'] ?? ArrayHelper::get($row, 'name'),
                        ArrayHelper::get($row, 'site'),
                        $row['serviceId'] ?? NULL,
                        self::extractService($row)
                    );

                    return $result;
                },
                []
            )
        );
    }

    private static function extractService(array $row)
    {
        $id = ArrayHelper::get($row, 'id');

        if (ArrayHelper::get($row, 'serviceType', FALSE)) {
            return CompanyServices::from(
                $id,
                [Service::from(
                    $id,
                    $row['serviceType'],
                    ArrayHelper::get($row, 'serviceStatus'),
                    NULL,
                    ArrayHelper::get($row, 'dateStart')
                )]
            );
        }
        return CompanyServices::empty($id);
    }
}
