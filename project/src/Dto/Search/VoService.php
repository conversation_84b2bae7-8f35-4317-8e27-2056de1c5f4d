<?php

namespace App\Dto\Search;

use CompanyModule\Contracts\IService;
use UserModule\Contracts\ICustomer;

class VoService implements IService
{
    const STATUS_ACTIVE = 'ACTIVE';
    const STATUS_SUSPENDED = 'SUSPENDED';
    const STATUS_LOST = 'LOST';

    /**
     * @var ICustomer
     */
    private $customer;

    /**
     * @var string
     */
    private $serviceName;

    /**
     * @var string
     */
    private $statusId;

    /**
     * @var int|null
     */
    private $renewalProductId;

    /**
     * @var int|null
     */
    private $companyId;

    /**
     * @param ICustomer $customer
     * @param string $serviceName
     * @param string $statusId
     * @param int|null $renewalProductId
     * @param int|null $companyId
     */
    public function __construct(ICustomer $customer, $serviceName, $statusId, $renewalProductId = NULL, $companyId = NULL)
    {
        $this->customer = $customer;
        $this->serviceName = $serviceName;
        $this->statusId = $statusId;
        $this->renewalProductId = $renewalProductId;
        $this->companyId = $companyId;
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return $this->statusId === self::STATUS_ACTIVE;
    }

    /**
     * @return bool
     */
    public function isOverdue()
    {
        return $this->statusId === self::STATUS_SUSPENDED;
    }

    /**
     * @return bool
     */
    public function isSuspended()
    {
        return $this->statusId === self::STATUS_LOST;
    }

    /**
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }

    /**
     * @return ICustomer
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * @return int|null
     */
    public function getRenewalProductId()
    {
        return $this->renewalProductId;
    }

    /**
     * @return bool
     */
    public function isPackageType()
    {
        return FALSE;
    }

    /**
     * @return string
     */
    public function getStatusId()
    {
        return $this->statusId;
    }

    /**
     * @return int|null
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }
}