<?php

namespace App\Dto\Search;

use Entities\Service;

class CompanyServiceEntities
{
    /**
     * @var string
     */
    private $companyId;

    /**
     * @var Service[]
     */
    private $serviceEntities;

    /**
     * @param string $companyId
     * @param Service[] $serviceEntities
     */
    public function __construct(string $companyId, array $serviceEntities)
    {
        $this->companyId = $companyId;
        $this->serviceEntities = $serviceEntities;
    }

    public static function from(string $companyId, array $serviceEntities): self
    {
        return new self($companyId, $serviceEntities);
    }

    public function getCompanyId(): string
    {
        return $this->companyId;
    }

    /**
     * @return Service[]
     */
    public function getServiceEntities(): array
    {
        return $this->serviceEntities;
    }

    /**
     * @return Service[][]
     * return format: [[serviceTypeId => Service]]
     */
    public function getServicesGroupedByType(): array
    {
        return array_reduce(
            $this->serviceEntities,
            function (array $grouped, Service $serviceEntity) {

                $current = $serviceEntity->getParent() ?? $serviceEntity;

                $grouped[$current->getServiceTypeId()][] = $current;
                return $grouped;
            },
            []
        );
    }
}
