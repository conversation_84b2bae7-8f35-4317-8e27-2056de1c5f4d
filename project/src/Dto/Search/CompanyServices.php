<?php

namespace App\Dto\Search;

class CompanyServices
{
    /**
     * @var string
     */
    private $companyId;

    /**
     * @var Service[]
     */
    private $services;

    public function __construct(string $companyId, array $services)
    {
        $this->companyId = $companyId;
        $this->services = $services;
    }

    public static function from(string $companyId, array $services): self
    {
        return new self($companyId, $services);
    }

    public static function empty(string $companyId): self
    {
        return self::from($companyId, []);
    }

    public function getCompanyId(): string
    {
        return $this->companyId;
    }

    public function getServices(): array
    {
        return $this->services;
    }

    public function sortByDateStartDesc(): self
    {
        $sorted = array_slice($this->services, 0);

        usort(
            $sorted,
            function (Service $a, Service $b) {
                if ($a->getDateStart() == $b->getDateStart()) {
                    return 0;
                }

                if ($a->getDateStart() && is_null($b)) {
                    return 1;
                }

                if (is_null($a) && $b->getDateStart()) {
                    return -1;
                }

                return ($a < $b) ? 1 : -1;
            }
        );

        return self::from($this->companyId, $sorted);
    }

    public function hasActiveService(): bool
    {
        /** @var Service $service */
        foreach ($this->services as $service) {
            if ($service->isActive()) {
                return TRUE;
            }
        }

        return FALSE;
    }

    public function isEmpty(): bool
    {
        return empty($this->services);
    }
}
