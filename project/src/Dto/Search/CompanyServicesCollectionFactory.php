<?php

namespace App\Dto\Search;

use Entities\Service;
use MailScanModule\Converters\CompanyServiceEntitiesConverter;

class CompanyServicesCollectionFactory
{
    /**
     * @var CompanyServiceEntitiesConverter
     */
    private $servicesConverter;

    public function __construct(CompanyServiceEntitiesConverter $serviceViewFactory)
    {
        $this->servicesConverter = $serviceViewFactory;
    }

    /**
     * @param Service[]
     * @return CompanyServicesCollection
     */
    public function fromServices(array $services): CompanyServicesCollection
    {
        $companyServiceEntitiesArr = CompanyServiceEntitiesFactory::arrayFromServicesArray($services);

        $servicesByCompanyArr = $this->servicesConverter->fromArrToServicesByCompanyArr($companyServiceEntitiesArr);

        return CompanyServicesCollection::from($servicesByCompanyArr);
    }
}
