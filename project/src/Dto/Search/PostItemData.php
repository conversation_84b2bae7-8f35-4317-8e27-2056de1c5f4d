<?php

namespace App\Dto\Search;

use CompanyModule\Contracts\IService;
use function FunctionalModule\Helpers\Object\map;
use function FunctionalModule\Helpers\Object\mapToArray;
use function FunctionalModule\Helpers\Object\pushToArray;
use function FunctionalModule\Transformations\callWith;
use function FunctionalModule\Transformations\hasAnyValue;
use function FunctionalModule\Transformations\hasValue;
use function FunctionalModule\Transformations\hasValues;
use function FunctionalPHP\FantasyLand\push_;
use IdModule\Emailers\IdEmailer;
use LoggableModule\Entities\EmailLog;
use MailScanModule\Emailers\PostDataEmailer;
use MailScanModule\Entities\PostItem;
use UserModule\Contracts\ICustomer;

class PostItemData
{
    const SERVICE_ACTIVE_WITH_ID_CHECK = 100010;
    const SERVICE_OVERDUE_WITH_ID_CHECK = 100011;
    const SERVICE_ACTIVE_NO_ID_CHECK = 100012;
    const SERVICE_OVERDUE_NO_ID_CHECK = 100013;
    const SERVICE_SUSPENDED = 100014;
    const SERVICE_DOES_NOT_EXIST = 100015;

    /**
     * @var IService
     */
    private $service;

    /**
     * @var string
     */
    private $statusToBe;

    /**
     * @var int
     */
    private $emailId;

    /**
     * @var PostItem
     */
    private $postItem;

    /**
     * @var ICustomer
     */
    private $customer;

    private function __construct()
    {
    }

    /**
     * @param int $emailId
     * @param PostItem $postItem
     * @param ICustomer $customer
     * @param IService $service
     * @param string|null $statusToBe
     * @return PostItemData
     */
    public static function fromService(int $emailId, PostItem $postItem, ICustomer $customer, IService $service, string $statusToBe = NULL)
    {
        $self = self::fromPostItem($emailId, $postItem, $customer);
        $self->service = $service;
        $self->statusToBe = $statusToBe;
        return $self;
    }

    /**
     * @param int $emailId
     * @param PostItem $postItem
     * @param ICustomer $customer
     * @return PostItemData
     */
    public static function fromPostItem(int $emailId, PostItem $postItem, ICustomer $customer)
    {
        $self = new self();
        $self->emailId = $emailId;
        $self->postItem = $postItem;
        $self->customer = $customer;
        return $self;
    }

    /**
     * @param PostItem $postItem
     * @param ICustomer $customer
     * @return PostItemData
     */
    public static function fromRacn(PostItem $postItem, ICustomer $customer)
    {
        $self = new self();
        $self->emailId = 0;
        $self->postItem = $postItem;
        $self->customer = $customer;
        $self->statusToBe = PostItem::STATUS_RELEASED;
        return $self;
    }

    /**
     * @return IService
     */
    public function getService(): ?IService
    {
        return $this->service;
    }

    /**
     * @return int
     */
    public function getEmailId(): int
    {
        return $this->emailId;
    }

    /**
     * @return PostItem
     */
    public function getPostItem(): PostItem
    {
        return $this->postItem;
    }

    /**
     * @return ICustomer
     */
    public function getCustomer(): ICustomer
    {
        return $this->customer;
    }

    /**
     * @return string|null
     */
    public function getCustomerEmail(): ?string
    {
        return $this->customer->getEmail();
    }

    /**
     * @return bool
     */
    public function canBeReleased(): bool
    {
        return $this->statusToBe === PostItem::STATUS_RELEASED && !$this->postItem->isReleased();
    }

    /**
     * @return bool
     */
    public function hasEmailBeenSent(): bool
    {
        $emailsToCheck = hasValue($this->getEmailId(), PostDataEmailer::EMAILS_VALID) ? PostDataEmailer::EMAILS_VALID : null;
        if (!$emailsToCheck) {
            $emailsToCheck = hasValue($this->getEmailId(), PostDataEmailer::EMAILS_INVALID) ? PostDataEmailer::EMAILS_INVALID : null;
        }
        return hasAnyValue(
            $emailsToCheck ? $emailsToCheck : [$this->getEmailId()],
            map(
                function (EmailLog $emailLog) {
                    return $emailLog->getEmailNodeId();
                },
                $this->postItem->getEmailsSent()
            )
        );
    }

    /**
     * @return bool
     */
    public function canSendEmail(): bool
    {
        return $this->getEmailId() > 0;
    }

    public function getCompanyId(): ?int
    {
        return $this->service ? $this->service->getCompanyId() : NULL;
    }

}