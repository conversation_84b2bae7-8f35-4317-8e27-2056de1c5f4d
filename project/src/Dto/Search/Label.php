<?php

namespace App\Dto\Search;

use DateTime;

class Label
{
    /**
     * @var string
     */
    private $companyNumber;

    /**
     * @var string
     */
    private $site;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var string
     */
    private $serviceType;

    /**
     * @var string
     */
    private $operator;

    /**
     * @var string
     */
    private $dateScanned;

    public function __construct(
        string $companyNumber,
        string $site,
        string $companyName,
        ?string $serviceType,
        string $operator,
        string $dateScanned
    )
    {
        $this->companyNumber = $companyNumber;
        $this->site = $site;
        $this->companyName = $companyName;
        $this->serviceType = $serviceType;
        $this->operator = $operator;
        $this->dateScanned = $dateScanned;
    }

    public function getCompanyNumber(): string
    {
        return $this->companyNumber;
    }

    public function getSite(): string
    {
        return '('.$this->site.')';
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function getServiceType(): ?string
    {
        return $this->serviceType ?? 'MAIL FORWARDING';
    }

    public function getOperator(): string
    {
        return $this->operator;
    }

    public function getDateScanned(): string
    {
        return $this->dateScanned;
    }

    public function toBarcode(): string
    {
        return implode(
            ' ',
            [
                $this->companyNumber,
//                $this->companyName,
//                $this->serviceType,
//                $this->operator,
//                $this->dateScanned->format('Y-m-d H:i:s')
            ]
        );
    }
}
