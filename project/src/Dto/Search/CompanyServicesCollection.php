<?php

namespace App\Dto\Search;

class CompanyServicesCollection
{
    /**
     * @var CompanyServices[]
     */
    private $servicesByCompany;

    /**
     * @param  CompanyServices[]
     */
    public function __construct(array $services)
    {
        $this->servicesByCompany = $services;
    }

    /**
     * @param  CompanyServices[]
     * @return self
     */
    public static function from(array $services): self
    {
        return new self($services);
    }

    public function getByCompanyId(int $companyId): CompanyServices
    {
        foreach ($this->servicesByCompany as $service) {
            if($service->getCompanyId() == $companyId) {
                return $service;
            }
        }

        return CompanyServices::empty($companyId);
    }

    public static function empty(): self
    {
        return self::from([]);
    }

    public function isEmpty(): bool
    {
        return empty($this->servicesByCompany);
    }
}
