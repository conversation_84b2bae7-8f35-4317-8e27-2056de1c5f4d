<?php

namespace App\Dto\Search;

class SearchResultRow
{
    const TYPE_COMPANY = 'COMPANY';
    const TYPE_PERSON = 'PERSON';

    const SITE_CMS = 'CMS';
    const SITE_VO = 'VO';

    /**
     * @var string
     */
    private $id;

    /**
     * @var int
     */
    private $serviceId;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var string
     */
    private $site;

    /**
     * @var CompanyServices
     */
    private $servicesCollection;

    public function __construct(
        string $id,
        string $type,
        string $name,
        string $companyName,
        string $site,
        int $serviceId = null,
        CompanyServices $services = null
    )
    {
        $this->id = $id;
        $this->type = $type;
        $this->name = $name;
        $this->companyName = $companyName;
        $this->site = $site;
        $this->serviceId = $serviceId;
        $this->servicesCollection = $services;
    }

    public static function from(
        string $id,
        string $type,
        string $name,
        string $companyName,
        string $site,
        int $serviceId = null,
        CompanyServices $services = null
    ) {
        return new self(
            $id,
            $type,
            $name,
            $companyName,
            $site,
            $serviceId,
            $services
        );
    }

    public function addServices(CompanyServices $services): self
    {
        return new self(
            $this->id,
            $this->type,
            $this->name,
            $this->companyName,
            $this->site,
            $this->serviceId,
            $services
        );
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getServiceId(): int
    {
        return $this->serviceId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function getSite(): string
    {
        return $this->site;
    }

    public function getServicesCollection(): CompanyServices
    {
        return $this->servicesCollection;
    }

    public function isCompanyType(): bool
    {
        return self::TYPE_COMPANY === $this->type;
    }

    public function isPersonType(): bool
    {
        return self::TYPE_PERSON === $this->type;
    }

    public function isCmsSite(): bool
    {
        return self::SITE_CMS === $this->site;
    }

    public function isVoSite(): bool
    {
        return self::SITE_VO === $this->site;
    }
}
