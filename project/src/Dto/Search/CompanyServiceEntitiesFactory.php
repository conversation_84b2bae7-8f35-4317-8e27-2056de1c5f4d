<?php

namespace App\Dto\Search;

use Entities\Service;
use models\Helpers\ServiceHelper;

class CompanyServiceEntitiesFactory
{
    /**
     * @param Service[] $services
     * @return CompanyServiceEntities[]
     */
    public static function arrayFromServicesArray(array $services): array
    {
        $resultArr = [];
        foreach (ServiceHelper::groupByCompany($services) as $companyId => $servicesArray) {
            $resultArr[] = CompanyServiceEntities::from($companyId, $servicesArray);
        }

        return $resultArr;
    }
}
