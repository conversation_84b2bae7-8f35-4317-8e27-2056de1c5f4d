<?php

namespace App\Dto\Search;

class SearchResult
{
    /**
     * @var SearchResultRow[]
     */
    private $rows;

    /**
     * @param SearchResultRow[]
     */
    public function __construct(array $rows)
    {
        $this->rows = $rows;
    }

    /**
     * @param  SearchResultRow[]
     * @return SearchResult
     */
    public static function from(array $row): self
    {
        return new self($row);
    }

    public static function empty(): self
    {
        return self::from([]);
    }

    public function union(SearchResult $that): self
    {
        return self::from(
            array_merge([], $this->getRows(), $that->getRows())
        );
    }

    /**
     * @return  SearchResultRow[]
     */
    public function getRows(): array
    {
        return $this->rows;
    }

    public function getCompanies(): iterable
    {
        foreach ($this->rows as $row) {
            if ($row->isCompanyType()) {
                yield $row;
            }
        }
    }

    public function getPeople(): iterable
    {
        foreach ($this->rows as $row) {
            if ($row->isPersonType()) {
                yield $row;
            }
        }
    }

    public function getCompanyIds(): array
    {
        return array_unique(
            array_map(
                function (SearchResultRow $row) {
                    return $row->getId();
                },
                $this->rows
            )
        );
    }

    public function filterByIds(array $ids): self
    {
        return self::from(
            array_values(
                array_filter(
                    $this->rows,
                    function (SearchResultRow $row) use ($ids) {
                        return in_array($row->getId(), $ids);
                    }
                )
            )
        );
    }

    public function mapServices(CompanyServicesCollection $services): self
    {
        return self::from(
            array_map(
                function (SearchResultRow $row) use ($services) {
                    return $row->addServices($services->getByCompanyId($row->getId()));
                },
                $this->rows
            )
        );
    }

    public function sortByService(): self
    {
        $rows = array_slice($this->rows, 0);

        usort(
            $rows,
            function (SearchResultRow $r1, SearchResultRow $r2) {
                if ($r1->getServicesCollection()->isEmpty() === $r2->getServicesCollection()->isEmpty()) {
                    return 0;
                }

                if ($r1->getServicesCollection()->isEmpty()) {
                    return 1;
                }

                return -1;
            }
        );

        return self::from($rows);
    }

    public function isEmpty(): bool
    {
        return empty($this->rows);
    }
}
