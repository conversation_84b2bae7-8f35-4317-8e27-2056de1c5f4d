<?php

namespace App\Dto\Search;

use DateTime;

class Service
{
    const STATUS_ACTIVE = 'ACTIVE';

    /**
     * @var string
     */
    private $companyId;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $status;

    /**
     * @var string|NULL
     */
    private $cssClass;

    /**
     * @var DateTime|NULL
     */
    private $dateStart;

    public function __construct(string $companyId, string $type, string $status, string $cssClass = NULL, DateTime $dateStart = NULL)
    {
        $this->companyId = $companyId;
        $this->type = $type;
        $this->status = $status;
        $this->cssClass = $cssClass;
        $this->dateStart = $dateStart;
    }

    public static function from(string $companyId, string $type, string $status, string $cssClass = NULL, DateTime $dateStart = NULL): self
    {
        return new self($companyId, $type, $status, $cssClass, $dateStart);
    }

    public static function empty()
    {
        return new self("","", "");
    }

    public function isEmpty(): bool
    {
        return $this->companyId === '';

    }

    public function isActive(): bool
    {
        return strtoupper($this->status) === self::STATUS_ACTIVE;
    }

    public function getCompanyId(): string
    {
        return $this->companyId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getCssClass(): ?string
    {
        return $this->cssClass;
    }

    public function getDateStart(): ?DateTime
    {
        return $this->dateStart;
    }
}
