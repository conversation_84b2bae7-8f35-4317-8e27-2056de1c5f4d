<?php

namespace App\Dto;

use InvalidArgumentException;
use Google\Cloud\PubSub\Message;

class Completed
{
   
    private string $pdfFilename;
    private string $companyName;
    private string $operationStatus;

    public function __construct(string $pdfFilename, $companyName, $operationStatus)
    {
        $this->pdfFilename = $pdfFilename;
        $this->companyName = $companyName;
        $this->operationStatus = $operationStatus;
    }

    public function getPdfFilename()
    {
        return $this->pdfFilename;
    }
    public function getCompanyName()
    {
        return $this->companyName;
    }
    public function getOperationStatus()
    {
        return $this->operationStatus;
    }

}
