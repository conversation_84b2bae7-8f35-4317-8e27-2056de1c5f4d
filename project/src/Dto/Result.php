<?php

namespace App\Dto;

use InvalidArgumentException;
use Google\Cloud\PubSub\Message;

class Result
{

    private array $completed;
    private Message $message;

    public function __construct(array $completed, Message $message)
    {
        $this->completed = $completed;
        $this->message = $message;
    }

    public function getCompleted(): array
    {
        return $this->completed;
    }

    public function getMessage(): Message
    {
        return $this->message;
    }

    public function getAckId(): string
    {
        return $this->message->ackId();
    }
}
