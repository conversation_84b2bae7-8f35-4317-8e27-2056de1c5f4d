<?php
namespace App\Dto;

use JsonSerializable;

class DiscordMessage implements JsonSerializable
{
    const TITLE_CHARACTERS_LIMIT = 60;
    const DESCRIPTION_CHARATERS_LIMIT = 190;
    const LEFT_BORDER_COLOR = 'FF3E3E';

    protected $title;
    protected $url;
    protected $description;
    protected $fields;
    protected $footer;
    protected $thumbnail;
    protected $color = self::LEFT_BORDER_COLOR;

    public function getTitle()
    {
        return $this->title;
    }

    public function setTitle($title, $short = false)
    {
        if($short) {
            $title = substr($title, 0, self::TITLE_CHARACTERS_LIMIT);
        }

        $this->title = $title;
        return $this;
    }

    public function getUrl()
    {
        return $this->url;
    }

    public function setUrl($url)
    {
        $this->url = $url;
        return $this;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function setDescription($description, $short = false)
    {
        if($short) {
            $description = substr($description, 0, self::DESCRIPTION_CHARATERS_LIMIT);
        }
        
        $this->description = $description;
        return $this;
    }

    public function getFields()
    {
        return $this->fields;
    }

    public function setFields($fields)
    {
        $this->fields = $fields;
        return $this;
    }

    public function getFooter()
    {
        return $this->footer;
    }

    public function setFooter($footer)
    {
        $this->footer = $footer;
        return $this;
    }

    public function getThumbnail()
    {
        return $this->thumbnail;
    }

    public function setThumbnail($thumbnail)
    {
        $this->thumbnail = $thumbnail;
        return $this;
    }

    public function getColor()
    {
        return $this->color;
    }

    public function setColor($color)
    {
        $this->color = $color;
        return $this;
    }

    public function jsonSerialize()
    {
        return [
            'tts' => false,
            'file' => null,
            'embeds' => [
                [
                    'type' => 'rich',
                    'color' => hexdec($this->getColor()),
                    'title' => $this->getTitle(),
                    'url' => $this->getUrl(),
                    'description' => $this->getDescription(),
                    'thumbnail' => [
                        'url' => $this->getThumbnail()
                    ],
                    'fields' => $this->getFields(),
                    'footer' => $this->getFooter(),
                    'timestamp' => date('c', strtotime('now'))
                ]
            ]
        ];
    }
}