<?php

namespace App\Dto;

use InvalidArgumentException;

class Topic
{
    public const TOPICS = [
        "mr_pdf_uploaded" => "mr_pdf_uploaded",
        "mr_ui_status" => "mr_ui_status",
        "mr_waiting_for_resolution" => "mr_waiting_for_resolution",
        "mr_predictions" => "mr_predictions",
        "mr_completed" => "mr_completed"
    ];

    private string $name;

    private function __construct($name)
    {
        $this->name = $name;
    }

    public static function fromString(string $topic): Topic
    {
        $topics = array_values(self::TOPICS);

        if (in_array($topic, $topics)) {
            return new Topic($topic);
        } else {
            throw new InvalidArgumentException(
                sprintf(
                    "Topic name %s is not allowed. Allowed topics are %s",
                    $topic,
                    json_encode($topics)
                )
            );
        }
    }

    public static function uiStatus(): Topic
    {
        return new Topic(self::TOPICS['mr_ui_status']);
    }

    public static function pdfUploaded(): Topic
    {
        return new Topic(self::TOPICS['mr_pdf_uploaded']);
    }

    public static function waitingForResolution(): Topic
    {
        return new Topic(self::TOPICS['mr_waiting_for_resolution']);
    }

    public static function mrPredictions(): Topic
    {
        return new Topic(self::TOPICS['mr_predictions']);
    }

    public static function mrCompleted(): Topic
    {
        return new Topic(self::TOPICS['mr_completed']);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function __toString()
    {
        return $this->getName();
    }
}
