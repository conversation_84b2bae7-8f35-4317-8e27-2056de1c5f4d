<?php

namespace App\Service;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Response;

class CSRF
{
    public const CSRF_TOKEN_COOKIE_NAME = 'X-CSRF-Token';

    public static function createCsrfCookie($tokenId, $key, $path="/"): Cookie
    {
        $name = self::CSRF_TOKEN_COOKIE_NAME;
        $cookieContent = JWT::encode(
            ['csrf_token' => hash('sha256', $key . ':' . $tokenId)],
            $tokenId,
            'HS256'
        );
        $expirationDate = strtotime('+1 day');
        $domain = isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'localhost';
        $secure = isset($_SERVER['REQUEST_SCHEME']) && $_SERVER['REQUEST_SCHEME'] === 'https';
        $httpOnly = false;
        $raw = true;
        $sameSitePolicy = 'Strict';

        return new Cookie(
            $name,
            $cookieContent,
            $expirationDate,
            $path,
            $domain,
            $secure,
            $httpOnly,
            $raw,
            $sameSitePolicy
        );
    }

    public static function validate($formToken, $cookieToken, $tokenId): bool
    {
        if ($formToken == $cookieToken) {
            try {
                JWT::decode($formToken, new Key($tokenId, 'HS256'));
                return true;
            } catch (\Exception $th) {
                return false;
            }
        }
        return false;
    }

    public static function destroyCsrfCookie(Response $response) : Response
    {
        $response->headers->removeCookie(self::CSRF_TOKEN_COOKIE_NAME);
        return $response;
    }
}