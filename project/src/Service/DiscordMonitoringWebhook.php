<?php
namespace App\Service;

use App\Dto\DiscordMessage;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Error, Exception, TypeError;

class DiscordMonitoringWebhook
{
    private const LOCAL_ENVIRONMENT_NAME = 'dev';

    private $client;
    private $dataBuilder;
    private $environment;
    private $logger;
    private $shortTrace = false;
    private $webhookUrl;

    public function __construct(
        string $environment,
        string $webhookUrl,
        DiscordMessage $dataBuilder,
        HttpClientInterface $client,
        LoggerInterface $logger
    ) {
        $this->client = $client;
        $this->dataBuilder = $dataBuilder;
        $this->environment = $environment;
        $this->logger = $logger;
        $this->webhookUrl = $webhookUrl;
    }

    private function isLocal()
    {
        return $this->environment === self::LOCAL_ENVIRONMENT_NAME;
    }

    public function shortTrace($shortTrace = true)
    {
        $this->shortTrace = $shortTrace;

        return $this;
    }

    public function trigger(Error|Exception|TypeError $exception)
    {
        if ($this->isLocal()) {
            return null;
        }

        try {
            $this->logger->error($exception->getMessage());
            $this->client->request(
                $method = 'POST',
                $this->webhookUrl,
                [
                    'body' => $this->buildData(
                        $exception->getMessage(),
                        $exception->getTraceAsString(),
                        $exception->getFile(),
                        $exception->getLine()
                    ),
                    'headers' => [
                        'Content-type: application/json'
                    ]
                ]
            );
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    private function buildData(
        string $message,
        string $trace,
        string $file,
        int $line
    ) {
        $this->dataBuilder->setTitle($message);
        $this->dataBuilder->setDescription($trace, $this->shortTrace);
        $this->dataBuilder->setFields([
            [
                'name' => 'File',
                'value' => $file,
                'inline' => true
            ],
            [
                'name' => 'Line',
                'value' => $line,
                'inline' => true
            ]
        ]);

        return json_encode($this->dataBuilder, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
}