<?php
namespace App\Service;

use Psr\Log\LoggerInterface;
use Error, Exception, TypeError;

class SaveExceptionToStorage
{
    private const EXCEPTIONS_STORAGE_PATH = '/var/exceptions/';
    private const LOCAL_ENVIRONMENT_NAME = 'dev';

    private $environment;
    private $exceptionsDirectory;
    private $logger;

    public function __construct(
        string $environment,
        string $projectDirectory,
        LoggerInterface $logger
    ) {
        $this->environment = $environment;
        $this->exceptionsDirectory = $projectDirectory . self::EXCEPTIONS_STORAGE_PATH;
        $this->logger = $logger;
    }
    public function save(Error|Exception|TypeError $exception)
    {
        if ($this->isLocal()) {
            return null;
        }

        try {
            $this->createDirectoryIfDoesntExist();
            file_put_contents(
                $this->getFilename(),
                $exception->getTraceAsString()
            );
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    private function isLocal()
    {
        return $this->environment === self::LOCAL_ENVIRONMENT_NAME;
    }

    private function createDirectoryIfDoesntExist()
    {
        if (!is_dir($this->exceptionsDirectory)) {
            mkdir(
                $this->exceptionsDirectory,
                $permissions = 0755,
                $recursive = true
            );
        }
    }

    private function getFilename()
    {
        $filename = uniqid() . '.html';

        return $this->exceptionsDirectory . $filename;
    }
}