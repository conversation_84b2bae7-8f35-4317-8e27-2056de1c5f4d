<?php

namespace App\Middleware;

use App\Repository\PostItem\PostItemEventsLogRepository;
use Exception;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelInterface;

/**
 * Blocks the access to routed symfony functions using the item id;
 */
class ActionBlocker implements HttpKernelInterface
{
    /**
     * The actions on this list cannot be taken twice or if
     * another one from the list has already been taken.
     */
    private const RESTRICTED_ACTIONS = [
        'post_collected',
        'address_label_generated',
        'post_returned_to_sender'
    ];

    /**
     * The paths of the actions on which to perform the checks.
     */
    private const ACTION_PATHS = [
        '/Collected',
        '/direct/post/items/forward/',
        '/RTS/'
    ];

    private HttpKernelInterface $httpKernel;
    private PostItemEventsLogRepository $eventRepository;
    private RouterInterface $routerInterface;

    public function __construct(
        HttpKernelInterface $httpKernel,
        PostItemEventsLogRepository $postItemEventsLogRepository,
        RouterInterface $routerInterface
    )
    {
        $this->httpKernel = $httpKernel;
        $this->eventRepository = $postItemEventsLogRepository;
        $this->routerInterface = $routerInterface;
    }

    /**
     * Main function that gets run everytime a route is accessed within the app.
     * Responsible for blocking the user from taking a restricted action for an
     * item if one has already been taken.
     */
    public function handle(Request $request, $type = HttpKernelInterface::MAIN_REQUEST, $catch = true) : Response
    {
        $uri = $request->getPathInfo();
        $route = $this->getRouteData($uri);

        if (!$route || !$this->isTrackedAction($uri)){
            return $this->httpKernel->handle($request, $type, $catch);
        }

        $itemId = $this->getIdFromRoute($route);

        if (!$itemId){
            return $this->httpKernel->handle($request, $type, $catch);
        }

        if( $this->hasTakenRestrictedAction($itemId) ) {
            return new RedirectResponse('/direct/post/items');    
        }

        return $this->httpKernel->handle($request, $type, $catch);
    }

    /**
     * Returns a route array with the attributes of the given uri route.
     */
    private function getRouteData ($uri)
    {
        try {
            return $this->routerInterface->match($uri);
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Returns whether the path is a tracked action or not
     */
    private function isTrackedAction ($uri) : bool
    {
        foreach (self::ACTION_PATHS as $path) {
            if (str_contains($uri, $path)){
                return true;
            }
        }
        return false;
    }

    /**
     * Gets the post item id from a given route.
     * Returns false if there is no id in the route.
     */
    private function getIdFromRoute ($route)
    {
        if (isset($route['id'])){
            return $route['id'];
        }
        return false;
    }

    /**
     * Returns whether the item has already had a restricted action taken upon.
     */
    private function hasTakenRestrictedAction ($itemId) : bool
    {
        $itemEvents = $this->eventRepository->findBy([ 'postItemId' => $itemId ]);
        return !!array_filter($itemEvents, function($event){
            return in_array($event->getEventName(), self::RESTRICTED_ACTIONS);
        });
    }
}