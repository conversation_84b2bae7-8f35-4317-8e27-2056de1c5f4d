<?php

namespace App\Middleware;

use App\Controller\ApiController;
use Exception;
use stdClass;
use App\Client\JWTAuthorization;
use Firebase\JWT\ExpiredException;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\Event\ResponseEvent;


/**
 * Listerner for all the requests that are sent to the Api
 * This Listener is responsible for verifiy the authorization token
 * sent by the user
 */
class Listener
{
    /** @var string $basicAuthToken */
    private string $basicAuthToken;

    /** @var JWTAuthorization $JWTAuthorization */
    private JWTAuthorization $JWTAuthorization;

    private const HOME_ROUTE = '/';

    private const NO_AUTH_ROUTES = [
        '/test-cors',
        '/api/session/end',
        '/logout',
        '/api/isserverup',
    ];

    private const EXTERNAL_API_ROUTES = [
        '/api/add-post-item-detail',
        '/api/add-post-item-event',
        '/api/add-post-item-external',
        '/api/change-item-status',
        '/api/get-ai-report',
        '/api/get-parcel-transit-fee',
        '/api/get-post-item',
        '/api/get-post-items',
        '/api/get-released-non-statutory-post-items',
        '/api/get-unpaid-forwarded-items',
        '/api/get-unprocessed-post-items',
        '/api/get-require-update-post-items',
        '/api/get-failed-payment-post-items',
        '/api/get-waiting-non-statutory-post-items',
        '/api/mark-items-as-charged',
        '/api/mark-item-as-purchased',
        '/api/set-last-email-sent',
        '/api/set-post-item-as-released',
        '/api/get-last-30days-company-purchases-count',
    ];

    private array $firebaseConfig;

    /** @var Session $session */
    private Session $session;

    /**
     * The define in the config/services.yaml
     * @param $basicAuthToken
     */
    public function __construct($basicAuthToken,JWTAuthorization $JWTAuthorization)
    {
        $this->basicAuthToken = $basicAuthToken;
        $this->JWTAuthorization = $JWTAuthorization;

        $this->firebaseConf = [
            'key' => $_ENV['FIREBASE_KEY'],
            'project_id' => $_ENV['FIREBASE_PROJECT_ID'],
            'iss_pattern' => $_ENV['FIREBASE_ISS_PATTERN'],
            'public_keys_link' => $_ENV['FIREBASE_PUBLIC_KEYS_LINK'],
        ];
    }

    /**
     * @param ControllerEvent $event
     * @return void
     */
    public function onKernelController(ControllerEvent $event)
    {
        if (!$event->isMainRequest()) {
            // don't do anything if it's not the main request
            return;
        }

        $request = $event->getRequest();
        $this->session = $request->getSession();
        $uri = $request->getPathInfo();

        // Validation used to exclude the listener from HOME_ROUTE and the ones listed in NO_AUTH_ROUTES.
        // This, for example, allows the route / to be rendered normally.
        if ($uri == self::HOME_ROUTE || in_array(rtrim($uri, '/'), self::NO_AUTH_ROUTES)) {
            return;
        }

        if (in_array(rtrim($uri, '/'), self::EXTERNAL_API_ROUTES)) {
            $result = $this->validateExternalApi($request);
            if (!$result['valid']) {
                $event->setController(function() use ($result) {
                    $this->session->getFlashBag()->add('warning', $result['message']);
                    return $this->redirectToLogout($result['message']);
                });
            }
            return;
        }

        $authToken = $this->JWTAuthorization->getAuthToken($request);
        
        if (empty($authToken)) {
            $event->setController(function() {
                $this->session->getFlashBag()->add('warning', 'Authorization token not provided');
                return $this->redirectToLogout();
            });
            return;
        }

        $refreshToken = $this->JWTAuthorization->getRefreshToken($request);

        $auth = $this->JWTAuthorization->validateToken($authToken);

        if (!$auth['valid']) {
            [$authToken,$refreshToken] = $this->JWTAuthorization->refreshToken($refreshToken);

            if (empty($authToken)) {
                $event->setController(function() {
                    $this->session->getFlashBag()->add('warning', 'Authorization token does not exist');
                    return $this->redirectToLogout();
                });
                return;
            }

            $auth = $this->JWTAuthorization->validateToken($authToken);
        }

        if (!$auth['valid']){
            $event->setController(function() use ($auth){
                $this->session->getFlashBag()->add('warning', $auth['message']);
                return $this->redirectToLogout();
            });
            return;
        }

        

        // Use session as inter event/listener/controller communication
        $sessionData = $this->session->get('user');
        $sessionData['accessToken'] = $authToken;
        $sessionData['refreshToken'] = $refreshToken;
        $sessionData['userEmail'] = $auth['data']->email;

        $this->session->set('user', $sessionData);
    }


    /**
     * @param ResponseEvent $event
     * @return void
     */
    public function onKernelResponse(ResponseEvent $event)
    {
        if (!$event->isMainRequest()) {
            // don't do anything if it's not the main request
            return;
        }

        $request = $event->getRequest();
        $response = $event->getResponse();
        $this->session = $request->getSession();

        
        if (!in_array($response->getStatusCode(),[
            Response::HTTP_FORBIDDEN,
            Response::HTTP_UNAUTHORIZED
        ])){

            $sessionData = $this->session->get('user');

            if (isset($sessionData['accessToken'])){
                $cookie = $this->JWTAuthorization->createCookie(JWTAuthorization::AUTH_COOKIE_NAME, $sessionData['accessToken']);
                $response->headers->setCookie($cookie);
            }


            if (isset($sessionData['refreshToken'])){
                $cookie = $this->JWTAuthorization->createCookie(JWTAuthorization::REFRESH_TOKEN_COOKIE_NAME, $sessionData['refreshToken']);
                $response->headers->setCookie($cookie);
            }
            return $response;

        }

        $this->JWTAuthorization->destroyCookie($request,$response);
        $this->JWTAuthorization->destroyCookie($request,$response,JWTAuthorization::REFRESH_TOKEN_COOKIE_NAME);
        $this->session->invalidate();
        return $response;
    }

    /**
     * @return RedirectResponse
     */
    private function redirectToLogout($message = 'You must log in to access Mailroom.'): RedirectResponse
    {
        $route = '/logout?%s=%s';
        $key = 'redAlert';
        $value = $message;
        return new RedirectResponse(sprintf($route, $key, $value));
    }

    private function validateExternalApi(Request $request){
        $token = $request->headers->get('x-api-auth');
        return $this->JWTAuthorization->validateApiToken($token);
    }
}