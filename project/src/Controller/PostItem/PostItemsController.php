<?php

namespace App\Controller\PostItem;

use stdClass;
use App\Client\CompaniesMadeSimple;
use App\Client\JWTAuthorization;
use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemsDetail;
use App\Entity\PostItem\PostItemEventsLog;
use App\Form\AddItemForm;
use App\Form\BulkCollectItemForm;
use App\Form\CollectItemForm;
use App\Form\ForwardItemForm;
use App\Form\LogRTSForm;
use App\Form\SecurelyDestroyForm;
use App\Form\PostItemFilteringTypeForm;
use App\Form\PostItemFilteringStatusForm;
use App\Form\PostItemSearchForm;
use App\Form\PostItemLimitForm;
use App\Model\AddPostItem;
use App\Model\CollectItem;
use App\Model\ForwardItem;
use App\Model\RTSItem;
use App\Model\SecurelyDestroyItem;
use App\Service\CSRF;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use DateTime;
use DateTimeZone;

class PostItemsController extends AbstractController
{
    /** @var CompaniesMadeSimple $companiesMadeSimple */
    private $companiesMadeSimple;

    /** @var ManagerRegistry $doctrine */
    private $doctrine;

    /** @var RequestStack $requestStack */
    private $request;

    /** @var PostItemRepository $postItemRepository */
    private $postItemRepository;

    /** @var PostItemDetailsRepository $postItemDetailsRepository */
    private $postItemDetailsRepository;

    /** @var PostItemEventsLogRepository $postItemEventsLogRepository */
    private $postItemEventsLogRepository;

    /** @var JWTAuthorization $JWTAuthorization */
    private $JWTAuthorization;

    public function __construct(CompaniesMadeSimple $companiesMadeSimple, ManagerRegistry $doctrine, RequestStack $requestStack, JWTAuthorization $JWTAuthorization)
    {
        $this->companiesMadeSimple = $companiesMadeSimple;
        $this->doctrine = $doctrine;
        $this->postItemRepository = $doctrine->getRepository(PostItem::class);
        $this->postItemDetailsRepository = $doctrine->getRepository(PostItemsDetail::class);
        $this->postItemEventsLogRepository = $doctrine->getRepository(PostItemEventsLog::class);
        $this->request = $requestStack->getCurrentRequest();
        $this->JWTAuthorization = $JWTAuthorization;
    }

    /**
     * @Route("/direct/post/items/{page}", name="app_post_items", defaults={"page": 1}, requirements={"page": "\d+"})
     * @return Response
     */
    public function index(): Response
    {
        $response = new Response();
        $defaultLimit = 20;
        $defaultPage = 1;
        $tokenId = 'index';

        $filter = $this->getQueryFilter();
        $filter['limit'] = isset($filter['limit']) ? $filter['limit'] : $defaultLimit;
        $filter['page'] = isset($filter['page']) ? $filter['page'] : $defaultPage;


        $searchForm = $this->createForm(PostItemSearchForm::class);
        if ($this->isFormValid($searchForm)) {
            $filter = $this->setupFilter($searchForm, $filter);
            $filter['page'] = 1;
            return $this->redirectToRoute('app_post_items', $filter);
        }

        $filteringTypeForm = $this->createForm(PostItemFilteringTypeForm::class);
        if ($this->isFormValid($filteringTypeForm)) {
            $filter['type'] = $filteringTypeForm->get('type')->getData();
            $filter['page'] = 1;
            return $this->redirectToRoute('app_post_items', $filter);
        }

        $filteringStatusForm = $this->createForm(PostItemFilteringStatusForm::class);
        if ($this->isFormValid($filteringStatusForm)) {
            $filter['status'] = $filteringStatusForm->get('status')->getData();
            $filter['page'] = 1;
            return $this->redirectToRoute('app_post_items', $filter);
        }

        $limitForm = $this->createForm(PostItemLimitForm::class);
        if ($this->isFormValid($limitForm, $tokenId)) {
            $filter['limit'] = $limitForm->get('limit')->getData();
            $filter['page'] = 1;
            return $this->redirectToRoute('app_post_items', $filter);
        }

        $postItemsList = $this->postItemRepository->findByCompanyNameOrCompanyNumber($filter);

        $count = $this->postItemRepository->count($filter);

        $RTSForm = $this->createForm(LogRTSForm::class);
        if ($this->isFormValid($RTSForm, $tokenId)) {
            return $this->redirectToRoute(
                'app_post_RTS_log_action',
                array_merge(
                    ['id' => $RTSForm->get('item_id')->getData()],
                    $filter
                )
            );
        }

        $securelyDestroyForm = $this->createForm(SecurelyDestroyForm::class);
        if ($this->isFormValid($securelyDestroyForm, $tokenId)) {
            return $this->redirectToRoute(
                'app_post_securely_destroy_action',
                array_merge(
                    ['id' => $securelyDestroyForm->get('item_id')->getData()],
                    $filter
                )
            );
        }

        $collectedForm = $this->createForm(CollectItemForm::class);
        if ($this->isFormValid($collectedForm, $tokenId)) {
            return $this->redirectToRoute(
                'app_post_collected_action',
                array_merge(
                    [
                        'id'                => $collectedForm->get('item_id')->getData(),
                        'collected_name'    => $collectedForm->get('collected_name')->getData(),
                        'collected_number'  => $collectedForm->get('collected_number')->getData(),
                    ],
                    $filter
                )
            );
        }

        $bulkCollectedForm = $this->createForm(BulkCollectItemForm::class);
        if ($this->isFormValid($bulkCollectedForm, $tokenId)) {
            return $this->redirectToRoute(
                'app_post_bulk_collected_action',
                array_merge(
                    [
                        'id_list'           => $bulkCollectedForm->get('selected_items')->getData(),
                        'collected_name'    => $bulkCollectedForm->get('collected_name')->getData(),
                        'collected_number'  => $bulkCollectedForm->get('collected_number')->getData(),
                    ],
                    $filter
                )
            );
        }

        $this->addToFlashFormErrors($bulkCollectedForm);
        $this->addToFlashFormErrors($collectedForm);
        $this->addToFlashFormErrors($RTSForm);
        $this->addToFlashFormErrors($securelyDestroyForm);

        $template = 'post_items/index.html.twig';

        $cookieCSRF = $this->createCsrf($response, $tokenId);

        $forms = $this->setupIndexPageForms($filter, $cookieCSRF);

        $offset = $this->calculateOffset($filter);

        return $this->renderForm(
            $template,
            array_merge(
                [
                    'postItemList' => array_map(function($postItem) {
                        return $postItem->toArray();
                    }, $postItemsList),
                    'totalPages' => (int) ceil($count / $filter['limit']),
                    'filter' => $filter,
                    'offset' => $offset,
                    'total' => $count,
                    'last_register_on_page' => $this->getLastRegisterOnPage($filter['limit'], $offset, $count),
                    'blockedActions' => json_decode(file_get_contents($this->getParameter('restricted_actions')))
                ],
                $forms
            ),
            $response
        );
    }

    /**
     * @Route("/direct/post/items/forward/{id}", name="app_post_items_forward")
     * @return Response
     */
    public function forwardItem(): Response
    {
        $filter = $this->getQueryFilter();
        $tokenId = 'forward';

        if (empty($id = $this->request->attributes->get('id'))) {
            return $this->redirectToRoute('app_post_items');
        }

        $postItem = $this->postItemRepository->findOneById($id);
        if (empty($postItem)) {
            return $this->redirectToRoute('app_post_items');
        }

        $alreadyForwarded = $postItem->getStatus() == PostItem::STATUS_FORWARDED;

        $form = $this->createForm(ForwardItemForm::class, [], [
            'postItem' => $postItem
        ]);
        if ($this->isFormValid($form, $tokenId) && !$alreadyForwarded) {
            return $this->saveForwardItemAndRefresh($form, $postItem, $filter);
        }
        $this->addToFlashFormErrors($form);

        $template = 'post_items/forward.html.twig';

        $response = new Response();

        return $this->renderForm(
            $template,
            [
                'form' => $this->createForm(ForwardItemForm::class, [
                    'csrf_token' => $this->createCsrf($response, $tokenId)->getValue()
                ], [
                    'postItem' => $postItem,
                    'alreadyForwarded' => $alreadyForwarded
                ]),
                'postItem' => array_merge(
                    $postItem->toArray(),
                    [
                        'address' => $this->companiesMadeSimple->getCompanyAddressByCompanyNumber(
                            $postItem->getCompanyNumber()
                        )
                    ]
                ),
                'alreadyForwarded' => $alreadyForwarded,
                'filter' => $filter
            ],
            $response
        );
    }

    /**
     * @Route("/direct/post/label/{label_item_id}/{postage_class}", name="app_post_items_label")
     * @return Response
     */
    public function postItemLabel(): Response
    {
        if (empty($id = $this->request->attributes->get('label_item_id'))) {
            return $this->redirectToRoute('app_post_items');
        }

        if (!$this->request->isMethod('GET')) {
            return $this->redirectToRoute('app_post_items_forward', ['id' => $id]);
        }

        $postItem = $this->postItemRepository->findOneById($id);
        if (empty($postItem)) {
            return $this->redirectToRoute('app_post_items');
        }

        return $this->render('post_items/forward-item-print.html.twig', [
            "labelImage" => ForwardItem::getLabelImage($this->request->attributes->get('postage_class')),
            "postItem" => array_merge(
                $postItem->toArray(),
                [
                    'address'=> $this->companiesMadeSimple->getCompanyAddressByCompanyNumber(
                        $postItem->getCompanyNumber()
                    )
                ]
            ),
        ]);
    }

    /**
     * @Route("direct/post/items/{page}/{id}/RTS/", name="app_post_RTS_log_action")
     * @return Response
     */
    public function RTSItem(): Response
    {
        $filter = $this->getQueryFilter();
        $response = $this->redirectToRoute('app_post_items', $filter);

        if (empty($id = $this->request->attributes->get('id'))) {
            $this->addFlash('error', 'no post item ID provided');
            return $response;
        }

        $postItem = $this->postItemRepository->findOneById($id);
        if (empty($postItem)) {
            $this->addFlash('error', 'no post item that matches the given ID was found');
            return $response;
        }

        $postItem->setStatus(PostItem::STATUS_RTS_D);

        $rtsItem = new RTSItem(
            $this->doctrine->getManager(),
            $postItem,
            $this->getOperator()
        );
        if (!$rtsItem->execute()->hasAnyError()) {
            return $response;
        }

        foreach($rtsItem->getErrors() as $error) {
            $this->addFlash('error', $error);
        }

        return $response;
    }

    /**
     * @Route("direct/post/items/{page}/{id}/securelyDestroy/", name="app_post_securely_destroy_action")
     * @return Response
     */
    public function securelyDestroyItem(): Response
    {
        $filter = $this->getQueryFilter();
        $response = $this->redirectToRoute('app_post_items', $filter);

        if (empty($id = $this->request->attributes->get('id'))) {
            $this->addFlash('error', 'no post item ID provided');
            return $response;
        }

        $postItem = $this->postItemRepository->findOneById($id);
        if (empty($postItem)) {
            $this->addFlash('error', 'no post item that matches the given ID was found');
            return $response;
        }

        $postItem->setStatus(PostItem::STATUS_SECURELY_DESTROYED);

        $securelyDestroyItem = new SecurelyDestroyItem(
            $this->doctrine->getManager(),
            $postItem,
            $this->getOperator()
        );
        if (!$securelyDestroyItem->execute()->hasAnyError()) {
            return $response;
        }

        foreach($securelyDestroyItem->getErrors() as $error) {
            $this->addFlash('error', $error);
        }

        return $response;
    }

    /**
     * @Route("direct/post/items/{page}/{id}/{collected_name}/{collected_number}/Collected", name="app_post_collected_action")
     * @return Response
     */
    public function ItemCollected(): Response
    {
        $filter = $this->getQueryFilter();
        $response = $this->redirectToRoute('app_post_items', $filter);

        $id = $this->request->attributes->get('id');
        $collectedName = $this->request->attributes->get('collected_name');
        $collectedNumber = $this->request->attributes->get('collected_number');

        if (empty($id)) {
            $this->addFlash('error', "No ID provided for the item to be marked as collected");
            return $response;
        }

        if (empty($collectedName)) {
            $this->addFlash('error', "No name provided for the item's collector");
            return $response;
        }

        if (empty($collectedNumber)) {
            $this->addFlash('error', "No ID provided for the item's collector");
            return $response;
        }

        $postItem = $this->postItemRepository->findOneById($id);

        if (empty($postItem)) {
            $this->addFlash('error', `Post item with ID: $id not found;`);
            return $response;
        }

        $collectItem = new CollectItem(
            $this->doctrine->getManager(),
            $postItem,
            [
                'collected_name' => $collectedName,
                'collected_number' => $collectedNumber
            ],
            $this->getOperator()
        );

        if(!$collectItem->execute()->hasAnyError()) {
            $postItem->setStatus(PostItem::STATUS_COLLECTED);
            return $response;
        }

        foreach ($collectItem->getErrors() as $error) {
            $this->addFlash('error', $error);
        }

        return $response;
    }

    /**
     * @Route("direct/post/items/{collected_name}/{collected_number}/Collected", name="app_post_bulk_collected_action")
     * @return Response
     */
    public function bulkItemCollected(): Response
    {
        $filter = $this->getQueryFilter();
        $response = $this->redirectToRoute('app_post_items', $filter);

        $idListString = $this->request->get('id_list');
        $idList = explode(',', $idListString);
        $collectedName = $this->request->attributes->get('collected_name');
        $collectedNumber = $this->request->attributes->get('collected_number');

        if (empty($idList)) {
            $this->addFlash('error', "No ID(s) provided for the item(s) to be marked as collected");
            return $response;
        }

        if (empty($collectedName)) {
            $this->addFlash('error', "No name provided for the item's collector");
            return $response;
        }

        if (empty($collectedNumber)) {
            $this->addFlash('error', "No ID provided for the item's collector");
            return $response;
        }

        foreach ($idList as $id) {
            $postItem = $this->postItemRepository->findOneById($id);
            if (empty($postItem)) {
                $this->addFlash('error', `Post item with ID: $id not found;`);
                continue;
            }

            $collectItem = new CollectItem(
                $this->doctrine->getManager(),
                $postItem,
                [
                    'collected_name' => $collectedName,
                    'collected_number' => $collectedNumber
                ],
                $this->getOperator()
            );

            if(!$collectItem->execute()->hasAnyError()) {
                foreach ($collectItem->getErrors() as $error) {
                    $this->addFlash('error', $error);
                }
                continue;
            }
        }

        return $response;
    }

    /**
     * @Route("/direct/post/items/add", name="app_post_items_add")
     * @return Response
     */
    public function addItem(): Response
    {
        $tokenId = 'addItem';
        $filter = $this->getQueryFilter();
        $addItemForm = $this->createForm(AddItemForm::class, ['is_production' => $this->getParameter('environment') == 'prod']);
        $companyToSearch = $this->request->get('companyToSearch', '');
        $presetIDCheck = $this->request->get('activeIDCheck', '');
        $presetMFService = $this->request->get('activeMFService', '');
        $companyList = $this->getCompanyList($companyToSearch);

        if ($this->isFormValid($addItemForm, $tokenId)) {
            $multipleAdditions = $addItemForm->get('add_multiple')->getData();
            if ($multipleAdditions && $multipleAdditions > 1) {
                for ($i = 0; $i < $multipleAdditions; $i++) {
                    $this->saveItem($addItemForm);
                }
                return $this->redirectToRoute('app_post_items');
            }

            if ($addItemForm->get('type')->getData() == 'parcel') {
                return $this->handleParcelSubmission($addItemForm, $filter, $companyToSearch, $presetIDCheck, $presetMFService);
            }

            return $this->saveItemThenRedirect($addItemForm, $filter);
        }
        $this->addToFlashFormErrors($addItemForm);

        $template = 'post_items/add-post-item.html.twig';

        if($companyName = $this->getCompanyNameFromFilterForm()) {
            return $this->redirectToRoute('app_post_items_add', array_merge($filter, ['companyToSearch' => $companyName]));
        }

        $searchForm = $this->createForm(PostItemSearchForm::class);
        $this->setSearchFormFields($searchForm, ['companyName' => $companyToSearch]);

        $response = new Response();
        $csrfCookie = $this->createCsrf($response, $tokenId);
        $addItemForm = $this->createForm(AddItemForm::class, [
            'csrf_token' => $csrfCookie->getValue(),
            'is_production' => $this->getParameter('environment') == 'prod'
        ]);
        return $this->renderForm(
            $template,
            array_merge(
                [
                    'searchForm' => $searchForm,
                    'addItemForm' => $addItemForm,
                    'companyList' => $companyList,
                    'companyNotFound'  => $companyToSearch && empty($companyList),
                    'filter' => $filter
                ]
            ),
            $response
        );
    }

    private function addToFlashFormErrors(FormInterface $form): void
    {
        if ($form->isSubmitted() && !$form->isValid()) {
            foreach ($form as $field => $fieldData) {
                $fieldErrors = $fieldData->getErrors();
                if (!empty((string) $fieldErrors)) {
                    // converting field form example_example to Example Example
                    $fieldValue = mb_convert_case(str_replace("_", " ", $field), MB_CASE_TITLE);
                    $this->addFlash('error', $fieldValue . ": " . $fieldErrors->current()->getMessage());
                }
            }
        }
    }

    private function calculateOffset($filter): int
    {
        return $filter['limit'] * ($filter['page'] - 1) + 1;
    }

    private function createCsrf(Response $response, $tokenId): Cookie
    {
        CSRF::destroyCsrfCookie($response);
        $csrfCookie = CSRF::createCsrfCookie($tokenId, random_bytes(32));
        $response->headers->setCookie($csrfCookie);
        return $csrfCookie;
    }

    private function getCompanyList($companyToSearch): array
    {
        $companyList = [];
        if ($companyToSearch) {
            $response = $this->companiesMadeSimple->companySearch('companyName=' . $companyToSearch);
            if($response->getStatusCode() == Response::HTTP_OK) {
                $companyList = json_decode($response->getContent());
            }
        }
        return $companyList;
    }

    private function getCompanyNameFromFilterForm(): ?string
    {
        $form = $this->createForm(PostItemSearchForm::class);
        if ($this->isFormValid($form)) {
            return $form->get('companyName')->getData();
        }
        return null;
    }

    private function getLastRegisterOnPage(int $limit, int $offset, int $count): int
    {
        return ($offset + $limit -1) > $count ? $count : $offset + $limit - 1;
    }

    private function getOperator(): ?stdClass
    {
        return $this->JWTAuthorization->getUser($this->request);
    }

    private function getQueryFilter(): array
    {
        $filter = [];
        if (!empty($companyName = $this->request->get('companyName'))) {
            $filter['companyName'] = $companyName;
        }
        if (!empty($companyNumber = $this->request->get('companyNumber'))) {
            $filter['companyNumber'] = $companyNumber;
        }
        if (!empty($type = $this->request->get('type'))) {
            $filter['type'] = $type;
        }
        if (!empty($status = $this->request->get('status'))) {
            $filter['status'] = $status;
        }
        if (!empty($limit = $this->request->get('limit'))) {
            $filter['limit'] = $limit;
        }
        if (!empty($page = $this->request->get('page'))) {
            $filter['page'] = $page;
        }
        if (!empty($sortBy = $this->request->get('sortBy'))) {
            $filter['sortBy'] = $sortBy;
        }
        if (!empty($direction = $this->request->get('direction'))) {
            $filter['direction'] = $direction;
        }
        return $filter;
    }

    private function isCsrfValid($form, $tokenId): bool
    {
        $formToken = $form->get('csrf_token')->getData();
        $cookieToken = $this->request->cookies->get(CSRF::CSRF_TOKEN_COOKIE_NAME);
        return CSRF::validate($formToken, $cookieToken, $tokenId);
    }

    private function isFormValid($form, $tokenId = null): bool
    {
        $form->handleRequest($this->request);
        if (!$form->isSubmitted() || !$form->isValid() || empty($form->getData())) {
            return false;
        }
        if($tokenId and !$this->isCsrfValid($form, $tokenId)) {
            return false;
        }
        return true;
    }
    private function saveForwardItemAndRefresh($form, $postItem, $filter)
    {
        $forwardItem = new ForwardItem(
            $this->doctrine->getManager(),
            $postItem,
            $form->getData(),
            $this->getOperator()
        );

        if (!$forwardItem->execute()->hasAnyError()) {
            $postItem->setStatus(PostItem::STATUS_FORWARDED);
            return $this->redirectToRoute(
                'app_post_items_forward',
                array_merge(
                    ['id' => $postItem->getId()],
                    $filter
                )
            );
        }
        foreach($forwardItem->getErrors() as $error) {
            $this->addFlash('error', $error);
        }
    }

    private function saveItemAdditionalInfo(FormInterface $form, PostItem $postItem) {
        $formData = $form->getData();

        if (isset($formData['length']) && isset($formData['width']) && isset($formData['height'])) {
            $dimensions = sprintf('%s x %s x %s', $formData['length'], $formData['width'], $formData['height']);

            $postItemDetails = new PostItemsDetail();
            $postItemDetails->setPostItemId($postItem);
            $postItemDetails->setKey('dimensions');
            $postItemDetails->setValue($dimensions);
            $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

            $this->doctrine->getManager()->persist($postItemDetails);
            $this->doctrine->getManager()->flush();
        }

        if (isset($formData['total_weight']) && $formData['total_weight'] !== null) {
            $postItemDetails = new PostItemsDetail();
            $postItemDetails->setPostItemId($postItem);
            $postItemDetails->setKey('total_weight');
            $postItemDetails->setValue($formData['total_weight']);
            $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

            $this->doctrine->getManager()->persist($postItemDetails);
            $this->doctrine->getManager()->flush();
        }

        if ($postItem->getType() === 'parcel' &&
            isset($formData['custom_price_enabled']) && $formData['custom_price_enabled'] &&
            isset($formData['custom_price']) && $formData['custom_price'] !== null) {

            $formattedPrice = number_format((float)$formData['custom_price'], 2, '.', '');

            $postItemDetails = new PostItemsDetail();
            $postItemDetails->setPostItemId($postItem);
            $postItemDetails->setKey('custom_price');
            $postItemDetails->setValue($formattedPrice);
            $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

            $this->doctrine->getManager()->persist($postItemDetails);
            $this->doctrine->getManager()->flush();
        }

        if ($postItem->getType() === 'parcel' &&
            isset($formData['description']) && !empty($formData['description'])) {

            $postItemDetails = new PostItemsDetail();
            $postItemDetails->setPostItemId($postItem);
            $postItemDetails->setKey('description');
            $postItemDetails->setValue($formData['description']);
            $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

            $this->doctrine->getManager()->persist($postItemDetails);
            $this->doctrine->getManager()->flush();
        }
    }

    private function saveItem($form)
    {
        $addPostItem = new AddPostItem(
            $this->doctrine->getManager(),
            $this->getOperator(),
            $form->get('company_number')->getData(),
            $form->get('company_name')->getData(),
            $form->get('type')->getData(),
            $form->get('sender')->getData()
        );

        $postItem = $addPostItem->save();

        if (!$postItem) {
            foreach ($addPostItem->getErrors() as $error) {
                $this->addFlash('error', $error);
            }
        }
    }

    private function saveItemThenRedirect(FormInterface $form, $filter, $logRTS = false, $isParcel = false, $securelyDestroyed = false)
    {
        $sender = $form->get('sender')->getData();
        $isCourtLetter = $sender === PostItem::SENDER_COURT_LETTER;
        $companyNumber = $form->get('company_number')->getData();

        $addPostItem = new AddPostItem(
            objectManager: $this->doctrine->getManager(),
            operator: $this->getOperator(),
            companyNumber: $companyNumber,
            companyName: $form->get('company_name')->getData(),
            type: $form->get('type')->getData(),
            sender: $sender ?? PostItem::SENDER_OTHER,
            status: $isCourtLetter ? PostItem::STATUS_SCAN_ONLY : PostItem::STATUS_ADDED,
        );

        if ($isCourtLetter) {
            $this->companiesMadeSimple->sendCourtLetterEmail($companyNumber);
        }

        $postItem = $addPostItem->save();

        if($isParcel)
            $this->saveItemAdditionalInfo($form, $postItem);

        if (!$postItem) {
            foreach ($addPostItem->getErrors() as $error) {
                $this->addFlash('error', $error);
            }
        }

        if ($logRTS) {
            return $this->redirectToRoute('app_post_RTS_log_action', array_merge($filter, ['id' => $postItem->getId()]));
        }

        if ($securelyDestroyed) {
            return $this->redirectToRoute('app_post_securely_destroy_log_action', array_merge($filter, ['id' => $postItem->getId()]));
        }

        return $this->redirectToRoute('app_post_items', $filter);
    }

    private function handleParcelSubmission($addItemForm, $filter, $companyToSearch, $presetIDCheck, $presetMFService): Response
    {
        if ($presetIDCheck == '' || $presetMFService == '') {
            $companyInfo = $this->companiesMadeSimple->getCompanyInformation($addItemForm->get('company_name')->getData());
            $activeIDCheck = $companyInfo['active_id_check'] ?? 0;
            $activeMFService = $companyInfo['active_mf_service'] ?? 0;
        } else {
            $activeIDCheck = $presetIDCheck;
            $activeMFService = $presetMFService;
        }
        $force_submit = $addItemForm->get('force_submit')->getData();
        $logRTS = $addItemForm->get('log_rts')->getData();

        if ($force_submit || ($activeIDCheck && $activeMFService)) {
            return $this->saveItemThenRedirect($addItemForm, $filter, false, true);
        }

        if ($logRTS) {
            return $this->saveItemThenRedirect($addItemForm, $filter, true, true);
        }

        $parcelData = [
            'activeIDCheck' => $activeIDCheck,
            'activeMFService' => $activeMFService,
            'formCompanyNumber' => $addItemForm->get('company_number')->getData(),
            'formCompanyName' => $addItemForm->get('company_name')->getData(),
            'formType' => $addItemForm->get('type')->getData(),
            'formSender' => $addItemForm->get('sender')->getData()
        ];

        return $this->redirectToRoute('app_post_items_add', array_merge($filter, $parcelData, ['companyToSearch' => $companyToSearch]));
    }

    private function setSearchFormFields($searchForm, $filter): void
    {
        $searchForm->get('companyName')->setData($filter['companyName'] ?? "");
        $searchForm->get('companyNumber')->setData($filter['companyNumber'] ?? "");
    }

    private function setFilteringTypeFormFields($filteringTypeForm, $filter): void
    {
        $filteringTypeForm->get('type')->setData($filter['type'] ?? "");
    }

    private function setFilteringStatusFormFields($filteringStatusForm, $filter): void
    {
        $filteringStatusForm->get('status')->setData($filter['status'] ?? "");
    }

    private function setupFilter($searchForm, $oldFilter): array
    {
        $filter = [];
        foreach ($oldFilter as $key => $value) {
            if ($key != 'companyName' && $key != 'companyNumber') {
                $filter[$key] = $value;
            }
        }
        if ($searchForm->get('companyName')->getData()){
            $filter['companyName'] = $searchForm->get('companyName')->getData();
        }
        if ($searchForm->get('companyNumber')->getData()){
            $filter['companyNumber'] = $searchForm->get('companyNumber')->getData();
        }
        return $filter;
    }

    private function setupIndexPageForms ($filter, $cookieCSRF) : array
    {
        $cookieCSRF = ['csrf_token' => $cookieCSRF->getValue()];

        $forms = [];
        $forms['searchForm'] = $this->createForm(PostItemSearchForm::class, $cookieCSRF);
        $forms['filteringTypeForm'] = $this->createForm(PostItemFilteringTypeForm::class, $cookieCSRF);
        $forms['filteringStatusForm'] = $this->createForm(PostItemFilteringStatusForm::class, $cookieCSRF);
        $forms['limitForm'] = $this->createForm(PostItemLimitForm::class, $cookieCSRF);
        $forms['RTSForm'] = $this->createForm(LogRTSForm::class, $cookieCSRF);
        $forms['securelyDestroyForm'] = $this->createForm(SecurelyDestroyForm::class, $cookieCSRF);
        $forms['collectedForm'] = $this->createForm(CollectItemForm::class, $cookieCSRF);
        $forms['bulkCollectedForm'] = $this->createForm(BulkCollectItemForm::class, $cookieCSRF);

        $this->setSearchFormFields($forms['searchForm'], $filter);
        $this->setFilteringTypeFormFields($forms['filteringTypeForm'], $filter);
        $this->setFilteringStatusFormFields($forms['filteringStatusForm'], $filter);
        $forms['limitForm']->get('limit')->setData($filter['limit']);

        return $forms;
    }
}
