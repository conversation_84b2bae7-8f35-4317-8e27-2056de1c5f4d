<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * The sole purpose of this Controller is to render the page with the Vue SPA with the configuration passed.
 *
 * todo: authentication
 */
class HomeController extends AbstractController
{
    /**
     * @Route("/{spa}", name="mailroomApp", requirements={"spa"="^(?!.+api|api|direct|.+direct).+"}, defaults={"spa": null})
     */
    public function index(): Response
    {
        $config = [
            'url' => [
                'uploadFile' => $this->generateUrl('api_upload_file'),
                'pullStatus' => $this->generateUrl('api_pull_status'),
                'pullWaiting' => $this->generateUrl('api_pull_waiting'),
                'pullMrPredictions' => $this->generateUrl('api_pull_mr_predictions'),
                'searchCompanies' => $this->generateUrl('api_company_search'),
                'pushResult' => $this->generateUrl('api_result'),
                'setSession' => $this->generateUrl('api_session'),
                'acknowledgeFile' => $this->generateUrl('api_acknowledge_file'),
            ],
            'fileUpload' => [
                'batchSize' => 10
            ]
        ];

        return $this->render('pages/home.html.twig', [
                'config' => json_encode($config)
            ]
        );
    }
}
