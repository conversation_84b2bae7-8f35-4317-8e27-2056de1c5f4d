<?php

namespace App\Controller;

use App\Repository\TransitFeeRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;

class TransitFeeController extends AbstractController
{
    private TransitFeeRepository $transitFeeRepository;

    public function __construct(TransitFeeRepository $transitFeeRepository)
    {
        $this->transitFeeRepository = $transitFeeRepository;
    }

    /**
     * Display and manage transit fees
     * 
     * @Route("/direct/transit-fees", name="app_transit_fees")
     */
    public function index(Request $request): Response
    {
        $transitFees = $this->transitFeeRepository->findAllOrdered();

        $formBuilder = $this->createFormBuilder();

        foreach ($transitFees as $index => $transitFee) {
            $id = $transitFee->getId();
            
            $formBuilder->add('uk_' . $id, NumberType::class, [
                'data' => $transitFee->getUkFee(),
                'label' => 'UK',
                'attr' => [
                    'class' => 'form-control',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a value for UK']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Value must be greater than or equal to 0'])
                ]
            ]);
            
            $formBuilder->add('europe_' . $id, NumberType::class, [
                'data' => $transitFee->getEuropeFee(),
                'label' => 'Europe',
                'attr' => [
                    'class' => 'form-control',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a value for Europe']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Value must be greater than or equal to 0'])
                ]
            ]);
            
            $formBuilder->add('world_' . $id, NumberType::class, [
                'data' => $transitFee->getWorldFee(),
                'label' => 'World',
                'attr' => [
                    'class' => 'form-control',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a value for World']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Value must be greater than or equal to 0'])
                ]
            ]);
        }
        
        $formBuilder->add('save', SubmitType::class, [
            'label' => 'Save Changes',
            'attr' => [
                'class' => 'btn btn-primary mt-3'
            ]
        ]);
        
        $form = $formBuilder->getForm();
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();

            foreach ($transitFees as $transitFee) {
                $id = $transitFee->getId();
                
                if (isset($data['uk_' . $id]) && 
                    isset($data['europe_' . $id]) && 
                    isset($data['world_' . $id])) {
                    
                    $transitFee->setUkFee($data['uk_' . $id]);
                    $transitFee->setEuropeFee($data['europe_' . $id]);
                    $transitFee->setWorldFee($data['world_' . $id]);
                    
                    $this->transitFeeRepository->save($transitFee);
                }
            }
            
            $this->addFlash('success', 'Transit fees have been updated successfully.');

            return $this->redirectToRoute('app_transit_fees');
        }
        
        return $this->renderForm('transit_fee/index.html.twig', [
            'form' => $form,
            'transitFees' => $transitFees
        ]);
    }
}
