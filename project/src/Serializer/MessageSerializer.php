<?php

namespace App\Serializer;

use Google\Cloud\PubSub\Message;

/**
 * todo refactor to proper serialisation (e.g. JMSSerializer)
 *
 * Class MessageSerializer
 * @package App\Serializer
 */
class MessageSerializer
{
    public static function toArray(Message $message): array
    {
        return [
            "id" => $message->id(),
            "ackId" => $message->ackId(),
            "data" => json_decode($message->data()),
            "attributes" => $message->attributes(),
            "subscription" => $message->subscription(),
            "deliveryAttempt" => $message->deliveryAttempt(),
            "info" => $message->info(),
            "orderingKey" => $message->orderingKey(),
            "publishTime" => $message->publishTime(),
            "message" => serialize($message)
        ];
    }
}
