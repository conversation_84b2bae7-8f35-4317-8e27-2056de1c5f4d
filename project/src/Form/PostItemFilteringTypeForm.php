<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class PostItemFilteringTypeForm extends CSRFForm
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('type', ChoiceType::class, [
                'required' => true,
                'choices' => [
                    'Show all Types' => 'all',
                    'Non-statutory' => 'non-statutory',
                    'Statutory' => 'statutory',
                    'Parcel' => 'parcel',
                ],
                'attr' => [
                    'class' => 'form-select',
                    'onchange' => 'this.form.submit()'
                ]
                ,'label' => false
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'attr' => [
            ]
        ));
    }

}
