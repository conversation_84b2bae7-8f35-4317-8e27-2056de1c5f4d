<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PostItemSearchForm extends CSRFForm
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('companyName', TextType::class, [
                'label' => 'Company Name',
                'required' => false,
                'attr' => [
                    'class' => 'form-control my-3 flex-fill',
                    'name' => 'companyName',
                ],
                'row_attr' => [
                    'class' => 'col-xl-4 col-lg-4 col-md-12',
                ]
            ])
            ->add('companyNumber', TextType::class, [
                'label' => 'Company Number',
                'required' => false,
                'attr' => [
                    'class' => 'form-control my-3 flex-fill',
                    'name' => 'companyNumber',
                ],
                'row_attr' => [
                    'class' => 'col-xl-4 col-lg-4 col-md-12',
                ]
            ])->add('search', SubmitType::class, [
                'attr' => [
                    'class' => 'btn btn-primary my-3 align-self-end'
                ],
                'row_attr' => [
                    'class' => 'align-self-end',
                ]
            ])->add('back', ButtonType::class, [
                'attr' => [
                    'class' => 'btn btn-outline-primary my-3 align-self-end',
                    'onclick' => "window.location.href = '/direct/post/items'"
                ],
                'row_attr' => [
                    'class' => 'align-self-end',
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'attr' => [ 
                'class' => 'm-0 p-0 gap-3 d-flex flex-wrap justify-content-start flex-fill',
            ]
        ));
    }
}

