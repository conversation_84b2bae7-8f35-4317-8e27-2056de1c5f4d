<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotNull;

class LogRTSForm extends CSRFForm
{
    private const NOT_NULL_MESSAGE = 'Please enter a value for this field';
    private const NOT_BLANK_MESSAGE = 'Please enter a value for this field';

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('item_id', TextType::class, [
                'required' => true,
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    new NotBlank([
                        'message' => self::NOT_BLANK_MESSAGE
                    ]),
                ],
                'attr'=>[
                    'onkeydown' => 'return false;',
                    'style' => 'caret-color: transparent !important;
                                pointer-events: none;
                                border: none;
                                margin-bottom: 0 !important;',
                    'type' => 'text',
                    'class' => 'form-control mb-3 p-0 m-0 rounded-0',
                ],
                'label' => 'Item ID',
            ])
            ->add('save', SubmitType::class, [
                'attr' => [
                    'class' => 'btn btn-primary',
                    'style' => 'min-width: 6em'
                ],
                'row_attr' => [
                    'class' => 'form-group',
                ],
                'label' => 'Log RTS'
            ]);
    }

}
