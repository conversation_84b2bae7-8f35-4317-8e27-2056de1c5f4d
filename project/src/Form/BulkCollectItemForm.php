<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotNull;

class BulkCollectItemForm extends CSRFForm
{
    private const NOT_NULL_MESSAGE = 'Please enter a value for this field';

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('selected_items', HiddenType::class, [
                'required' => true,
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    
                ],
            ])
            ->add('collected_name', TextType::class, [
                'required' => true,
                'attr'=>[
                    'class' => 'form-control mb-3 mh-2 mt-2',
                    'placeholder' => "Collector's full name",
                ],
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    
                ],
                'label' => 'Collected by:',
            ])
            ->add('collected_number', TextType::class, [
                'required' => true,
                'attr'=>[
                    'class' => 'form-control mb-3 mh-2 mt-2',
                    'placeholder' => "Collector's ID",
                ],
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    
                ],
                'label' => 'ID or other identification:',
            ])
            ->add('save', SubmitType::class, [
                'attr' => [
                    'class' => 'btn btn-primary',
                    'style' => 'min-width: 5em'
                ],
                'row_attr' => [
                    'class' => 'form-group',
                ],
                'label' => 'Collect'
            ]);
    }

}
