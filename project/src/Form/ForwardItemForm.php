<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class ForwardItemForm extends CSRFForm
{
    private const NOT_NULL_MESSAGE = 'Please enter a value for this field';
    private const NOT_BLANK_MESSAGE = 'Please enter a value for this field';

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->addEventListener(FormEvents::PRE_SET_DATA, function(FormEvent $event) use ($options) {
                $postItemData = $options['postItem'];
                $details = $postItemData->getDetails();
                $formattedDetails = [];
                foreach ($details as $detail) {
                    $formattedDetails[$detail->getKey()] = $detail->getValue();
                }
                $alreadyForwarded = $options['alreadyForwarded'];
                $form = $event->getForm();

                $form
                    ->add('postage_class', ChoiceType::class, [
                        'required' => true,
                        'label' => 'form.postage_class',
                        'data' => $formattedDetails['postage_class'] ?? null,
                        'choices' => [
                            'First Class' => 'first_class',
                            'Second Class' => 'second_class',
                        ],
                        'attr' => [
                            'disabled' => $alreadyForwarded
                        ],
                        'constraints' => [
                            new NotNull([
                                'message' => self::NOT_NULL_MESSAGE
                            ]),
                            new NotBlank([
                                'message' => self::NOT_BLANK_MESSAGE
                            ]),
                        ],
                        'multiple' => false,
                        'expanded' => true
                    ])
                    ->add('save', SubmitType::class, [
                        'label' => 'Save & Print',
                        'attr' => [
                            'class' => 'btn btn-primary',
                            'disabled' => $alreadyForwarded
                        ],
                        'row_attr' => [
                            'class' => 'form-group',
                        ]
                    ]);

                switch ($postItemData->getType()) {
                    case "statutory":
                        $form
                            ->add('statutory_item', ChoiceType::class, [
                                'required' => true,
                                'label' => 'Statutory Item Type',
                                'data' => $formattedDetails['statutory_item'] ?? null,
                                'attr' => [
                                    'class' => 'form-control',
                                    'disabled' => $alreadyForwarded
                                ],
                                'row_attr' => [
                                    'class' => 'form-group'
                                ],
                                'constraints' => [
                                    new NotNull([
                                        'message' => self::NOT_NULL_MESSAGE
                                    ]),
                                    new NotBlank([
                                        'message' => self::NOT_BLANK_MESSAGE
                                    ]),
                                ],
                                'choices' => [
                                    'Select a option' => '',
                                    'Court' => 'Court',
                                    'Intellectual Property Office' => 'Intellectual Property Office',
                                    'Office for National Statistics' => 'Office for National Statistics',
                                    'Companies House' => 'Companies House',
                                    'HMRC' => 'HMRC',
                                    'Pensions' => 'Pensions',
                                    'Other' => 'Other',
                                ]
                            ]);
                        break;
                    case "parcel":
                        $form

                            ->add('total_weight', NumberType::class, [
                                'required' => true,
                                'label' => 'Total Weight (grams)',
                                'data' => $formattedDetails['total_weight'],
                                'empty_data' => $formattedDetails['total_weight'],
                                'row_attr' => [
                                    'class' => 'form-group'
                                ],
                                'attr' => [
                                    'class' => 'form-control',
                                    'disabled' => true
                                ],
                                'constraints' => [
                                    new NotNull([
                                        'message' => self::NOT_NULL_MESSAGE
                                    ]),
                                    new NotBlank([
                                        'message' => self::NOT_BLANK_MESSAGE
                                    ]),
                                ],
                                'mapped'=> false
                            ])
                            ->add('dimensions', TextType::class, [
                                'required' => true,
                                'label' => 'Dimensions (length x width x height)',
                                'data' => $formattedDetails['dimensions'],
                                'empty_data' => $formattedDetails['dimensions'],
                                'row_attr' => [
                                    'class' => 'form-group'
                                ],
                                'attr' => [
                                    'class' => 'form-control',
                                    'disabled' => true
                                ],
                                'constraints' => [
                                    new NotNull([
                                        'message' => self::NOT_NULL_MESSAGE
                                    ]),
                                    new NotBlank([
                                        'message' => self::NOT_BLANK_MESSAGE
                                    ]),
                                ],
                                'mapped'=> false
                            ]);

                            if (isset($formattedDetails['custom_price'])) {
                                $form->add('custom_price', NumberType::class, [
                                    'required' => false,
                                    'label' => 'Custom Price (£)',
                                    'data' => $formattedDetails['custom_price'],
                                    'empty_data' => $formattedDetails['custom_price'],
                                    'row_attr' => [
                                        'class' => 'form-group'
                                    ],
                                    'attr' => [
                                        'class' => 'form-control',
                                        'disabled' => true
                                    ],
                                    'mapped'=> false
                                ]);
                            }

                            if (isset($formattedDetails['description'])) {
                                $form->add('description', TextType::class, [
                                    'required' => false,
                                    'label' => 'Description',
                                    'data' => $formattedDetails['description'],
                                    'empty_data' => $formattedDetails['description'],
                                    'row_attr' => [
                                        'class' => 'form-group'
                                    ],
                                    'attr' => [
                                        'class' => 'form-control',
                                        'disabled' => true
                                    ],
                                    'mapped'=> false
                                ]);
                            }
                        break;
                    default:
                        $form
                            ->add('total_weight', NumberType::class, [
                                'required' => true,
                                'label' => 'Total Weight (grams)',
                                'data' => $formattedDetails['total_weight'] ?? null,
                                'row_attr' => [
                                    'class' => 'form-group'
                                ],
                                'attr' => [
                                    'class' => 'form-control',
                                    'disabled' => $alreadyForwarded
                                ],
                                'constraints' => [
                                    new NotNull([
                                        'message' => self::NOT_NULL_MESSAGE
                                    ]),
                                    new NotBlank([
                                        'message' => self::NOT_BLANK_MESSAGE
                                    ]),
                                ],
                            ]);
                }
            });
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'postItem' => null,
            'alreadyForwarded' => false,
            'attr' => [
                'id' => 'forward_item_form'
            ]
        ));
    }
}
