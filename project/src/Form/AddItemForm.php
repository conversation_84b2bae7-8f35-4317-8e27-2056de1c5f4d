<?php

namespace App\Form;

use App\Entity\PostItem\PostItem;
use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class AddItemForm extends CSRFForm
{
    private const NOT_NULL_MESSAGE = 'Please enter a value for this field';
    private const NOT_BLANK_MESSAGE = 'Please enter a value for this field';

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('company_number', TextType::class, [
                'required' => false,
                'attr'=>[
                    'readonly' => true,
                    'type' => 'text',
                    'class' => 'form-control mb-3',
                    'placeholder' => 'Select from the search list above.',
                ],
                'label' => 'Company Number',
            ])
            ->add('company_name', TextType::class, [
                'required' => true,
                'attr'=>[
                    'onkeydown' => 'return false;',
                    'style' => 'caret-color: transparent !important;
                                pointer-events: none;',
                    'type' => 'text',
                    'class' => 'form-control mb-3',
                    'placeholder' => 'Select from the search list above.',
                ],
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    new NotBlank([
                        'message' => self::NOT_BLANK_MESSAGE
                    ]),
                ],
                'label' => 'Company Name',
            ])
            ->add('type', ChoiceType::class, [
                'required' => true,
                'placeholder' => 'Please select one',
                'choices' => [
                    'Parcel' => 'parcel',
                ],
                'constraints' => [
                    new NotNull([
                        'message' => self::NOT_NULL_MESSAGE
                    ]),
                    new NotBlank([
                        'message' => self::NOT_BLANK_MESSAGE
                    ]),
                ],
                'attr' => [
                    'class' => 'form-select mb-3',
                ]
            ])

            ->add('total_weight', NumberType::class, [
                'required' => true,
                'data' => 0,
                'label' => 'Total Weight (grams)',
                'row_attr' => [
                    'class' => 'form-group'
                ],
                'attr' => [
                    'class' => 'form-control',
                ]
            ])
            ->add('length', NumberType::class, [
                'required' => true,
                'data' => 0,
                'label' => 'Length (cm)',
                'row_attr' => [
                    'class' => 'form-group'
                ],
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0
                ]
            ])
            ->add('width', NumberType::class, [
                'required' => true,
                'data' => 0,
                'label' => 'Width (cm)',
                'row_attr' => [
                    'class' => 'form-group'
                ],
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0
                ]
            ])
            ->add('height', NumberType::class, [
                'required' => true,
                'data' => 0,
                'label' => 'Height (cm)',
                'row_attr' => [
                    'class' => 'form-group'
                ],
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0
                ]
            ])
            ->add('custom_price_enabled', CheckboxType::class, [
                'required' => false,
                'label' => 'Custom price',
                'label_attr' => [
                    'class' => 'form-check-label'
                ],
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'row_attr' => [
                    'class' => 'form-check mb-2 parcel-field',
                    'style' => 'display: none;'
                ]
            ])
            ->add('custom_price', NumberType::class, [
                'required' => false,
                'label' => 'Price (£)',
                'scale' => 2,
                'html5' => true,
                'attr' => [
                    'class' => 'form-control',
                    'step' => '0.01',
                    'min' => '0',
                    'placeholder' => '0.00',
                    'disabled' => 'disabled'
                ],
                'row_attr' => [
                    'class' => 'form-group mb-3 parcel-field',
                    'style' => 'display: none;'
                ],
                'constraints' => [
                    new PositiveOrZero([
                        'message' => 'Price must be a positive number or zero'
                    ])
                ]
            ])
            ->add('description', TextType::class, [
                'required' => false,
                'label' => 'Description',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter a description for this parcel'
                ],
                'row_attr' => [
                    'class' => 'form-group mb-3 parcel-field',
                    'style' => 'display: none;'
                ]
            ])
            ->add('sender', ChoiceType::class, [
                'placeholder' => 'Please select one',
                'choices' => [
                    'Companies House' => PostItem::SENDER_COMPANIES_HOUSE,
                    'HMRC' => PostItem::SENDER_HMRC,
                    'Court Letter' => PostItem::SENDER_COURT_LETTER,
                    'Other' => PostItem::SENDER_OTHER,
                ],
                'attr' => [
                    'class' => 'form-select mb-4',
                ]
            ])
            ->add('add_multiple', IntegerType::class, [
                'required' => false,
                'data' => 1,
                'label' => '(Add multiple items)',
                'label_attr' => [
                    'class' => 'test-mode col text-muted fw-light fst-italic align-self-center'
                ],
                'attr' => [
                    'class' => 'test-mode col form-control',
                    'style' => 'max-width: 5rem',
                    'disabled' => 'disabled'
                ],
                'row_attr' => [
                    'class' => 'test-mode form-group'
                ]
            ])

            ->add('log_rts', HiddenType::class, [
                'required' => false,
                'data' => false
            ])
            ->add('force_submit', HiddenType::class, [
                'required' => false,
                'data' => false
            ])
            ->add('save', SubmitType::class, [
                'attr' => [
                    'class' => 'btn btn-primary'
                ],
                'row_attr' => [
                    'class' => 'form-group',
                ],
                'label' => 'Add item'
            ]);

            if (isset($options['data']['is_production']) && $options['data']['is_production'] === false) {
                $builder->add('test_mode', HiddenType::class, [
                    'required' => false,
                    'data' => 1
                ]);
            }
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'attr' => [
                'id' => 'add_item_form'
            ]
        ));
    }
}
