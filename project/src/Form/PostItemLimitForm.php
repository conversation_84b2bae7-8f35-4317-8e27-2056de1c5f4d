<?php

namespace App\Form;

use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class PostItemLimitForm extends CSRFForm
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('limit', ChoiceType::class, [
                'required' => true,
                'choices' => [
                    '10' => '10',
                    '20' => '20',
                    '30' => '30',
                ],
                'attr' => [
                    'class' => 'form-select',
                    'onchange' => 'this.form.submit()'
                ]
                ,'label' => false
            ]);
    }
}
