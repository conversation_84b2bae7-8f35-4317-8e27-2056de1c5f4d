<?php

declare(strict_types=1);

namespace App\Form;

use App\Entity\PostItem\PostItem;
use App\Form\Extension\CSRFForm;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PostItemFilteringStatusForm extends CSRFForm
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('status', ChoiceType::class, [
                'required' => true,
                'choices' => ['Show all Statuses' => 'all',] + array_flip(PostItem::PARSED_STATUSES),
                'attr' => [
                    'class' => 'form-select',
                    'onchange' => 'this.form.submit()'
                ]
                ,'label' => false
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'attr' => [
            ]
        ));
    }

}
