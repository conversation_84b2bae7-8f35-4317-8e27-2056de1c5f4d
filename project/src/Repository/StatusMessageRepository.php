<?php

namespace App\Repository;

use App\Entity\StatusMessage;
use DateTime;
use DateTimeZone;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @extends ServiceEntityRepository<StatusMessage>
 *
 * @method StatusMessage|null find($id, $lockMode = null, $lockVersion = null)
 * @method StatusMessage|null findOneBy(array $criteria, array $orderBy = null)
 * @method StatusMessage[]    findAll()
 * @method StatusMessage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class StatusMessageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, StatusMessage::class);
    }

    public function save(StatusMessage $entity, bool $flush = true): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(StatusMessage $entity, bool $flush = true): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

   /**
    * @return StatusMessage[] Returns an array of StatusMessage objects
    */
    public function findByUid($uid): array
    {
        try {
            return $this->createQueryBuilder('sm')
                ->andWhere('sm.operator = :uid')
                ->setParameter('uid', $uid)
                ->orderBy('sm.dtc', 'DESC')
                ->getQuery()
                ->getResult();
        } catch (Exception $e) {
            return [];
        }
    }
    
    public function removeExpiredMessages()
    {
        $timeNow = new DateTime('now', new DateTimeZone('UTC'));
        $expirationTime = $timeNow->modify('-6 hours');

        try {
            $qb = $this->createQueryBuilder('sm');
            return $qb->delete()
                ->where($qb->expr()->lt('sm.dtc', ':expirationTime'))
                ->setParameter('expirationTime', $expirationTime)
                ->getQuery()
                ->getResult();
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * @return bool Returns true if the message was removed, false otherwise
     */
    public function removeById($id): mixed
    {
        try {
            return $this->createQueryBuilder('sm')
                ->delete()
                ->where('sm.id = :messageId')
                ->setParameter('messageId', $id, 'uuid')
                ->getQuery()
                ->getResult();
        } catch (Exception $e) {
            return false;
        }
    }
}
