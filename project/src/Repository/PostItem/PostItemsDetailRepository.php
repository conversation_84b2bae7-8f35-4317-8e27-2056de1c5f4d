<?php

namespace App\Repository\PostItem;

use App\Entity\PostItem\PostItemsDetail;
use DateTime;
use DateTimeZone;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends ServiceEntityRepository<PostItemsDetail>
 *
 * @method PostItemsDetail|null find($id, $lockMode = null, $lockVersion = null)
 * @method PostItemsDetail|null findOneBy(array $criteria, array $orderBy = null)
 * @method PostItemsDetail[]    findAll()
 * @method PostItemsDetail[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method PostItemsDetail[]    queryListByGroup(array $itemList, ?string $field='postItemId', ?string $ordKey="key", ?bool $ascending=true)
 * @method PostItemsDetail[]    getMatchingItems($id, $listToCheck, ?array $fieldsToGet=[])
 */
class PostItemsDetailRepository extends ServiceEntityRepository
{
    private EntityManagerInterface $em;
    
    public function __construct(ManagerRegistry $registry, EntityManagerInterface $manager)
    {
        parent::__construct($registry, PostItemsDetail::class);
        $this->setEm($manager);
    }

    public function queryListByGroup(array $itemList, ?string $field='postItemId', ?string $ordKey="key", ?bool $ascending=true):array
    {
        $em = $this->getEm();
        $qb = $this->em->createQueryBuilder();
        $qb->select('i')
            ->from($this->getClassName(), 'i')
            ->where($qb->expr()->in("i.$field", ':ids'))
            ->setParameters([
                'ids' => array_map(function ($uuid) {
                    return pack("H*", str_replace('-', '', (string)$uuid));
                }, $itemList),
            ])
            ->orderBy("i.$ordKey", ($ascending ? 'ASC' : 'DESC'));
        
        return $qb->getQuery()->getResult();
    }

    function getMatchingItems($id, $listToCheck, ?array $fieldsToGet=[]):array
    {
        $list = [];
        foreach($listToCheck as $item){
            if ($item->getPostItemId()->getId() == $id){
                $item = $item->toArray();
                $listItem = [];
                foreach($fieldsToGet as $field){
                    if ($item[$field]) {
                        $listItem[$field] = $item[$field];
                    }
                }
                $list[] = $listItem;
            }
        }
        return $list;
    }

    function saveDetail(PostItemsDetail $detail)
    {
        $now = new DateTime('now', new DateTimeZone('UTC'));

        if (!$detail->getDtc()) {
            $detail->setDtc($now);
        }

        $detail->setDtm($now);

        $this->getEm()->persist($detail);

        $this->getEm()->flush();
    }

	/**
	 * @return mixed
	 */
	private function getEm() {
		return $this->em;
	}
	
	/**
	 * @param mixed $em 
	 * @return self
	 */
	private function setEm($em): self {
		$this->em = $em;
		return $this;
	}

}
