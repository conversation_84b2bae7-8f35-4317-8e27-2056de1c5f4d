<?php

namespace App\Repository\PostItem;

use App\Entity\PostItem\PostItem;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @extends ServiceEntityRepository<PostItem>
 *
 * @method PostItem|null find($id, $lockMode = null, $lockVersion = null)
 * @method PostItem|null findOneBy(array $criteria, array $orderBy = null)
 * @method PostItem[]    findAll()
 * @method PostItem[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PostItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PostItem::class);
    }

    public function count(array $criteria = []): int
    {
        $query = $this->createQueryBuilder('p');
        if (!empty($criteria)) {
            if(isset($criteria['type']) && $criteria['type'] != 'all') {
                if ($criteria['type'] != 'non-statutory') {
                    $query->andWhere('p.type = :type')->setParameter('type', $criteria['type']);
                } else {
                    $query->andWhere('p.type = :type')->setParameter('type', 'non-statutory');
                    $query->orWhere('p.type = :type2')->setParameter('type2', 'regular');
                }
            }

            if(isset($criteria['companyName'])) {
                $query->andWhere('p.companyName LIKE :companyName')
                ->setParameter('companyName', '%' . $criteria['companyName'] . '%');
            }

            if(isset($criteria['companyNumber'])) {
                $query->andWhere('p.companyNumber LIKE :companyNumber')
                ->setParameter('companyNumber', '%' . $criteria['companyNumber'] . '%');
            }

            if(isset($criteria['companyNumbers'])) {
                $query->andWhere('p.companyNumber IN (:companyNumbers)')
                    ->setParameter('companyNumbers', $criteria['companyNumbers']);
            }

            if(isset($criteria['status'])) {
                if(is_array($criteria['status'])) {
                    $query->andWhere('p.status IN (:statuses)')
                        ->setParameter('statuses', $criteria['status']);
                } else {
                    $query->andWhere('p.status = :status')
                        ->setParameter('status', $criteria['status']);
                }
            }
            if(isset($criteria['dtc'])) {
                $query->andWhere('p.dtc >= :dtc')
                    ->setParameter('dtc', $criteria['dtc']);
            }

            if(isset($criteria['transactionId'])) {
                if ($criteria['transactionId'] == 'NOT NULL') {
                    $query->andWhere('p.transactionId IS NOT NULL');
                } else {
                    $query->andWhere('p.transactionId = :transactionId')
                        ->setParameter('transactionId', $criteria['transactionId']);
                }
            }

        }

        return (int) $query
            ->select('count(p.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findOneById(string $id): ?PostItem
    {
        try {
            return $this->createQueryBuilder('p')
                ->andWhere('p.id = :id')
                ->setParameter('id', $id, 'uuid')
                ->getQuery()
                ->getSingleResult();
        } catch(Exception $e) {
            return null;
        }
    }

    public function findByCompanyNameOrCompanyNumber(array $criteria): array
    {
        try {
            $query = $this->createQueryBuilder('p');

            if (isset($criteria['sortBy'], $criteria['direction']) && !empty($criteria['sortBy']) && !empty($criteria['direction'])) {
                switch ($criteria['sortBy']) {
                    case 'sender':
                        $query->orderBy('LOWER(p.sender)', $criteria['direction']);
                        break;
                    case 'type':
                        $query->orderBy("CASE WHEN p.type = 'regular' THEN 'non-statutory' ELSE p.type END", $criteria['direction']);
                        break;
                    case 'events':
                        $query->leftJoin('p.events', 'e')
                        ->groupBy('p.id')
                        ->orderBy('COUNT(e.id)', $criteria['direction']);
                        break;
                    case 'status':
                        $query->addOrderBy('p.status', $criteria['direction']);
                        break;
                    default:
                        $query->orderBy('p.' . $criteria['sortBy'], $criteria['direction']);
                        break;
                }
            } else {
                $query->orderBy('p.dtc', 'DESC');
            }

            if(isset($criteria['type']) && $criteria['type'] != 'all') {
                if ($criteria['type'] != 'non-statutory') {
                    $query->andWhere('p.type = :type')->setParameter('type', $criteria['type']);
                } else {
                    $query->andWhere('p.type = :type')->setParameter('type', 'non-statutory');
                    $query->orWhere('p.type = :type2')->setParameter('type2', 'regular');
                }
            }

            if(isset($criteria['companyName'])) {
                $query->andWhere('p.companyName LIKE :companyName')->setParameter('companyName', '%' . $criteria['companyName'] . '%');
            }

            if(isset($criteria['companyNumber'])) {
                $query->andWhere('p.companyNumber LIKE :companyNumber')->setParameter('companyNumber', '%' . $criteria['companyNumber'] . '%');
            }

            if (isset($criteria['status']) && $criteria['status'] != 'all') {
                $query->andWhere('p.status = :status')->setParameter('status', $criteria['status']);
            }

            return (new Paginator($query))
                ->getQuery()
                ->setFirstResult($criteria['limit'] * ($criteria['page'] - 1))
                ->setMaxResults($criteria['limit'])
                ->getResult();
        } catch (Exception $e) {
            return [];
        }
    }

    public function findByCompanyNumbers(?array $criteria)
    {
        try {
            $query = $this->createQueryBuilder('pi');
            $query->select('pi');
            
            if (isset($criteria['companyNumbers'])) {
                $query->andWhere('pi.companyNumber IN (:companyNumbers)')
                    ->setParameter('companyNumbers', $criteria['companyNumbers']);
            }

            if (isset($criteria['includedStatuses'])) {
                $query->andWhere('pi.status IN (:includedStatuses)')
                    ->setParameter('includedStatuses', $criteria['includedStatuses']);
            }

            $query->orderBy('pi.dtc', 'DESC');

            if (isset($criteria['pageNumber'], $criteria['pageSize'])) {
                return (new Paginator($query))
                    ->getQuery()
                    ->setFirstResult($criteria['pageSize'] * ($criteria['pageNumber'] - 1))
                    ->setMaxResults($criteria['pageSize'])
                    ->getResult();
            }

            return $query->getQuery()
                ->getResult();
        } catch (Exception $e) {
            return [];
        }
    }

    public function getWaitingNonStatutory()
    {
        $subquery = $this->createQueryBuilder('pi_sub')
            ->select('1')
            ->leftJoin('pi_sub.events', 'log_sub')
            ->andWhere('log_sub.eventName = :eventName')
            ->andWhere('pi_sub.id = pi.id')
            ->setMaxResults(1)
            ->getQuery()
            ->getDQL();

        return $this->createQueryBuilder('pi')
            ->andWhere('NOT EXISTS (' . $subquery . ')')
            ->setParameter('eventName', 'released')
            ->getQuery()
            ->getResult();
    }
    public function getItemsFromStatusList(array $statuses)
    {
        return $this->createQueryBuilder('pi')
            ->andWhere('pi.status IN (:requiredUpdatingStatuses)')
            ->setParameter('requiredUpdatingStatuses', $statuses)
            ->getQuery()
            ->getResult();
    }

    /**
     * Retrieves a paginated chunk of items filtered by status using company-based pagination.
     *
     * This method implements an intelligent chunking strategy that groups items by company
     * to ensure efficient batch processing while respecting size limits. It avoids splitting
     * a company's items across different chunks, maintaining data consistency during processing.
     *
     * Algorithm:
     * 1. Uses the last processed item's company number as pagination cursor
     * 2. Groups items by company and counts items per company
     * 3. Selects companies sequentially until chunk size limit would be exceeded
     * 4. Retrieves all items for selected companies, ordered consistently by dtc
     *
     * @param array $statuses Array of status values to filter items by
     * @param string|null $lastId ID of the last processed item for pagination continuation
     * @param int $chunkSize Maximum number of items to return (default: 500)
     *
     * @return array Array of PostItem entities matching the specified statuses,
     *               ordered by company number (ASC) then dtc (DESC)
     *               Returns empty array when no suitable companies are found
     */
    public function getChunkOfItemsFromStatusList(array $statuses, string $lastId = null, $chunkSize = 500)
    {
        $lastItem = $lastId ? $this->findOneById($lastId)?->toArray() : null;

        $companyQb = $this->createQueryBuilder('pi')
            ->select('pi.companyNumber, COUNT(pi.id) as itemCount')
            ->andWhere('pi.status IN (:processingStatuses)')
            ->setParameter('processingStatuses', $statuses)
            ->groupBy('pi.companyNumber')
            ->orderBy('pi.companyNumber', 'ASC');

        if ($lastItem !== null) {
            $companyQb->andWhere('pi.companyNumber > :lastCompanyNumber')
                ->setParameter('lastCompanyNumber', $lastItem['companyNumber']);
        }

        $selectedCompanies = [];
        $totalItems = 0;

        foreach ($companyQb->getQuery()->getResult() as $company) {
            if ($totalItems + $company['itemCount'] > $chunkSize) break;
            $selectedCompanies[] = $company['companyNumber'];
            $totalItems += $company['itemCount'];
        }

        if (empty($selectedCompanies)) {
            return [];
        }

        return $this->createQueryBuilder('pi')
            ->andWhere('pi.status IN (:processingStatuses)')
            ->andWhere('pi.companyNumber IN (:companyNumbers)')
            ->setParameter('processingStatuses', $statuses)
            ->setParameter('companyNumbers', $selectedCompanies)
            ->orderBy('pi.companyNumber', 'ASC')
            ->addOrderBy('pi.dtc', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function getWaitingNonStatutoryByCompany($companyNumber)
    {
        $subquery = $this->createQueryBuilder('pi_sub')
            ->select('1')
            ->leftJoin('pi_sub.events', 'log_sub')
            ->andWhere('log_sub.eventName = :eventName')
            ->andWhere('pi_sub.id = pi.id')
            ->andWhere('pi_sub.companyNumber = :companyNumber')
            ->andWhere('pi_sub.type = :type')
            ->setMaxResults(1)
            ->getQuery()
            ->getDQL();

        return $this->createQueryBuilder('pi')
            ->andWhere('NOT EXISTS (' . $subquery . ')')
            ->andWhere('pi.companyNumber = :companyNumber')
            ->andWhere('pi.type = :type')
            ->setParameter('eventName', 'released')
            ->setParameter('companyNumber', $companyNumber)
            ->setParameter('type', 'non-statutory')
            ->getQuery()
            ->getResult();
    }

    public function getReleasedNonStatutory()
    {
        return $this->createQueryBuilder('pi')
            ->leftJoin('pi.events', 'log')
            ->andWhere('pi.type = :type')
            ->andWhere('log.eventName = :eventName')
            ->setParameter('type', 'non-statutory')
            ->setParameter('eventName', 'released')
            ->getQuery()
            ->getResult();
    }

    public function getReleasedNonStatutoryByCompany(string $companyNumber)
    {
        return $this->createQueryBuilder('pi')
            ->leftJoin('pi.events', 'log')
            ->andWhere('pi.type = :type')
            ->andWhere('pi.companyNumber = :companyNumber')
            ->andWhere('log.eventName = :eventName')
            ->setParameter('type', 'non-statutory')
            ->setParameter('companyNumber', $companyNumber)
            ->setParameter('eventName', 'released')
            ->getQuery()
            ->getResult();
    }

    public function getUnpaidForwardedItems()
    {
        $subQuery = $this->createSubQueryForUnpaidItems();
        $query = $this->createQueryForUnpaidForwardedItems($subQuery);
        
        $this->setQueryParameters(
            $query,
            [
                'eventName' => 'address_label_generated',
                'detailKeyOrderId' => 'order_id',
                'detailKeyCustomPrice' => 'custom_price',
                'detailValueCustomPrice' => '0'
            ]
        );

        return $query->getQuery()->getResult();
    }

    private function createSubQueryForUnpaidItems()
    {
        $subQuery = $this->_em->createQueryBuilder();
        $subQuery->select('pid')
            ->from('App\Entity\PostItem\PostItemsDetail', 'pid')
            ->where(
                $subQuery->expr()->orX(
                    $subQuery->expr()->eq('pid.key', ':detailKeyOrderId'),
                    $subQuery->expr()->andX(
                        $subQuery->expr()->eq('pid.key', ':detailKeyCustomPrice'),
                        $subQuery->expr()->eq('pid.value', ':detailValueCustomPrice')
                    )
                )
                
            )
            ->andWhere($subQuery->expr()->eq('pid.postItemId', 'pi'));

        return $subQuery;
    }

    private function createQueryForUnpaidForwardedItems($subQuery)
    {
        $query = $this->createQueryBuilder('pi');
        $query->join('pi.events', 'pie')
            ->where($query->expr()->eq('pie.eventName', ':eventName'))
            ->andWhere($query->expr()->not($query->expr()->exists($subQuery->getDQL())));
        
        return $query;
    }

    private function setQueryParameters($query, array $parameters)
    {
        foreach ($parameters as $key => $value) {
            $query->setParameter($key, $value);
        }
    }

    public function getFailedPaymentItems(?array $companyNumbers = null)
    {
        $qb = $this->_em->createQueryBuilder();

        $qb->select('DISTINCT pi')
            ->from(PostItem::class, 'pi')
            ->join('pi.details', 'pid')
            ->where('pid.key = :detailKey')
            ->andWhere('pi.status = :status');

        $qb->setParameter('detailKey', 'charging_attempts')
            ->setParameter('status', 'added');

        if ($companyNumbers !== null) {
            $qb->andWhere('pi.companyNumber IN (:companyNumbers)')
                ->setParameter('companyNumbers', $companyNumbers);
        }

        return $qb->getQuery()->getResult();
    }

    private function setQueryStatutoryCondition($query, $statutory)
    {
        if(is_null($statutory)){
            return;
        }

        if($statutory){
            $query->andWhere($query->expr()->eq('pi.type', ':statutoryString'));
        } else {
            $query->andWhere($query->expr()->neq('pi.type', ':statutoryString'));
        }
        $query->setParameter('statutoryString', 'statutory');
    }
}
