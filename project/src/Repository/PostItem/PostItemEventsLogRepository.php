<?php

namespace App\Repository\PostItem;

use App\Entity\PostItem\PostItemEventsLog;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PostItemEventsLog>
 *
 * @method PostItemEventsLog|null find($id, $lockMode = null, $lockVersion = null)
 * @method PostItemEventsLog|null findOneBy(array $criteria, array $orderBy = null)
 * @method PostItemEventsLog[]    findAll()
 * @method PostItemEventsLog[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method PostItemEventsLog[]    queryListByGroup(array $itemList, ?string $field='postItemId', ?string $ordKey="key", ?bool $ascending=true)
 * @method PostItemEventsLog[]    getMatchingItems($id, $listToCheck, ?array $fieldsToGet=[])
 */
class PostItemEventsLogRepository extends ServiceEntityRepository
{
    private EntityManagerInterface $em;

    public function __construct(ManagerRegistry $registry, EntityManagerInterface $manager)
    {
        parent::__construct($registry, PostItemEventsLog::class);
        $this->setEm($manager);
    }

    public function queryListByGroup(array $itemList, ?string $field='postItemId', ?string $ordKey="key", ?bool $ascending=true):array
    {
        $em = $this->getEm();
        $qb = $this->em->createQueryBuilder();
        $qb->select('i')
            ->from($this->getClassName(), 'i')
            ->where($qb->expr()->in("i.$field", ':ids'))
            ->setParameters([
                'ids' => array_map(function ($uuid) {
                    return pack("H*", str_replace('-', '', (string)$uuid));
                }, $itemList),
            ])
            ->orderBy("i.$ordKey", ($ascending ? 'ASC' : 'DESC'));
        
        return $qb->getQuery()->getResult();
    }

    function getMatchingItems($id, $listToCheck, ?array $fieldsToGet=[]):array
    {
        $list = [];
        foreach($listToCheck as $item){
            if ($item->getPostItemId()->getId() == $id){
                $item = $item->extract(true);
                $listItem = [];
                foreach($fieldsToGet as $field){
                    if ($item[$field]) {
                        $listItem[$field == 'eventName' ? 'event' : $field] = $item[$field];
                    }
                }
                $list[] = $listItem;
            }
        }
        return $list;
    }

	/**
	 * @return mixed
	 */
	private function getEm() {
		return $this->em;
	}
	
	/**
	 * @param mixed $em 
	 * @return self
	 */
	private function setEm($em): self {
		$this->em = $em;
		return $this;
	}
}
