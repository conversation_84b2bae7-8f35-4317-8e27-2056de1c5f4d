<?php

namespace App\Repository;

use App\Entity\TransitFee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @extends ServiceEntityRepository<TransitFee>
 *
 * @method TransitFee|null find($id, $lockMode = null, $lockVersion = null)
 * @method TransitFee|null findOneBy(array $criteria, array $orderBy = null)
 * @method TransitFee[]    findAll()
 * @method TransitFee[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TransitFeeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TransitFee::class);
    }

    public function save(TransitFee $entity, bool $flush = true): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TransitFee $entity, bool $flush = true): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByFeeName(string $feeName): ?TransitFee
    {
        try {
            return $this->createQueryBuilder('tf')
                ->andWhere('tf.feeName = :feeName')
                ->setParameter('feeName', $feeName)
                ->getQuery()
                ->getOneOrNullResult();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * @return TransitFee[]
     */
    public function findAllOrdered(): array
    {
        try {
            return $this->createQueryBuilder('tf')
                ->orderBy('tf.id', 'ASC')
                ->getQuery()
                ->getResult();
        } catch (Exception $e) {
            return [];
        }
    }
}
