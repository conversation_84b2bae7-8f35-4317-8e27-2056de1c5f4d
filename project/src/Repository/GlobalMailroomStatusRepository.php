<?php

namespace App\Repository;

use App\Entity\GlobalMailroomStatus;
use DateTime;
use DateTimeZone;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Exception;

/**
 * @extends ServiceEntityRepository<GlobalMailroomStatus>
 *
 * @method GlobalMailroomStatus|null find($id, $lockMode = null, $lockVersion = null)
 * @method GlobalMailroomStatus|null findOneBy(array $criteria, array $orderBy = null)
 * @method GlobalMailroomStatus[]    findAll()
 * @method GlobalMailroomStatus[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class GlobalMailroomStatusRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, GlobalMailroomStatus::class);
    }

    public function save(GlobalMailroomStatus $entity, bool $flush = true): void
    {
        if (!$entity->getDtc()) {
            $entity->setDtc(new DateTime('now', new DateTimeZone('UTC')));
        }

        $entity->setDtm(new DateTime('now', new DateTimeZone('UTC')));

        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(GlobalMailroomStatus $entity, bool $flush = true): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getBatchCounterEntity(): GlobalMailroomStatus
    {
        $entity = $this->findOneBy(['name' => 'batch_counter']);

        if (!$entity) {
            $entity = new GlobalMailroomStatus();
            $entity->setName('batch_counter')
                ->setValue(0)
                ->setDtc(new DateTime('now', new DateTimeZone('UTC')))
                ->setDtm(new DateTime('now', new DateTimeZone('UTC')));
        }

        return $entity;
    }
}
