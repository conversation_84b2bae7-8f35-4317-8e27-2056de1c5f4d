<?php

namespace App\Repository;

use App\Entity\AiPerformanceReport;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AiPerformanceReport>
 *
 * @method AiPerformanceReport|null find($id, $lockMode = null, $lockVersion = null)
 * @method AiPerformanceReport|null findOneBy(array $criteria, array $orderBy = null)
 * @method AiPerformanceReport[]    findAll()
 * @method AiPerformanceReport[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AiPerformanceReportRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AiPerformanceReport::class);
    }

    public function save(AiPerformanceReport $entity, bool $flush = true): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AiPerformanceReport $entity, bool $flush = true): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByPeriod(string $period): array
    {
        $result = $this->createQueryBuilder('a')
            ->andWhere('a.period = :period')
            ->setParameter('period', $period)
            ->getQuery()
            ->getResult();
        
        return $result;
    }
}
