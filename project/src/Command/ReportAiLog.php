<?php

namespace App\Command;

use App\Client\MatchLogHelper;
use App\Dto\DiscordMessage;
use App\Entity\AiPerformanceReport;
use App\Repository\AiPerformanceReportRepository;
use DateTime;
use DateTimeZone;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\ResponseInterface;

class ReportAiLog extends Command
{
    protected static $defaultName = 'app:report-ai-log';

    /**
     * @var ManagerRegistry $doctrine
     */
    private ManagerRegistry $doctrine;

    public function __construct(
        ManagerRegistry $doctrine
    )
    {
        $this->doctrine = $doctrine;

        parent::__construct();
    }
    
    protected function configure(): void
    {
        $this
            ->setDescription('Executes the AI log reporting routine in a monthly manner.')
            ->setHelp('This performs the AI log reporting routine on the 1st day of each month.')
            ->addOption(name: 'month', description: 'The month to report on. Defaults to the current month.', mode: InputOption::VALUE_REQUIRED)
            ->addOption(name: 'year', description: 'The year to report on. Defaults to the current year.', mode: InputOption::VALUE_REQUIRED)
            ->addOption(name: 'dry-run', description: 'Skips any actual reporting. Meant for debugging.')
            ->addOption(name: 'debug', description: 'Displays extra information about the data collected or other info relevant for debugging.')
            ->addOption(name: 'no-discord', description: 'Runs the script without posting the resulting data on the Moat Discord Monitoring channel.')
            ->addOption(name: 'no-database', description: 'Runs the script without posting the resulting data on the dedicated Google Sheet file.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $noReport = !!$input->getOption('dry-run');
        $debug = !!$input->getOption('debug');
        $noDiscord = !!$input->getOption('no-discord');
        $noDatabase = !!$input->getOption('no-database');

        $month = $input->getOption('month');
        $year = $input->getOption('year');

        $start = new DateTime('now', new DateTimeZone('UTC'));
        
        if ($month && $year) {
            $month = (int) $month;
            $year = (int) $year;
        } else {
            $lastMonthDate = (clone $start)->modify('-1 month');
            $month = $lastMonthDate->format('m');
            $year = $lastMonthDate->format('Y');
        }


        $output->writeln(sprintf('Generating logs for %s/%s ...', $month, $year));

        $report = MatchLogHelper::buildAiReport(month: $month, year: $year);

        $config = [
            'month' => $month,
            'year' => $year,
        ];

        if (!isset($report['total']) || $report['total'] == ['No items matched found.']) {
            return $this->endExecution(
                output: $output,
                command: Command::FAILURE,
                message: 'No items matched found.',
                start: $start
            );
        }

        if ($debug) {
            $output->writeln($formattedReport = MatchLogHelper::formatReportText($report));
        }
        
        if ($noDatabase || $noReport) {
            $output->writeln('Skipping database report...');
        } else {
            $output->writeln('Recording report into database...');
            $databaseResponse = $this->saveReportToDatabase($report);
            $output->writeln($databaseResponse['status'] ? 'Report recorded!' : 'Report failed to save to the database.');

            if ($debug) {
                $output->writeln(json_encode($databaseResponse, JSON_PRETTY_PRINT));
            }
        }
        
        if ($noDiscord || $noReport) {
            $output->writeln('Skipping Discord report...');
        } else {
            $output->writeln('Posting report on Discord...');
            $discordResponse = $this->postReportOnDiscord(report: $formattedReport, config: $config);
            $output->writeln($discordResponse->getStatusCode() < 400 ? 'Report sent successfully!' : 'Failure to report.');

            if ($debug) {
                $output->writeln(sprintf('Response', $discordResponse->getContent()));
            }
        }

        return $this->endExecution(
            output: $output,
            command: Command::SUCCESS,
            message: 'The AI log reporting routine was executed successfully!',
            start: $start
        );        
    }

    private function endExecution(OutputInterface $output, int $command, string $message, DateTime $start = null): int
    {
        $output->writeln($message);

        if (!is_null($start)) {
            $end = new DateTime('now', new DateTimeZone('UTC'));
            $jobDuration = $end->diff($start, true);
            $seconds = $jobDuration->days * 24 * 60 * 60
                     + $jobDuration->h * 60 * 60
                     + $jobDuration->i * 60
                     + $jobDuration->s;
            $output->writeln(sprintf('Job duration: %s seconds', $seconds));
        }

        return $command;
    }

    private function postReportOnDiscord(string $report, array $config): ResponseInterface
    {
        $client = HttpClient::create();
        $webhookUrl = $_ENV['DISCORD_WEBHOOK_MONITORING'];
        $discordMessage = $this->buildDiscordMessage($report, $config);

        $response = $client->request(
            'POST',
            $webhookUrl,
            [
                'body' => $discordMessage,
                'headers' => [
                    'Content-Type' => 'application/json',
                ]
            ]
        );

        return $response;
    }

    private function buildDiscordMessage(string $messageBody, array $config): string
    {
        $discordMessage = new DiscordMessage();
        $discordMessage
            ->setDescription($messageBody)
            ->setColor('11d9d5');

        return json_encode($discordMessage);
    }

    private function saveReportToDatabase(array $report): array
    {
        try {
            /** @var AiPerformanceReportRepository $repository */
            $repository = $this->doctrine->getRepository(AiPerformanceReport::class);

            if (empty($reportEntity = $repository->findByPeriod($report['period']))) {
                /** @var AiPerformanceReport $reportEntity */
                $reportEntity = new AiPerformanceReport();
                $reportEntity->setDtc(new DateTime('now', new DateTimeZone('UTC')));
            } else {
                $reportEntity = $reportEntity[0];
            }

            $reportEntity
                ->setPeriod($report['period'])

                ->setTotalMatched($report['total']['items_matched'])
                ->setTotalCorrectlyMatched($report['total']['items_matched_correctly'])
                ->setTotalPredictionScore($report['total']['scores']['prediction_score'])
                ->setTotalPredictedGuessedSimilarity($report['total']['scores']['predicted_guessed_similarity'])
                ->setTotalPredictedMatchedSimilarity($report['total']['scores']['predicted_matched_similarity'])
                ->setTotalGuessedMatchedSimilarity($report['total']['scores']['guessed_matched_similarity'])

                ->setChMatched($report['COMPANIES_HOUSE']['items_matched'])
                ->setChCorrectlyMatched($report['COMPANIES_HOUSE']['items_matched_correctly'])
                ->setChPredictionScore($report['COMPANIES_HOUSE']['scores']['prediction_score'])
                ->setChPredictedGuessedSimilarity($report['COMPANIES_HOUSE']['scores']['predicted_guessed_similarity'])
                ->setChPredictedMatchedSimilarity($report['COMPANIES_HOUSE']['scores']['predicted_matched_similarity'])
                ->setChGuessedMatchedSimilarity($report['COMPANIES_HOUSE']['scores']['guessed_matched_similarity'])

                ->setHmrcMatched($report['HMRC']['items_matched'])
                ->setHmrcCorrectlyMatched($report['HMRC']['items_matched_correctly'])
                ->setHmrcPredictionScore($report['HMRC']['scores']['prediction_score'])
                ->setHmrcPredictedGuessedSimilarity($report['HMRC']['scores']['predicted_guessed_similarity'])
                ->setHmrcPredictedMatchedSimilarity($report['HMRC']['scores']['predicted_matched_similarity'])
                ->setHmrcGuessedMatchedSimilarity($report['HMRC']['scores']['guessed_matched_similarity'])

                ->setOtherMatched($report['OTHER']['items_matched'])
                ->setOtherCorrectlyMatched($report['OTHER']['items_matched_correctly'])
                ->setOtherPredictionScore($report['OTHER']['scores']['prediction_score'])
                ->setOtherPredictedGuessedSimilarity($report['OTHER']['scores']['predicted_guessed_similarity'])
                ->setOtherPredictedMatchedSimilarity($report['OTHER']['scores']['predicted_matched_similarity'])
                ->setOtherGuessedMatchedSimilarity($report['OTHER']['scores']['guessed_matched_similarity'])

                ->setNonStatutoryMatched($report['NON_STATUTORY']['items_matched'])
                ->setNonStatutoryCorrectlyMatched($report['NON_STATUTORY']['items_matched_correctly'])
                ->setNonStatutoryPredictionScore($report['NON_STATUTORY']['scores']['prediction_score'])
                ->setNonStatutoryPredictedGuessedSimilarity($report['NON_STATUTORY']['scores']['predicted_guessed_similarity'])
                ->setNonStatutoryPredictedMatchedSimilarity($report['NON_STATUTORY']['scores']['predicted_matched_similarity'])
                ->setNonStatutoryGuessedMatchedSimilarity($report['NON_STATUTORY']['scores']['guessed_matched_similarity'])

                ->setDtm(new DateTime('now', new DateTimeZone('UTC')))
            ;

            $repository->save($reportEntity);
        } catch (Exception $e) {
            return [
                'status' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'trace' => $e->getTrace(),
                ]
            ];
        }

        return [
            'status' => true,
            'data' => $reportEntity->toArray(),
        ];
    }
}
