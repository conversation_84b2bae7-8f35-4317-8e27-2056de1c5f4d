<?php

namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemEventsLog;
use App\Entity\PostItem\PostItemsDetail;
use DateTime;
use DateTimeZone;
use Doctrine\Persistence\ObjectManager;
use Exception;

final class RTSItem extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem,
        private object $operator
    )
    {}

    public function execute(): RTSItem
    {
        return $this->itemLogRTS();
    }
    
    public function itemLogRTS(): RTSItem
    {
        $itemRTSEventLog = new PostItemEventsLog();
        $itemRTSEventLog->setEventName("post_returned_to_sender");
        $itemRTSEventLog->setPostItemId($this->postItem);
        $itemRTSEventLog->setOperator($this->operator->email);
        $itemRTSEventLog->setDtc(new DateTime('now', new DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($itemRTSEventLog);
            $this->objectManager->flush();
        } catch (Exception $e) {
            $this->addError('error', "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }
}