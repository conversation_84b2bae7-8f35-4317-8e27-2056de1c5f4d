<?php

namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemEventsLog;
use App\Entity\PostItem\PostItemsDetail;
use Doctrine\Persistence\ObjectManager;
use DateTime;
use DateTimeZone;
use Exception;

final class ForwardItem extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem,
        private array $data,
        private object $operator
    )
    {}

    public static function getLabelImage(string $postageClass): string
    {
        if ($postageClass == 'second_class') {
            return $_ENV['SECOND_CLASS_LABEL_URL'];
        }
        return $_ENV['FIRST_CLASS_LABEL_URL'];
    }

    public function execute(): ForwardItem
    {
        return $this
            ->setPostItemStatus()
            ->setPostItemDetails()
            ->setPostItemsLog();
    }
    private function setPostItemStatus(): ForwardItem
    {
        $this->postItem->setStatus(PostItem::STATUS_FORWARDED);
        try {
            $this->objectManager->persist($this->postItem);
            $this->objectManager->flush();
        } catch (Exception $e) {
            $this->addError('error', "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }


    public function setPostItemDetails(): ForwardItem
    {
        foreach ($this->data as $key => $value) {
            if ($key != 'csrf_token' && ($key != 'custom_price' || $value)) {
                $postItemDetails = new PostItemsDetail();
                $postItemDetails->setPostItemId($this->postItem);
                $postItemDetails->setKey($key);
                $postItemDetails->setValue($key == 'custom_price' ? 0 : $value);
                $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

                try {
                    $this->objectManager->persist($postItemDetails);
                    $this->objectManager->flush();
                } catch (Exception $e) {
                    $this->addError("post_item_details", "An unknown error occurred while generating the file:" . $e->getMessage());
                    return $this;
                }
            }
        }

        return $this;
    }

    public function setPostItemsLog(): ForwardItem
    {
        $postItemsEventLog = new PostItemEventsLog();
        $postItemsEventLog->setEventName("address_label_generated");
        $postItemsEventLog->setPostItemId($this->postItem);
        $postItemsEventLog->setOperator($this->operator->email);
        $postItemsEventLog->setDtc(new DateTime('now', new DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($postItemsEventLog);
            $this->objectManager->flush();
        } catch (Exception $e) {
            $this->addError("post_items_log", "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }
}
