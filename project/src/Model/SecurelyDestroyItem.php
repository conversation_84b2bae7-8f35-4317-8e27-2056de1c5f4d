<?php

declare(strict_types=1);

namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemEventsLog;
use Doctrine\Persistence\ObjectManager;

class SecurelyDestroyItem extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem,
        private object $operator
    )
    {}

    /**
     * @throws \DateMalformedStringException
     */
    public function execute(): SecurelyDestroyItem
    {
        return $this->itemSecurelyDestroy();
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function itemSecurelyDestroy(): SecurelyDestroyItem
    {
        $itemSecurelyDestroyEventLog = new PostItemEventsLog();
        $itemSecurelyDestroyEventLog->setEventName("post_securely_destroyed");
        $itemSecurelyDestroyEventLog->setPostItemId($this->postItem);
        $itemSecurelyDestroyEventLog->setOperator($this->operator->email);
        $itemSecurelyDestroyEventLog->setDtc(new \DateTime('now', new \DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($itemSecurelyDestroyEventLog);
            $this->objectManager->flush();
        } catch (\Throwable $e) {
            $this->addError('error', "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }
}