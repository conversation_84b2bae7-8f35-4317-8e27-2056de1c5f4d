<?php

namespace App\Model;

abstract class Model
{
    protected array $errors = [];

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getError(string $key): ?string
    {
        return $this->errors[$key] ?? "";
    }

    public function hasAnyError(): bool
    {
        return !empty($this->errors);
    }

    public function hasError(string $key): bool
    {
        return isset($this->errors[$key]);
    }

    protected function addError(string $key, string $message): Model
    {
        $this->errors[$key] = $message;
        return $this;
    }
}
