<?php
namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemEventsLog;
use Doctrine\Persistence\ObjectManager;
use DateTime;
use DateTimeZone;

class PostItemEventLog extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem
    )
    {}

    public function save($eventName, $operator)
    {
        $event = new PostItemEventsLog();
        $event->setPostItemId($this->postItem);
        $event->setEventName($eventName);
        $event->setOperator($operator);
        $event->setDtc(new DateTime('now', new DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($event);
            $this->objectManager->flush();
            return $event;
        } catch (\Exception $e) {
            $this->addError("post_item_event", "An unknown error occurred while creating the Post Item Event:" . $e->getMessage());
            return false;
        }
    }
}