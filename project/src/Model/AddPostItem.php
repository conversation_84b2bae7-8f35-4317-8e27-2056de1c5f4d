<?php

namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemsDetail;
use App\Entity\PostItem\PostItemEventsLog;
use App\Model\PostItemDetail;
use Doctrine\Persistence\ObjectManager;
use DateTime;
use DateTimeZone;
use Exception;
use Monolog\Logger;

final class AddPostItem extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private $operator,
        private $companyNumber,
        private $companyName,
        private $type,
        private $sender,
        private ?string $batchName="N/A",
        private ?string $fileName="N/A",
        private ?string $filePath="N/A",
        private $scanned=false,
        private $status=PostItem::STATUS_ADDED,
        private ?Logger $logger = null
    )
    {
        $this->logger = $this->logger ?? new Logger('add_post_item_model');
    }

    private function getNonStatutoryPlaceholder($type): string
    {
        $nonStatutory = '';
        if ($type == 'non-statutory' || $type == 'regular') {
            $nonStatutory = 'non-statutory/';
        }
        return $nonStatutory;
    }

    private function generateCreationEvent($postItem){
        $creationEvent = new PostItemEventsLog();
        $creationEvent->setPostItemId($postItem);
        $creationEvent->setEventName(!!$this->scanned ? 'scanned' : 'added');
        $creationEvent->setOperator(is_object($this->operator) ? $this->operator->email : $this->operator);
        $creationEvent->setDtc(new DateTime('now', new DateTimeZone('UTC')));
        $creationEvent->setDtm(new DateTime('now', new DateTimeZone('UTC')));
        return $creationEvent;
    }

    public function save(): PostItem|bool
    {
        $postItem = new PostItem();
        $postItem->setCompanyNumber($this->companyNumber);
        $postItem->setCompanyName($this->companyName);
        $postItem->setType($this->type);
        $postItem->setSender($this->sender);
        $postItem->setOperator(is_object($this->operator) ? $this->operator->email : $this->operator);
        $postItem->setDtc(new DateTime('now', new DateTimeZone('UTC')));
        $postItem->setDtm(new DateTime('now', new DateTimeZone('UTC')));
        $postItem->setBatchNumber($this->batchName);
        $postItem->setFileName($this->fileName);
        $postItem->setStatus($this->status);

        try {
            $this->objectManager->persist($postItem);
            $this->objectManager->flush();
        } catch (\Throwable $th) {
            $this->addError("post_item_add", "An unknown error occurred while generating the file:" . $th->getMessage());
            $this->logger->error($th->getMessage(), ['exception' => $th]);
            return false;
        }

        $creationEvent = $this->generateCreationEvent($postItem);

        try {
            $this->objectManager->persist($creationEvent);
            $this->objectManager->flush();
        } catch (\Throwable $th) {
            $this->addError("post_item_add_event", "An unknown error occurred while generating the file:" . $th->getMessage());
            $this->objectManager->remove($postItem);
            $this->logger->error($th->getMessage(), ['exception' => $th]);
            return false;
        }

        if ($this->filePath != 'N/A') {
            $detail = new PostItemDetail($this->objectManager, $postItem);
            $detail->save(
                'pdf_link',
                sprintf(
                    '%spost_items/%s/%s.pdf',
                    $this->filePath,
                    (new DateTime('now', new DateTimeZone('UTC')))->format('Y_m_d'),
                    $postItem->getId()
                )
            );
        }
        
        return $postItem;
    }

}