<?php
namespace App\Model;

use App\Repository\PostItem\PostItemsDetailRepository;
use App\Entity\PostItem\{PostItem, PostItemsDetail};
use Doctrine\Persistence\ObjectManager;
use DateTime;
use DateTimeZone;

class PostItemDetail extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem
    )
    {}

    public function save($key, $value)
    {
        $detail = $this->objectManager->getRepository(PostItemsDetail::class)->findOneBy([
            'postItemId' => $this->postItem,
            'key' => $key
        ]);

        if (empty($detail)) {
            $detail = new PostItemsDetail();
            $detail->setPostItemId($this->postItem);
            $detail->setKey($key);
            $detail->setDtc(new DateTime('now', new DateTimeZone('UTC')));
        }

        $detail->setValue($value);
        $detail->setDtm(new DateTime('now', new DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($detail);
            $this->objectManager->flush();
            return $detail;
        } catch (Exception $e) {
            $this->addError("post_item_detail", "An unknown error occurred while generating the file:" . $e->getMessage());
            return false;
        }
    }
}