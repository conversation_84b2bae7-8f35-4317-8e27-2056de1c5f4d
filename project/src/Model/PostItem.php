<?php

namespace App\Model;

use App\Entity\PostItem\PostItem as EntityPostItem;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\Persistence\ObjectManager;
use Exception;

final class PostItem extends Model
{
    private string $companiesMadeSimpleUrl;
    private string $companiesMadeSimpleAuthorization;
    private string $mailroomSearchEndpoint;

    public function __construct(
        private ObjectManager $objectManager,
        private EntityPostItem $postItem
    )
    {
        $this->companiesMadeSimpleUrl = $_ENV['COMPANIES_MADE_SIMPLE_URL'];
        $this->companiesMadeSimpleAuthorization = $_ENV['COMPANIES_MADE_SIMPLE_AUTHORIZATION'];
        $this->mailroomSearchEndpoint = $_ENV['MAILROOM_SEARCH_ENDPOINT'];
    }

    public function getPostItemAddress(): array
    {
        $addressPattern = [
            'country' => "",
            'city' => "",
            'postcode' => "",
            'address' => "",
        ];
        $companySearchResponse = $this->companySearch();
        if ($companySearchResponse->getStatusCode() != 200) {
            return $addressPattern;
        }

        $companyResult = json_decode($companySearchResponse->getContent())[0] ?? null;
        if (empty($companyResult)) {
            return $addressPattern;
        }
        $address = $companyResult->address1;
        if (!empty($companyResult->address2)) {
            $address .= "<br/>" . $companyResult->address2;
        }
        if (!empty($companyResult->address3)) {
            $address .= "<br/>" . $companyResult->address3;
        }

        return [
            'country' => $companyResult->countryId ?? '',
            'city' => $companyResult->city ?? '',
            'postcode' => $companyResult->postcode ?? '',
            'address' => $address,
        ];
    }

    protected function companySearch(): Response
    {
        try {
            $response = HttpClient::create()->request(
                'GET',
                sprintf(
                    '%s%s?companyName=%s',
                    $this->companiesMadeSimpleUrl,
                    $this->mailroomSearchEndpoint,
                    $this->postItem->getCompanyName()
                ),
                [
                    'headers' => ['X-Msg-Auth' => $this->companiesMadeSimpleAuthorization]
                ]
            );
            return new Response($response->getContent());
        } catch (Exception $e) {
            return new Response($e->getMessage(), 500);
        }
    }
}
