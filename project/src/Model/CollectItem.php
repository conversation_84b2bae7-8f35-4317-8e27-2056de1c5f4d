<?php

namespace App\Model;

use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemEventsLog;
use App\Entity\PostItem\PostItemsDetail;
use Doctrine\Persistence\ObjectManager;
use DateTime;
use DateTimeZone;
use Exception;

final class CollectItem extends Model
{
    public function __construct(
        private ObjectManager $objectManager,
        private PostItem $postItem,
        private array $data,
        private object $operator
    )
    {}

    public function execute(): CollectItem
    {
        return $this
            ->setPostItemStatus()
            ->itemEventLogCollect()
            ->itemDetailCollect();
    }

    private function setPostItemStatus(): CollectItem
    {
        $this->postItem->setStatus(PostItem::STATUS_COLLECTED);
        try {
            $this->objectManager->persist($this->postItem);
            $this->objectManager->flush();
        } catch (Exception $e) {
            $this->addError('error', "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }

    public function itemEventLogCollect(): CollectItem
    {
        $itemCollectedLog = new PostItemEventsLog();
        $itemCollectedLog->setEventName("post_collected");
        $itemCollectedLog->setPostItemId($this->postItem);
        $itemCollectedLog->setOperator($this->operator->email);
        $itemCollectedLog->setDtc(new DateTime('now', new DateTimeZone('UTC')));

        try {
            $this->objectManager->persist($itemCollectedLog);
            $this->objectManager->flush();
        } catch (Exception $e) {
            $this->addError('error', "An unknown error occurred while generating the file:" . $e->getMessage());
        }

        return $this;
    }

    public function itemDetailCollect(): CollectItem
    {
        foreach ($this->data as $key => $value) {
            if($key || $value){
                $postItemDetails = new PostItemsDetail();
                $postItemDetails->setPostItemId($this->postItem);
                $postItemDetails->setKey($key);
                $postItemDetails->setValue($value);
                $postItemDetails->setDtc(new DateTime('now', new DateTimeZone('UTC')));

                try {
                    $this->objectManager->persist($postItemDetails);
                    $this->objectManager->flush();
                } catch (Exception $e) {
                    $this->addError("post_item_details", "An unknown error occurred while generating the file:" . $e->getMessage());
                    return $this;
                }
            }
        }

        return $this;
    }
}