<?php

namespace App\Twig;

use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class AppExtension extends AbstractExtension
{
    private $kernel;
    private $router;

    public function __construct(KernelInterface $kernel, UrlGeneratorInterface $router)
    {
        $this->kernel = $kernel;
        $this->router = $router;
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('pages_before_current', [$this, 'beforePages']),
            new TwigFunction('pages_after_current', [$this, 'afterPages']),
            new TwigFunction('last_page', [$this, 'lastPage']),
            new TwigFunction('first_page', [$this, 'firstPage']),
            new TwigFunction('column_header', [$this, 'buildColumnHeader']),
            new TwigFunction('version_asset', [$this, 'versionAsset'])
        ];
    }

    public function lastPage(int $lastPage, string $url, array $filter)
    {
        $filter['page'] = $lastPage;
        return $this->router->generate($url, $filter);
    }

    public function firstPage(string $url, array $filter)
    {
        $filter['page'] = 1;
        return $this->router->generate($url, $filter);
    }

    public function beforePages(
        int $range = 3,
        array $options = []
    ) {
        $currentPage = $options['filter']['page'];
        $htmlTemplate = '';
        for ($p = $currentPage - $range; $p <= $currentPage -1; $p++) {
            if ($p >= 1) {
                $options['filter']['page'] = $p;
                $url = $this->router->generate(
                    $options['url'] ?? '',
                    $options['filter'] ?? []
                );
                $htmlTemplate .= <<<EOF
                    <li class='page-item'>
                        <a class="page-link" href="$url">$p</a>
                    </li>
                EOF;
            }
        }

        return $htmlTemplate;
    }

    public function afterPages(
        int $totalPages,
        int $range = 3,
        array $options = []
    ) {
        $currentPage = $options['filter']['page'];
        $htmlTemplate = '';
        for ($p = $currentPage + 1; $p <= $currentPage + $range; $p++) {
            if ($p <= $totalPages) {
                $options['filter']['page'] = $p;
                $url = $this->router->generate(
                    $options['url'] ?? '',
                    $options['filter'] ?? []
                );
                $htmlTemplate .= <<<EOF
                    <li class='page-item'>
                        <a class="page-link" href="$url">$p</a>
                    </li>
                EOF;
            }
        }

        return $htmlTemplate;
    }

    public function buildColumnHeader(
        array $filter,
        array $name = [
            'Batch ID',
            'batchNumber'
        ],
        string $tooltipPlacement = 'top',
        array $terminology = [
            'Sorting items in ',
            'ascending',
            'descending',
            ' order.'
        ],
        bool $defaultASC = true,
        string $routeName = 'app_post_items'
    ) {
        $selected = isset($filter['sortBy']) && $filter['sortBy'] == $name[1];
        $currentDir = $filter['direction'] ?? '';

        if (!$selected) {
            $filter['direction'] = $defaultASC ? 'ASC' : 'DESC';
            $selected = !isset($filter['sortBy']) && $name[1] == 'dtc';
            if ($selected) {
                $filter['direction'] = 'ASC';
                $currentDir = 'DESC';
            }
        } else {
            $filter['direction'] = isset($filter['direction']) && $filter['direction'] == 'ASC' ? 'DESC' : 'ASC';
        }

        $filter['sortBy'] = $name[1];
        $tooltipContent = $terminology[0] . ($currentDir == 'ASC' ? $terminology[1] : $terminology[2]) . $terminology[3];
        $url = $this->router->generate($routeName, $filter);
        $htmlTemplate = '';
        $htmlTemplate .= '<th class="text-start p-2">';
        $htmlTemplate .= '<a class="text-dark d-block" style="font-size: 1rem;';
        if ($name[1] === 'events') {
            $htmlTemplate .= ' pe-none"';
        } else {
            $htmlTemplate .= '" href="'. $url .'"';
        }
        if ($selected) {
            $htmlTemplate .= <<<EOF
                data-bs-toggle="tooltip"
                data-bs-original-title="$tooltipContent"
                data-bs-placement="$tooltipPlacement"
            EOF;
        }
        $htmlTemplate .= <<<EOF
            >
            $name[0]
        EOF;
        if ($selected) {
            if ($currentDir == 'ASC') {
                $htmlTemplate .= <<<EOF
                    <span style="font-size: 0.8rem"> &#9650</span>
                EOF;
            } else {
                $htmlTemplate .= <<<EOF
                    <span style="font-size: 0.8rem"> &#9660</span>
                EOF;
            }
        }
        $htmlTemplate .= <<<EOF
                </a>
            </th>
        EOF;
        return $htmlTemplate;
    }

    public function versionAsset(string $assetUrl)
    {
        if(file_exists($file = $this->getPublicPath() . $assetUrl)) {
            return sprintf('%s?version=%s', $assetUrl, sha1_file($file));
        }

        return $file;
    }

    private function getPublicPath()
    {
        return $this->kernel->getProjectDir() . '/public/';
    }
}
