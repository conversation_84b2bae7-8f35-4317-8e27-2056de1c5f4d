<?php
namespace App\Listener;

use App\Service\DiscordMonitoringWebhook;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class MonitoringListener
{
    private $discordMonitoringWebhook;

    public function __construct(DiscordMonitoringWebhook $discordMonitoringWebhook)
    {
        $this->discordMonitoringWebhook = $discordMonitoringWebhook;
    }

    public function onKernelException(ExceptionEvent $event)
    {
        $this->discordMonitoringWebhook->shortTrace()->trigger($exception = $event->getThrowable());
    }
}