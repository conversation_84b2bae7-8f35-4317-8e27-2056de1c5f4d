<?php

namespace App\ParamConverter;

use App\Dto\Result;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;

class ResultParamConverter implements ParamConverterInterface
{
    public function apply(Request $request, ParamConverter $configuration)
    {
        $content = json_decode($request->getContent(), true);
        $request->attributes->set($configuration->getName(), new Result($content, unserialize($content['message']))); 
    }

    public function supports(ParamConverter $configuration): bool
    {
        return Result::class === $configuration->getClass();
    }
}
