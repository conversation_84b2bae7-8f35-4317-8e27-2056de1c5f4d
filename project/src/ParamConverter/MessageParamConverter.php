<?php

namespace App\ParamConverter;

use Google\Cloud\PubSub\Message;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;

class MessageParamConverter implements ParamConverterInterface
{
    public function apply(Request $request, ParamConverter $configuration)
    {
        $content = json_decode($request->getContent());
        $message = $content->message;
        $request->attributes->set($configuration->getName(), unserialize($message->message));
    }

    public function supports(ParamConverter $configuration): bool
    {
        return Message::class === $configuration->getClass();
    }
}
