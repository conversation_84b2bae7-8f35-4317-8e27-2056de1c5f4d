<?php

namespace App\ParamConverter;

use App\Dto\Topic;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;

class TopicParamConverter implements ParamConverterInterface
{
    public function apply(Request $request, ParamConverter $configuration)
    {
        $content = json_decode($request->getContent());
        $request->attributes->set($configuration->getName(), Topic::fromString($content->topic));
    }

    public function supports(ParamConverter $configuration): bool
    {
        return Topic::class === $configuration->getClass();
    }
}
