<template>
  <Header title="Post Items">
    <div class="d-inline-flex justify-content-center align-items-center mx-2" id="home-link-section">
      <ul class="d-block navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item d-inline-block px-1">
          <a class="nav-link" :href="dashboardRoute" style="cursor: pointer;" data-bs-toggle="tooltip"
            data-bs-placement="top" title="Home">
            <font-awesome-icon icon="fa-solid fa-home" />&nbsp;Home&nbsp;
          </a>
        </li>
        <li class="nav-item d-inline-block px-1">
          <a id="post-items-button" class="nav-link" :href="postItemsRoute" style="cursor: pointer;" data-bs-toggle="tooltip"
            data-bs-placement="top" title="Dashboard">
            <font-awesome-icon icon="fa-solid fa-list" />&nbsp;Post Items&nbsp;
          </a>
        </li>
        <li class="nav-item d-inline-block px-1">
          <a class="nav-link" :href="transitFeesRoute" style="cursor: pointer;" data-bs-toggle="tooltip"
            data-bs-placement="top" title="Dashboard">
            <font-awesome-icon icon="fa-solid fa-pound-sign" />&nbsp;Royal Mail Fees&nbsp;
          </a>
        </li>
      </ul>
    </div>
    <slot></slot>
  </Header>
</template>

<script lang="ts">
import Header from '@/components/Header.vue'
import { defineComponent } from "vue"
import { DASHBOARD_ROUTE_PATH, TRANSIT_FEES_ROUTE_PATH, POST_ITEMS_ROUTE_PATH } from "@/config/config"

export default defineComponent({
  name: 'DirectPagesHeader',
  components: {
    Header
  },
  data() {
    return {
      dashboardRoute: DASHBOARD_ROUTE_PATH,
      transitFeesRoute: TRANSIT_FEES_ROUTE_PATH,
      postItemsRoute: POST_ITEMS_ROUTE_PATH
    }
  },
})
</script>
