<template>
  <div class="container-fluid">
    <footer class="py-3 my-4 border-top row">
      <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
        <div class="d-block p-1">
          <h5 class="m-0">Mailroom</h5>
          <h6> MadeSimple </h6>
        </div>
      </div>
      <div class="col-md-6 mb-3 mb-md-0 me-md-auto link-dark text-decoration-none">
        <div class="row text-center text-md-end">
          <div class="col-12 mb-3 mb-md-0">
            <small class="text-muted">
              Having issues?
              <a href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            </small>
          </div>
          <div class="col-12 mb-3 mb-md-0">
            <div class="badge bg-light text-dark">
              <small>
                Build&nbsp;{{ buildCommit.slice(0, 8) }}&nbsp;({{ buildDate }})
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <!-- Spacer for the fixed statusbar -->
        <br/>
        <br/>
      </div>
    </footer>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Footer',
  computed: {
    buildDate(): string {
      const buildDate = process.env.VUE_APP_BUILD_DATE ? new Date(process.env.VUE_APP_BUILD_DATE) : new Date()
      return buildDate.toLocaleDateString()
    },
    buildCommit(): string {
      return process.env.VUE_APP_BUILD_COMMIT || 'local';
    },
  },
});
</script>
