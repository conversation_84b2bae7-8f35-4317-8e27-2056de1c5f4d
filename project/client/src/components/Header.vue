<template>
  <nav class="navbar navbar-expand bg-light">
    <div class="container-fluid">
      <div class="navbar-brand">
        <div class="p-2">
          <a class="d-inline-block pe-2 msg-header-brand" href="/">
            <img src="/logo.png" width="150" />
          </a>
          <div class="d-inline-block ps-2">Mailroom&nbsp;{{ title }}&nbsp;</div>
        </div>
      </div>
      <div class="d-inline-flex justify-content-end align-items-center">
        <Auth>
          <div class="d-inline-flex justify-content-center align-items-center mx-2" id="test-mode-section"
            v-if="shouldShowTestModeSection()">
            <div class="test-mode text-danger p-2 my-1 mx-2" style="font-size: x-large;"
              v-if="shouldShowTestModeIndicator()" v-bind:hidden="!isTestMode">
              &nbsp;You are now on test mode.&nbsp;
            </div>
            <div class="form-check form-switch" v-if="shouldShowTestModeSwitch()">
              <input class="form-check-input" type="checkbox" role="switch" ref="test-mode-switch"
                v-bind:checked="isTestMode" @change="updateTestModeIndicator($event)">
              <label class="form-check-label" for="test-mode-switch">Test-mode</label>
            </div>
          </div>
        </Auth>
        <div class="d-inline-flex justify-content-center align-items-center mx-2" id="slot-section">
          <slot></slot>
        </div>
        <Auth>
          <div class="d-inline-flex justify-content-center align-items-center mx-2" id="logout-link-section">
            <a class="nav-link" v-bind:href="logoutRoute" style="cursor: pointer;" data-bs-toggle="tooltip"
              data-bs-placement="top" title="Log Out">
              <font-awesome-icon icon="fa-solid fa-arrow-right-from-bracket" />&nbsp;Log Out&nbsp;
            </a>
          </div>
        </Auth>
      </div>
    </div>
  </nav>
</template>

<script lang="ts">
import Auth from "@/app-modules/auth/components/Auth.vue"
import EnvironmentHelper from "@/app-modules/helpers/EnvironmentHelper"
import { setupTestElements } from '@/main'
import { LOGOUT_ROUTE_PATH } from "@/config/config";
import { store } from "@/store"
import { library } from "@fortawesome/fontawesome-svg-core"
import {
  faArrowRightFromBracket,
  faHome,
  faList,
  faPoundSign,
} from '@fortawesome/free-solid-svg-icons'
import { defineComponent } from 'vue'

library.add(
  faArrowRightFromBracket,
  faHome,
  faList,
  faPoundSign,
)

export default defineComponent({
  name: 'Header',
  components: {
    Auth
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isDeveloper: false as boolean,
      isTestMode: EnvironmentHelper.isProductionEnvironment() as boolean,
      logoutRoute: LOGOUT_ROUTE_PATH,
      userEmail: ''
    }
  },
  methods: {
    shouldShowTestModeSection(): boolean {
      return !EnvironmentHelper.isProductionEnvironment() && this.isDeveloper
    },
    shouldShowTestModeIndicator(): boolean {
      return this.shouldShowTestModeSection() && this.isTestMode
    },
    shouldShowTestModeSwitch(): boolean {
      return this.shouldShowTestModeSection()
    },
    updateTestModeIndicator(event: Event): void {
      // @ts-ignore
      const checked = event.target ? (event.target.checked === true) : false
      store.commit('setTestMode', checked)
    },
    updateStatus(): void {
      this.isTestMode = store.state.testMode
      this.userEmail = store.state.userEmail
      this.isDeveloper = store.state.isDeveloper
    }
  },
  created() {
    this.updateStatus()

    this.$store.watch(() => this.$store.getters.mutated, async mutated => {
      this.updateStatus()

      this.$forceUpdate()
    })
  },
  watch: {
    isTestMode: function (): void {
      setupTestElements()
    }
  },
  mounted() {
    setupTestElements()
  }
})
</script>
