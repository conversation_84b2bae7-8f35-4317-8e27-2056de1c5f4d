<template>
  <div class="modal fade" id="testModeBatchNoticeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 40rem;">
      <div class="modal-content d-flex-column gap-4 align-items-center p-4">
        <div class="text-center fs-5 w-75">
          Batches that have already been created will <b>not</b> have their
          <span class="bg-dark text-danger font-monospace p-1 rounded">isTest</span> value updated.
        </div>
        <div class="d-flex gap-4 align-items-center">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="max-width: 6rem;">
            Okay
          </button>
          <a type="button" class="link link-primary" data-bs-dismiss="modal" @click="dismissTestModeBatchNotice()">
            Don't warn me again
          </a>
        </div>
      </div>
    </div>
    <button type="button" class="hidden-trigger" data-bs-toggle="modal" data-bs-target="#testModeBatchNoticeModal"
      style="display: none;">
    </button>
  </div>
</template>

<script lang="ts">
import { store } from "@/store"
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'TestModeBatchNotice',
  methods: {
    dismissTestModeBatchNotice() {
      store.commit('setTestModeBatchNoticeDismissed', true)
    }
  }
})
</script>
