<template>
  <div v-if="runningTasks()">
    <nav class="navbar fixed-bottom navbar-light bg-light">
      <div class="container-fluid">
        <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
          <li class="nav-item">
            <div class="nav-link">
              <div class="d-inline text-muted">
                <span role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                </span>
              </div>
              <div class="d-inline badge bg-info border-info border text-white ms-1" v-if="task">
                   {{ task }}
              </div>
              <div class="d-inline badge bg-success border-info border text-white ms-1" v-if="autoMatchingFiles">
                Auto-matching files
              </div>
              <div class="d-inline badge bg-transparent border-secondary border text-secondary ms-1" v-if="thread0">
                {{ thread0 }}
              </div>
              <div class="d-inline badge bg-transparent border-secondary border text-secondary ms-1" v-if="thread1">
                {{ thread1 }}
              </div>
              <div class="d-inline badge bg-transparent border-danger border text-danger ms-1" v-if="thread2">
                {{ thread2 }}
              </div>
              <div class="d-inline badge bg-transparent border-secondary border text-muted ms-1" v-if="thread3">
                {{ thread3 }}
              </div>
              <div class="d-inline badge bg-transparent border-success border text-success ms-1" v-if="thread4">
                {{ thread4 }}
              </div>
              <div class="d-inline badge bg-transparent border-secondary border text-muted ms-1" v-if="thread5">
                {{ thread5 }}
              </div>
            </div>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</template>

<script lang="ts" setup>
import {ref, watch} from "vue";
import {useStore} from "vuex";

const store = useStore();

let updatingWaiting = ref(false),
    updatingStatus = ref(false),
    updatingStatusInstances = ref(0),
    uploading = ref(false),
    performingSearch = ref(false),
    deletingFile = ref(false),
    matchingFile = ref(false),
    autoMatchingFiles = ref(false),
    task = ref(``),
    thread0 = ref(``),
    thread1 = ref(``),
    thread2 = ref(``),
    thread3 = ref(``),
    thread4 = ref(``),
    thread5 = ref(``)

function runningTasks() {
  return (
      uploading.value ||
      updatingStatusInstances.value > 0 ||
      updatingWaiting.value ||
      performingSearch.value ||
      deletingFile.value ||
      matchingFile.value ||
      autoMatchingFiles.value
  )
}

watch(() => store.getters.mutated,   async () => {
  updatingWaiting.value = store.state.appState.updatingWaiting
  updatingStatus.value = store.state.appState.updatingStatus
  updatingStatusInstances.value = store.state.appState.updatingStatusInstances
  performingSearch.value = store.state.appState.performingSearch
  deletingFile.value = store.state.appState.deletingFile
  matchingFile.value = store.state.appState.matchingFile
  autoMatchingFiles.value = store.state.appState.autoMatchingFiles
  task.value = store.state.appState.task
  thread0.value = store.state.appState.thread0
  thread1.value = store.state.appState.thread1
  thread2.value = store.state.appState.thread2
  thread3.value = store.state.appState.thread3
  thread4.value = store.state.appState.thread4
  thread5.value = store.state.appState.thread5
  uploading.value = store.state.appState.uploading
})
</script>