<template>
  <Header title="Dashboard">
    <Auth>
      <ul class="d-block navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item d-inline-block px-1">
          <div id="post-items-button" class="nav-link" @click="goToPostItemList()" style="cursor: pointer;" data-bs-toggle="tooltip"
            data-bs-placement="top" title="Post Items List">
            <font-awesome-icon icon="fa-solid fa-list" />&nbsp;Post Items&nbsp;
          </div>
        </li>
        <li class="nav-item d-inline-block px-1">
          <div class="nav-link" @click="goToTransitFeePage()" style="cursor: pointer;" data-bs-toggle="tooltip"
            data-bs-placement="top" title="Post Items List">
            <font-awesome-icon icon="fa-solid fa-pound-sign" />&nbsp;Royal Mail Fees&nbsp;
          </div>
        </li>
        <li class="nav-item dropdown d-inline-block px-1">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"
            title="Settings">
            <font-awesome-icon icon="fa-solid fa-gear fa-fw" />&nbsp;Settings&nbsp;
          </a>
          <ul class="dropdown-menu dropdown-menu-end">
            <li v-show="ready && hasTrackedFiles" data-bs-toggle="tooltip" data-bs-placement="left"
              title="Dismisses all saved files currently tracked by this client.">
              <div class="dropdown-item" v-show="hasTrackedFiles" @click="dismissSavedFiles()">
                <font-awesome-icon icon="fa-solid fa-ban" /> Dismiss all saved files
              </div>
            </li>
            <li v-show="debugMode" data-bs-toggle="tooltip" data-bs-placement="left"
              title="Purges all files currently tracked by this client from local storage only.">
              <div class="dropdown-item" v-show="hasTrackedFiles" @click="purgeLocallyTrackedFiles()">
                <font-awesome-icon icon="fa-solid fa-ban" /> Purge all locally tracked files
              </div>
            </li>
            <li v-show="ready" data-bs-toggle="tooltip" data-bs-placement="left"
              title="Deletes all files currently tracked in all batches for this client.">
              <div class="dropdown-item" v-show="hasTrackedFiles" data-bs-toggle="modal"
                data-bs-target="#purgeFilesConfirmationModal">
                <font-awesome-icon icon="fa-solid fa-trash" /> Delete all tracked files and batches
              </div>
            </li>
            <li data-bs-toggle="tooltip" data-bs-placement="left"
              title="Allows requesting manual updates for status of tracked files.">
              <div v-show="!allowBackgroundUpdate" class="dropdown-item" @click="runManualUpdateStatus()">
                <font-awesome-icon icon="fa-solid fa-rotate" /> Update tracked files status
              </div>
            </li>
            <li data-bs-toggle="tooltip" data-bs-placement="left"
              title="When disabled this will prevent new updates from running in the background. Currently running ones are unaffected.">
              <div class="dropdown-item">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked0"
                    v-on:change="setBackgroundUpdate()" v-bind:checked="allowBackgroundUpdate" />
                  <label class="form-check-label" for="flexSwitchCheckChecked0"> Update in the background </label>
                </div>
              </div>
            </li>
            <li data-bs-toggle="tooltip" data-bs-placement="left"
              title="When enabled items with high confidence are auto-matched when a company is found.">
              <div class="dropdown-item">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked1"
                    v-on:change="setAutoMatch()" v-bind:checked="allowAutoMatch" />
                  <label class="form-check-label" for="flexSwitchCheckChecked1"> Auto-match on high confidence </label>
                </div>
              </div>
            </li>
            <li data-bs-toggle="tooltip" data-bs-placement="left"
              title="When enabled clicking on edit will run an automatic search for the selected item based on the predicted company name.">
              <div class="dropdown-item">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked2"
                    v-on:change="setAutoSearch()" v-bind:checked="allowAutoSearch" />
                  <label class="form-check-label" for="flexSwitchCheckChecked2"> Auto-search on edit </label>
                </div>
              </div>
            </li>
            <li v-show="hasStats">
              <hr class="dropdown-divider">
            </li>
            <li v-show="ready && debugMode && hasStats" data-bs-toggle="tooltip" data-bs-placement="left" title="
                  Statistics on status messages.
                  Used/Received: The number of messages used and received in the current batch.
                  Not tracked: Item belongs to this operator but it is from an older batch.
                  Not used:  Likely an older message for this batch but the item already has a more relevant state present.
                ">
              <div class="dropdown-item bg-light text-dark" v-if="hasStats">
                <div>
                  <font-awesome-icon icon="fa-solid fa-message" />
                  Used/Received: {{ stats.statusMessages.messagesUsed }}/{{ stats.statusMessages.messagesReceived }}
                </div>
                <div class="text-success">
                  <font-awesome-icon icon="fa-solid fa-message" />
                  Acknowledged: {{ stats.statusMessages.messagesACK }}
                </div>
                <div>
                  <font-awesome-icon icon="fa-solid fa-message" />
                  Not tracked: {{ stats.statusMessages.messagesForMeButNotTracked }}
                </div>
                <div>
                  <font-awesome-icon icon="fa-solid fa-message" />
                  Not used: {{ stats.statusMessages.messagesIgnored }}
                </div>
              </div>
            </li>
          </ul>
        </li>
      </ul>
      <TestModeBatchNotice></TestModeBatchNotice>
    </Auth>
  </Header>
</template>

<script lang="ts">
// TODO: Split this component into DashboardHeader AND DashboardHeaderSettings component

import { AppState } from '@/app-modules/app-state/AppState'
import Auth from '@/app-modules/auth/components/Auth.vue'
import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper'
import { MAX_STATUS_UPDATE_INSTANCES } from "@/config/config";
import { PostItem } from '@/app-modules/post-items/entities/PostItem'
import { Status } from '@/app-modules/post-items/entities/Status'
import Header from '@/components/Header.vue'
import TestModeBatchNotice from '@/components/TestModeBatchNotice.vue'
import { setupTestElements } from '@/main'
import { store } from '@/store'
import { size } from 'lodash'
import { defineComponent, ref } from 'vue'
import { POST_ITEMS_ROUTE_PATH, TRANSIT_FEES_ROUTE_PATH } from "@/config/config";

export default defineComponent({
  name: 'DashboardHeader',
  components: {
    Auth,
    Header,
    TestModeBatchNotice
  },
  data() {
    return {
      isTestMode: ref(false),
      render: ref(false),
      debugMode: ref(false),
      ready: ref(false),
      updatingWaiting: ref(false),
      updatingStatus: ref(false),
      updatingStatusInstances: ref(0),
      performingSearch: ref(false),
      deletingFile: ref(false),
      matchingFile: ref(false),
      autoMatchingFiles: ref(false),
      hasTrackedFiles: ref(false),
      hasStatusMessages: ref(false),
      hasStats: ref(false),
      stats: {} as any,
      allowBackgroundUpdate: ref(true),
      allowAutoMatch: ref(false),
      allowAutoSearch: ref(false)
    }
  },
  methods: {
    goToPostItemList(): void {
      window.location.assign(POST_ITEMS_ROUTE_PATH)
    },
    goToTransitFeePage(): void {
      window.location.assign(TRANSIT_FEES_ROUTE_PATH)
    },
    isProdEnv(): boolean {
      return EnvironmentHelper.isProductionEnvironment()
    },
    hasBatches(): boolean {
      return size(store.state.batches) > 0
    },
    bringUpTestModeBatchNoticeModal(): void {
      if (!store.state.testModeBatchNoticeDismissed && this.hasBatches()) {
        const modal = document.getElementById('testModeBatchNoticeModal')
        if (modal) {
          const button = modal.querySelector('button.hidden-trigger')
          if (button) {
            // @ts-ignore
            button.click()
          }
        }
      }
    },
    dismissSavedFiles(): void {
      PostItem.dismissSavedFiles()
    },
    purgeLocallyTrackedFiles(): void {
      AppState.resetTrackedFiles()
    },
    runManualUpdateStatus(): void {
      new AppState({
        task: `Running update tasks manually`,
        busy: false
      })
      this.pullStatusIfNotUpdatingStatus()
    },
    pullStatusIfNotUpdatingStatus() {
      const isInstancesLessThanMax = store.state.appState.updatingStatusInstances < MAX_STATUS_UPDATE_INSTANCES
      if (!store.state.appState.updatingStatus || isInstancesLessThanMax) {
        Status.pull()
      }
    },
    setBackgroundUpdate(): void {
      this.allowBackgroundUpdate = !this.allowBackgroundUpdate

      new AppState({
        allowBackgroundUpdate: this.allowBackgroundUpdate
      })

      if (this.allowBackgroundUpdate) {
        new AppState({
          task: ``
        })
      }
    },
    setAutoMatch(): void {
      this.allowAutoMatch = !this.allowAutoMatch

      new AppState({
        allowAutoMatch: this.allowAutoMatch
      })

      if (this.allowAutoMatch) {
        new AppState({
          task: ``
        })
      }
    },
    setAutoSearch(): void {
      this.allowAutoSearch = !this.allowAutoSearch

      new AppState({
        allowAutoSearch: this.allowAutoSearch
      })
    },
    isDebugModeOn(): boolean {
      return (this.$route.query.debug === 'on') || (this.$route.query.debug === 'true')
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated, async mutated => {
      this.isTestMode = store.state.testMode
      this.debugMode = store.state.appState.debug
      this.ready = store.state.appState.ready
      this.allowBackgroundUpdate = store.state.appState.allowBackgroundUpdate
      this.allowAutoMatch = store.state.appState.allowAutoMatch
      this.allowAutoSearch = store.state.appState.allowAutoSearch
      this.updatingWaiting = store.state.appState.updatingWaiting
      this.updatingStatus = store.state.appState.updatingStatus
      this.updatingStatusInstances = store.state.appState.updatingStatusInstances
      this.performingSearch = store.state.appState.performingSearch
      this.deletingFile = store.state.appState.deletingFile
      this.matchingFile = store.state.appState.matchingFile
      this.autoMatchingFiles = store.state.appState.autoMatchingFiles
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.hasStatusMessages = size(store.state.statusMessages) > 0
      this.hasStats = size(store.state.stats) > 0
      this.stats = store.state.stats

      this.$forceUpdate()
    })

    this.debugMode = this.isDebugModeOn()
  },
  watch: {
    isTestMode: function (): void {
      this.bringUpTestModeBatchNoticeModal()
      setupTestElements()
    },
    debugMode: function (): void {
      this.debugMode = this.isDebugModeOn()
      new AppState({
        debug: this.debugMode
      })
    }
  },
  mounted() {
    this.debugMode = this.isDebugModeOn()
  },
})
</script>
