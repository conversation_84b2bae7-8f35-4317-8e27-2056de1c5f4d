<template>
  <div class="container">
    <div class="alert alert-secondary bg-transparent border-0 p-3 my-1">
      <div class="spinner-container text-center w-100 p-3 d-flex align-items-center justify-content-center">
        <div class="spinner-icon spinner-border" role="status"></div>
        <div class="spinner-loading-message mx-2">Please wait</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .spinner-container .spinner-icon {
    border: 4px solid #8ebbde;
    border-top: 5px solid #0D6EFD;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    animation: spin 0.8s linear infinite;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .spinner-container .spinner-loading-message {
    font-size: 16px;
    margin-left: 10px;
  }
</style>