/***********************************************************************************************************************
 * Default Configuration File
 *
 * This configuration file contains the values for many constants and settings for the application.
 * It's versioned, so keep that in mind when creating new constants for credential/sensitive data values.
 * It's essential to document these values for clarity and maintainability.
 *
 * ***DON'T USE THIS TO STORE PRODUCTION ENVIRONMENT VALUES!!!***
 **********************************************************************************************************************/

// Allowed domains for authentication provider service
export const ALLOWED_AUTH_PROVIDER_DOMAINS: Array<string> = [
    process.env.VUE_APP_DEV_AUTH_DOMAIN,
    process.env.VUE_APP_MSG_AUTH_DOMAIN
]

export const SENTRY_DSN = process.env.VUE_APP_SENTRY_DSN

export const CUSTOM_TOKEN_ENCRYPTION_KEY = process.env.VUE_APP_CUSTOM_TOKEN_ENCRYPTION_KEY
export const UNIVERSAL_LOGIN_URL = process.env.VUE_APP_UNIVERSAL_LOGIN_URL
// Maximum status update instances
export const MAX_STATUS_UPDATE_INSTANCES: number = 2

// Minimum confidence level to auto-match items on Dashboard
export const MIN_AUTO_MATCH_CONFIDENCE: number = 100.0

// Firebase auth service credential key (json object)
export const FIREBASE_KEY: object = JSON.parse(atob(process.env.VUE_APP_FIREBASE_KEY ?? '')) ?? {}

export const IMAGE_STORAGE_URL = process.env.VUE_APP_IMAGE_STORAGE_URL
export const PDF_STORAGE_URL = process.env.VUE_APP_PDF_STORAGE_URL

export const MAILROOM_API_ACKNOWLEDGE_URL = process.env.VUE_APP_MAILROOM_API_ACKNOWLEDGE_URL
export const MAILROOM_API_PULL_STATUS_URL = process.env.VUE_APP_MAILROOM_API_PULL_STATUS_URL
export const MAILROOM_SEARCH_API = process.env.VUE_APP_MAILROOM_SEARCH_API

export const COMPANIES_MADE_SIMPLE_AUTHORIZATION = process.env.VUE_APP_COMPANIES_MADE_SIMPLE_AUTHORIZATION

export const MANTLE_API_BUCKET_URL = process.env.VUE_APP_MANTLE_API_BUCKET_URL
export const MANTLE_API_BUCKET_AUTH_TOKEN = process.env.VUE_APP_MANTLE_API_BUCKET_AUTH_TOKEN

export const MANTLE_API_MATCH_URL = process.env.VUE_APP_MANTLE_API_MATCH_URL
export const MANTLE_API_MATCH_AUTH_TOKEN = process.env.VUE_APP_MANTLE_API_MATCH_AUTH_TOKEN

export const LOGIN_ROUTE_PATH = '/'
export const LOGOUT_API_ROUTE_PATH = '/api/session/end/'
export const LOGOUT_ROUTE_PATH = '/logout'
export const DASHBOARD_ROUTE_PATH = '/dashboard'
export const POST_ITEMS_ROUTE_PATH = '/direct/post/items'
export const TRANSIT_FEES_ROUTE_PATH = '/direct/transit-fees'
