<template>
  <div>
    <AuthStateWatcher />
    <router-view/>
    <AppStateBar/>
    <Footer/>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount } from 'vue';
import { AppState } from "./app-modules/app-state/AppState";
import { library } from "@fortawesome/fontawesome-svg-core"
import {
  faHourglassHalf,
  faExclamationTriangle,
  faCheck,
  faPlus,
  faRotateRight,
  faRobot,
  faMessage,
  faBug,
  faClock,
  faCode,
  faFloppyDisk,
  faAdd,
  faUser,
  faLock,
  faGear,
  faCircleNotch,
  faTrash,
  faPencil,
  faCheckCircle,
  faArrowRightFromBracket,
  faTriangleExclamation,
  faRotate,
  faCodeBranch,
  faBan,
  faSearch,
  faBuilding,
  faIdBadge,
  faBarcode,
  faXmark,
  faEnvelope,
  faEyeSlash,
  faEye,
  faUpload,
  faEnvelopesBulk,
  faTimes,
  faSortUp,
  faSortDown,
  faList, faPoundSign
} from '@fortawesome/free-solid-svg-icons'
import AuthStateWatcher from "@/app-modules/firebase/components/AuthStateWatcher.vue";
import AppStateBar from "@/components/AppStateBar.vue";
import Footer from "@/components/Footer.vue";

library.add(
    faHourglassHalf,
    faExclamationTriangle,
    faCheck,
    faPlus,
    faRotateRight,
    faRobot,
    faMessage,
    faBug,
    faClock,
    faCode,
    faFloppyDisk,
    faAdd,
    faUser,
    faLock,
    faGear,
    faCircleNotch,
    faTrash,
    faPencil,
    faCheckCircle,
    faArrowRightFromBracket,
    faTriangleExclamation,
    faRotate,
    faCodeBranch,
    faBan,
    faSearch,
    faBuilding,
    faIdBadge,
    faBarcode,
    faXmark,
    faEnvelope,
    faEyeSlash,
    faEye,
    faUpload,
    faEnvelopesBulk,
    faTimes,
    faSortUp,
    faSortDown,
    faList,
    faPoundSign,
)

onBeforeMount(() => {
  new AppState({
    ready: false,
  })
})
</script>