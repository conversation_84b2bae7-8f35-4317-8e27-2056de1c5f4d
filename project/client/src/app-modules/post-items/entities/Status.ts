import axios from 'axios';
import { ackDeadline, statuses } from '@/app-modules/post-items/StatusConfig';
import { AppState } from '@/app-modules/app-state/AppState';
import { debug } from '@/app-modules/helpers/debug';
import { elapsedTimeInSeconds } from '@/app-modules/helpers/clock';
import { filter, size } from 'lodash';
import { isErrorStatus } from '@/app-modules/post-items/StatusConfig';
import { MAILROOM_API_ACKNOWLEDGE_URL } from '@/config/config';
import { MAILROOM_API_PULL_STATUS_URL } from '@/config/config';
import { MAX_STATUS_UPDATE_INSTANCES } from '@/config/config';
import { PostItem } from '@/app-modules/post-items/entities/PostItem';
import { PostItemLogEntry } from '@/app-modules/app-state/entities/PostItemLogEntry';
import { Stats } from '@/app-modules/app-state/entities/Stats';
import { store } from '@/store';

export const TTL = 120 // Time to live for file statuses in minutes
export class Status {
    ack_id: any
    toAck: Array<String> = []
    attributes: any
    data: any
    delivery_attempt: any
    message_id: any
    ordering_key: any
    published_at: any
    acknowledged: any
    dtc: any

    private static mrUiStatusApiUrlAck: string = MAILROOM_API_ACKNOWLEDGE_URL
    private static mrUiStatusApiUrlPull: string = MAILROOM_API_PULL_STATUS_URL

    constructor() {

    }

    async build({
        ack_id = null,
        attributes = null,
        data = null,
        delivery_attempt = null,
        message_id = null,
        ordering_key = null,
        published_at = null,
        acknowledged = null
    }: any) {
        if (ack_id) {
            this.ack_id = ack_id
            this.attributes = this.getValue("attributes", attributes)
            this.data = this.getValue("data", data)
            this.delivery_attempt = this.getValue("delivery_attempt", delivery_attempt)
            this.message_id = this.getValue("message_id", message_id)
            this.ordering_key = this.getValue("ordering_key", ordering_key)
            this.published_at = this.getValue("published_at", published_at)
            this.acknowledged = this.getValue("acknowledged", acknowledged, false)
            this.dtc = this.getValue("dtc", null, Date.now())
        }
        this.toAck = Status.getMessagesPendingAck()
    }

    private getValue(key: string, input: any = null, defaultsTo: any = ``): any {
        try {
            return (input != null ? input : defaultsTo)
        } catch (e) {
            return defaultsTo
        }
    }

    private static getMessagesPendingAck(): any {
        try {
            return store.state.toAck || []
        } catch (e) {
            return []
        }
    }

    static getWeight(key: string): number {
        try {
            return statuses[key].weight
        } catch (e) {
            debug(e)
            return 0
        }
    }

    static getPriority(key: string): number {
        try {
            return statuses[key].priority
        } catch (e) {
            debug(e)
            return 0
        }
    }

    private static setMessagesPendingAck(messagesToAck: any) {
        store.commit("updateMessagesToAck", messagesToAck)
    }

    static async pull() {
        try {
            const hasTrackedFiles = await AppState.hasTrackedFiles()
            if (hasTrackedFiles) {

                AppState.incrementUpdatingStatusInstances()

                new AppState({
                    updatingStatus: true,
                    thread1: `Updating status of files ${store.state.appState.updatingStatusInstances}/${MAX_STATUS_UPDATE_INSTANCES}`
                })

                const alreadyStoredMessages = Status.getMessagesPendingAck()
                const batches = store.state.batches
                const batchesString = Object.keys(batches)

                const { data } = await axios.get(
                    `${Status.mrUiStatusApiUrlPull}?uid=${store.state.uid}&batch_ids=${batchesString}`
                )

                if (typeof data.messages === 'object') {
                    for (let message in data.messages) {
                        if (alreadyStoredMessages.includes(data.messages[message].message_id)) {
                            delete data.messages[message]
                        }
                    }

                    for (let itemIndex in data.messages) {
                        if (data.messages[itemIndex]) {
                            await Stats.incrementStatusMessagesStats(`messagesReceived`)
                            const status = new Status()
                            status.build({
                                ack_id: data.messages[itemIndex].message_id,
                                data: data.messages[itemIndex].data,
                                message_id: data.messages[itemIndex].message_id,
                                published_at: data.messages[itemIndex].publishTime
                            }).then(() => {
                                status.updatePostItem()
                            })
                        }
                    }
                }
                // Sleep if you need some time in between pull calls
                // const sleepTimer = Math.round(Math.random() * 10000)
                // new AppState({
                //     thread1: `Waiting for ${Math.round(sleepTimer / 1000)} second(s)`
                // })
                // await sleep(sleepTimer)

                AppState.decrementUpdatingStatusInstances()
                if (store.state.appState.updatingStatusInstances === 0) {
                    new AppState({
                        updatingStatus: false,
                        thread1: ''
                    })
                }
                return data.messages
            }
            return {}
        } catch (e) {
            debug(e)
            AppState.decrementUpdatingStatusInstances()
            new AppState({
                updatingStatus: false,
                thread1: ''
            })
            return {}
        }
    }

    public async updatePostItem(): Promise<void> {
        try {
            if (this.data.pdf_filename) {
                const item = new PostItem(false, true)
                await item.build(this.data)
                const currentOperationStatus = await item.getValue('operation_status', null, '')

                if (item.isBeingTracked()) {
                    if (Status.shouldStoreLog(this.data.operation_status)) {
                        await new PostItemLogEntry().build({
                            pdf_filename: item.pdf_filename,
                            message: 'Item is being tracked, checking if should update...',
                            operation_info: this.data.operation_info,
                            operation_status: this.data.operation_status
                        })
                    }

                    const isStatusHeavier = Status.getWeight(this.data.operation_status) > Status.getWeight(currentOperationStatus)

                    if (isStatusHeavier) {
                        item.disableOperationDataProtection()
                        await item.setOperationData({
                            operation_status: this.data.operation_status,
                            operation_info: this.data.operation_info
                        })
                        item.enableOperationDataProtection()
                        item.save()
                        await Stats.incrementStatusMessagesStats(`messagesUsed`)
                        await Stats.updatePostItemStatsInBatch(item)
                    } else {
                        await Stats.incrementStatusMessagesStats(`messagesIgnored`)
                    }

                    this.markForAck()
                    if (Status.shouldStoreLog(this.data.operation_status)) {
                        await new PostItemLogEntry().build({
                            pdf_filename: item.pdf_filename,
                            message: 'Finished acknowledging message...',
                            operation_info: this.data.operation_info,
                            operation_status: this.data.operation_status
                        })
                    }
                }
                else {
                    await Stats.incrementStatusMessagesStats(`messagesIgnored`)
                    if (Status.belongsToThisOperator(this)) {
                        await Stats.incrementStatusMessagesStats(`messagesForMeButNotTracked`)
                        this.markForAck()
                    }
                }
            } else {
                await Stats.incrementStatusMessagesStats(`messagesIgnored`)
                // message does not contain pdf_filename
                this.markForAck()
            }
        } catch (e) {
            debug(e)
        }
    }

    public markForAck() {
        try {
            this.toAck = Status.getMessagesPendingAck()
            this.toAck.push(this.ack_id)
            Status.setMessagesPendingAck(this.toAck)
        } catch (e) {
            debug(e)
        }
    }

    public static async acknowledge(): Promise<void> {
        try {
            let ackList = Status.getMessagesPendingAck()
            if (size(ackList) > 0) {
                new AppState({
                    acknowledging: true
                })

                const { data } = await axios.post(
                    Status.mrUiStatusApiUrlAck,
                    { ids: ackList }
                )

                // We must remove only the acked messages.
                // Because we await for the request, the list to ack might have changed.
                const latestAckList = Status.getMessagesPendingAck()
                ackList = filter(latestAckList, (id: string) => {
                    return !ackList.includes(id)
                })

                Status.setMessagesPendingAck(ackList)

                new AppState({
                    acknowledging: false
                })
            }
        } catch (e) {
            debug(e)
        }
    }

    static hasError(file: any): boolean {
        try {
            return file.operation_status.includes("ERROR") || file.operation_status.includes("FAIL")
        } catch (e) {
            debug(e)
            return false
        }
    }

    static isSaving(file: any): boolean {
        try {
            return file.operation_status === "COMPLETED_START"
        } catch (e) {
            debug(e)
            return false
        }
    }

    static isCompleted(file: any): boolean {
        try {
            return file.operation_status === "COMPLETED_SUCCESS"
        } catch (e) {
            debug(e)
            return false
        }
    }

    static isLocked(file: any): boolean {
        return Status.isSaving(file) || Status.isCompleted(file)
    }

    static isStuck(file: any) {
        return Status.getWeight(file.operation_status) == 1001 && elapsedTimeInSeconds(file.dtm) > 300
    }

    static isNewer(currentDate: string, receivedDate: string): boolean {
        try {
            return (new Date(receivedDate).getTime() > new Date(currentDate).getTime())
        } catch (e) {
            debug(e)
            return false
        }
    }

    public static belongsToThisOperator(status: Status) {
        try {
            return status.data.operator && status.data.operator === store.state.uid
        } catch (e) {
            debug(e)
            return false
        }
    }

    public static deadlineExceeded(status: Status) {
        try {
            return elapsedTimeInSeconds(status.dtc) / 60 > ackDeadline
        } catch (e) {
            debug(e)
            return true
        }
    }

    static shouldStoreLog(operation_status): boolean {
        return isErrorStatus(operation_status) && store.state.testMode
    }

    public static statusExceededTimeToLive(status: Status) {
        try {
            return elapsedTimeInSeconds(status.dtc) / 60 > TTL
        } catch (e) {
            debug(e)
            return true
        }
    }

    save() {
        store.commit("persistStatusMessage", this)
    }

    public delete() {
        store.commit("deleteStatusMessage", this)
    }

    public static purgeAllMessages() {
        if (size(store.state.statusMessages) > 0) {
            store.commit("resetStatusMessages")
        }
    }
}
