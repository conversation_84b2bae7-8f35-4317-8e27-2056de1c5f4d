import axios from 'axios';
import Bucket<PERSON>pi from '@/app-modules/mantle-apis/BucketApi';
import Match<PERSON><PERSON> from '@/app-modules/mantle-apis/MatchApi';
import { AppState } from '@/app-modules/app-state/AppState';
import { AutoMatcher } from '@/app-modules/post-items/services/AutoMatcher';
import { Batch } from '@/app-modules/batch/entities/Batch';
import { debug } from '@/app-modules/helpers/debug';
import { elapsedTimeInSeconds } from '@/app-modules/helpers/clock';
import { isEqual } from 'lodash';
import { Stats } from '@/app-modules/app-state/entities/Stats';
import { Status } from '@/app-modules/post-items/entities/Status';
import { store } from '@/store';
import {MailType} from "@/app-modules/batch/entities/MailType";

export const expiryDeadline = 24 // Number in hours in which the item will be lost on pub/sub
/*
* This is the data representation of a single file/row in the status table
*/
export class PostItem {
    id: any
    pdf_filename: any
    operator: any
    operation_status: any
    operation_info: any
    protectOperationStatus: any
    typeMail: any
    companyName: any
    companyId: any
    companyNumber: any
    serviceName: any
    confidence: any
    imagePath: any
    ack_id: any
    test: any
    protectAckId: boolean // this allows us to stop ack_id from being overwritten
    batchId: any
    dtc: any
    dtm: any
    autoMatched: any
    newProcess: any

    constructor(inWaitingQueue = false, protectOperationStatus = true) {
        this.protectAckId = !inWaitingQueue;
        this.protectOperationStatus = protectOperationStatus;
    }

    /*
    * We receive the data from pullWaiting and pullStatus api endpoints
    * we then use the build method to set this object with these values for us and persist this object to storage
    * we can later rebuild this object from storage, mutate it and save it again
    * once a mutation is triggered on vuex store, the object should also update on the page.
    */
    async build({
        pdf_filename = null,
        operation_status = null,
        operation_info = null,
        companyName = null,
        companyId = null,
        companyNumber = null,
        confidence = null,
        imagePath = null,
        batchId = null,
        ack_id = null,
        test = null
    }: any) {

        this.pdf_filename = (pdf_filename != null ? pdf_filename : null)

        if (this.pdf_filename) {
            this.operator = await this.getValue('operator', null, store.state.uid)
            this.companyId = await this.getValue('companyId', companyId)
            this.companyName = await this.getValue('companyName', companyName)
            this.companyNumber = await this.getValue('companyNumber', companyNumber)
            await this.setOperationData({
                operation_status: operation_status,
                operation_info: operation_info,
            })
            this.confidence = await this.getValue('confidence', confidence)
            this.imagePath = await this.getValue('imagePath', imagePath)
            this.batchId = await this.getValue('batchId', batchId)
            this.typeMail = await Batch.getValue(this.batchId, 'batchType')
            const batch = await Batch.getBatchById(this.batchId)
            this.test = await this.getValue('test', test, `${batch.isTest}`)
            await this.setAutoMatchedStatus()
            this.dtc = await this.getValue('dtc', null, new Date())
            await this.setAckId(ack_id)
        } else {
            throw new Error('Property pdf_filename must not be null.')
        }
    }

    async setOperationData({
        operation_status = null,
        operation_info = null
    }: any): Promise<void> {
        this.operation_status = await this.getStoredValue('operation_status')
        this.operation_info = await this.getStoredValue('operation_info')

        if (!this.protectOperationStatus || !this.operation_status) {
            this.operation_status = await this.getValue('operation_status', operation_status)
            this.operation_info = await this.getValue('operation_info', operation_info)
        }
    }

    async setAckId(ack_id: any): Promise<void> {
        const currAckId = await this.getStoredValue('ack_id')
        this.ack_id = currAckId
        if (!this.protectAckId || !currAckId) {
            this.ack_id = ack_id
        }
    }

    async getValue(key: string, input: any = null, defaultsTo: any = ``): Promise<any> {
        try {
            return (input != null ? input : await this.getStoredValue(key) || defaultsTo)
        } catch (e) {
            return defaultsTo
        }
    }

    private async getStoredValue(key: string): Promise<any> {
        try {
            return store.state.trackedFiles[this.pdf_filename][key]
        } catch (e) {
            return ''
        }
    }

    static getByFilename(pdf_filename: string): any {
        try {
            return store.state.trackedFiles[pdf_filename]
        } catch (e) {
            return ''
        }
    }

    async setAutoMatchedStatus() {
        try {
            const id = AutoMatcher.getFileId(this)
            this.autoMatched = AutoMatcher.getAutoMatcherHistory().includes(id)
        } catch (e) {
            debug(e)
            this.autoMatched = false
        }
    }

    isBeingTracked(): boolean {
        try {
            return !!PostItem.getByFilename(this.pdf_filename)
        } catch (e) {
            return false
        }
    }

    enableOperationDataProtection() {
        this.protectOperationStatus = true
    }

    disableOperationDataProtection() {
        this.protectOperationStatus = false
    }

    public static itemExpired(postItem: PostItem) {
        try {
            return elapsedTimeInSeconds(postItem.dtm) / 60 / 60 > expiryDeadline
        } catch (e) {
            debug(e)
            return true
        }
    }

    public async addItemToMrDatabase(formData: FormData): Promise<any> {
        try {
            const batchType = (await Batch.getBatchById(this.batchId)).batchType

            const data = {
                'type_mail': MailType.getMailTypeFromBatchTypeForDatabaseInsertion(batchType),
                'companyNumber': this.companyNumber,
                'companyName': this.companyName,
                'batchId': this.batchId,
                'pdf_filename': this.pdf_filename,
                'sender': MailType.getSenderFromBatchType(batchType),
                'operator': store.state.userEmail,
                'formData': formData.get('data'),
            }

            const payload = JSON.stringify(data)

            const response = await axios.post(
                `/api/add-post-item?scanned=true`,
                payload,
                {
                    withCredentials: true
                }
            )

            return response.data.post_item_id
        } catch (e) {
            debug(e)
        }
    }

    async match(): Promise<any> {
        try {
            new AppState({
                matchingFile: true,
                thread4: `Saving (${this.pdf_filename})`
            })

            this.disableOperationDataProtection()
            await this.setOperationData({
                operation_status: "COMPLETED_START"
            })
            await Stats.updatePostItemStatsInBatch(this)
            this.enableOperationDataProtection()

            this.test = `${(await Batch.getBatchById(this.batchId)).isTest}`
            this.newProcess = true

            this.save()

            const formData = new FormData()

            if (this.serviceName === 'VO' || String(this.companyId ?? '').startsWith('LP')) {
                this.newProcess = false
                this.save()
                formData.append('data', JSON.stringify(this))
                await new MatchApi().publish(formData)
            } else {
                formData.append('data', JSON.stringify(this))
                this.id = await this.addItemToMrDatabase(formData)
            }

            this.save()

            new AppState({
                matchingFile: false,
                thread4: ''
            })

            await Stats.updatePostItemStatsInBatch(this)
        } catch (e) {

            debug(e, 't')

            this.disableOperationDataProtection()
            await this.setOperationData({
                operation_status: "COMPLETED_ERROR",
                operation_info: {}
            })
            this.enableOperationDataProtection()
            this.save()

            new AppState({
                matchingFile: false,
                thread4: ''
            })
            return false
        }
    }

    async patch({
        id = null,
        companyName = null,
        companyNumber = null,
        serviceName = null,
    }): Promise<void> {
        this.operation_info.lastCompanyId = this.companyId
        this.operation_info.lastCompanyName = this.companyName
        this.operation_info.lastCompanyNumber = this.companyNumber
        this.operation_info.edited = 1
        this.companyId = id
        this.companyName = companyName
        this.companyNumber = companyNumber
        this.serviceName = serviceName
        this.test = `${store.state.testMode}`
        this.save()
    }

    static async dismissSavedFiles(): Promise<any> {
        try {
            new AppState({
                task: `Dismissing all saved files`,
                busy: true,
                ready: false,
                acceptUploads: false,
                allowBackgroundUpdate: false
            })

            const trackedFiles = await store.dispatch("getTrackedFiles")
            for (const file in trackedFiles) {
                if (Status.isCompleted(trackedFiles[file])) {
                    let item = new PostItem()
                    await item.build(trackedFiles[file])
                    item.save()
                    await item.deleteFromStorage()
                }
            }
            store.commit("setSelectedFile", {})

            new AppState({
                task: ``,
                busy: false,
                ready: true,
                acceptUploads: !await AppState.hasTrackedFiles(),
                allowBackgroundUpdate: true
            })
        } catch (e) {
            debug(e)
            new AppState({
                task: ``,
                busy: false,
                ready: true,
                acceptUploads: !await AppState.hasTrackedFiles(),
                allowBackgroundUpdate: true
            })
        }
    }

    static async purgeTrackedFiles(): Promise<any> {
        try {
            new AppState({
                task: `Purging all tracked files`,
                busy: true,
                ready: false,
                acceptUploads: false,
                allowBackgroundUpdate: false
            })

            const trackedFiles = await store.dispatch("getTrackedFiles")
            for (const file in trackedFiles) {
                if (trackedFiles[file]) {
                    let item = new PostItem()
                    item.build(trackedFiles[file]).then(() => {
                        item.delete().then(() => { })
                    })
                }
            }
            store.commit("setSelectedFile", {})

            new AppState({
                task: ``,
                busy: false,
                ready: true,
                acceptUploads: !await AppState.hasTrackedFiles(),
                allowBackgroundUpdate: true
            })
        } catch (e) {
            debug(e)
        }
    }

    async deleteFromStorage(): Promise<any> {
        await store.commit("deleteTrackedFile", this)
        await Stats.removePostItemStatsInBatch(this)
    }

    public async delete(): Promise<boolean> {
        try {
            new AppState({
                deletingFile: true,
                thread2: `Deleting file(s)`
            })

            return this.deleteFromStorage()
                .then(async () => {
                    const bucketApi = new BucketApi()
                    try {
                        const result = await bucketApi.deletePostItem(this.pdf_filename);
                        new AppState({
                            deletingFile: false,
                            thread2: ''
                        });
                        return result;
                    } catch (error) {
                        debug(error);
                        new AppState({
                            deletingFile: false,
                            thread2: ''
                        });
                        return false;
                    }
                }).catch((error) => {
                    debug(error)
                    new AppState({
                        deletingFile: false,
                        thread2: ''
                    })
                    return false
                })
        } catch (error) {
            debug(error)
            new AppState({
                deletingFile: false,
                thread2: ''
            })
            return false
        }
    }

    save() {
        if (!isEqual(this, PostItem.getByFilename(this.pdf_filename))) {
            this.dtm = new Date()
        }
        store.commit("updateTrackedFile", this)
    }
}
