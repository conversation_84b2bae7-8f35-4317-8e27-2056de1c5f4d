import axios from "axios";
import {debug} from "@/app-modules/helpers/debug";
import {AppState} from "@/app-modules/app-state/AppState";
import {PostItem} from "@/app-modules/post-items/entities/PostItem";

export async function pullWaiting() {
    try {
        new AppState({
            updatingWaiting: true,
            thread0: 'Verifying files waiting on queue'
        })
        const { data }  = await axios.get( '/api/pull-waiting/',{
            headers: {
                withCredentials: true
            }
        })

        if(typeof data === 'object') {
            for(let itemIndex in data) {
                if(data[itemIndex]) {
                    let item = new PostItem(true)
                    await item.build(data[itemIndex])
                    item.save()
                }
            }
        }

        new AppState({
            updatingWaiting: false,
            thread0: ''
        })
        return data
    } catch (e) {
        debug(e)
        new AppState({
            updatingWaiting: false,
            thread0: ''
        })
        return {}
    }
}