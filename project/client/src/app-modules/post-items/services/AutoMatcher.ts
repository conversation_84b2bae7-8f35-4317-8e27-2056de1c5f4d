import { AppState } from '@/app-modules/app-state/AppState';
import { debug } from '@/app-modules/helpers/debug';
import { isElement, isEqual } from 'lodash';
import { MIN_AUTO_MATCH_CONFIDENCE } from '@/config/config';
import { Status } from '@/app-modules/post-items/entities/Status';
import { store } from '@/store';

export class AutoMatcher {
    file: any
    fileId: any
    matchElement: any
    autoMatchedFiles: any

    constructor(file: any) {
        this.file = file
        this.fileId = AutoMatcher.getFileId(file)
        this.autoMatchedFiles = AutoMatcher.getAutoMatcherHistory()
    }

    getConfidence(): number {
        try {
            return this.file.confidence
        } catch (e) {
            debug(e)
            return 0
        }
    }

    static getFileId(file: any) {
        try {
            const regex = /[^a-zA-Z0-9]+/g
            if (typeof file === 'object') {
                return `pITf-${file.pdf_filename.replaceAll('.pdf', '').toLowerCase().replace(regex, "-")}`
            }
            return `pITf-${file.replaceAll('.pdf', '').toLowerCase().replace(regex, "-")}`
        } catch (e) {
            debug(e)
            return ``
        }
    }

    static getAutoMatcherHistory() {
        try {
            return store.state.autoMatcher ? store.state.autoMatcher : []
        } catch (e) {
            debug(e)
            return []
        }
    }

    canAutoMatch(): boolean {
        return false
        return store.state.appState.allowAutoMatch
            && !Status.isCompleted(this.file)
            && !Status.isSaving(this.file)
            && !this.autoMatchedFiles.includes(this.fileId)
    }

    hasHighConfidenceLevel(): boolean {
        try {
            return this.getConfidence() >= MIN_AUTO_MATCH_CONFIDENCE
        } catch (e) {
            debug(e)
            return false
        }
    }

    async autoMatch(): Promise<boolean> {
        try {
            const btn = this.matchElement;
            if (btn && isElement(btn)) {
                btn.click()
                this.autoMatchedFiles.push(`${this.fileId}`)
                this.save()
                return true
            }
            return false
        } catch (e) {
            debug(e)
            return false
        }

    }

    async tryAutoMatch() {
        try {
            if (this.file.autoMatched && !this.autoMatchedFiles.includes(this.fileId)) {
                this.autoMatchedFiles.push(`${this.fileId}`)
                this.save()
            } else if (this.canAutoMatch() && this.hasHighConfidenceLevel()) {
                new AppState({
                    autoMatchingFiles: true
                })
                this.matchElement = document.getElementById(`${this.fileId}-match`) as HTMLElement
                await this.autoMatch()
            }
        } catch (e) {
            debug(e)
        }
    }

    static async tryMatchAll(): Promise<void> {
        try {
            for (const file in store.state.trackedFiles) {
                const autoMatcher = new AutoMatcher(store.state.trackedFiles[file])
                autoMatcher.tryAutoMatch().then(() => { })
            }
        } catch (e) {
            debug(e)
        }
        new AppState({
            autoMatchingFiles: false
        })
    }

    save() {
        store.commit("updateAutoMatcher", { autoMatcherHistory: this.autoMatchedFiles })
    }

    static purge() {
        store.commit("resetAutoMatcher")
    }
}
