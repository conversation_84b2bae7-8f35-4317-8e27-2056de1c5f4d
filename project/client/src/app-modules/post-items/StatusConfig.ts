export const ackDeadline = 10 // ACK deadline for status in minutes
/*
* PRIORITY
*
* Priority is used to sort items in the PostItem table UI.
* A higher number means it will be placed above lower ones.
* Priority order:
* failed/matched 20-30
* in progress 10-20
* saving/saved 0-9
*
* WEIGHT
*
* Weight is used to determine which status should override or not the current status
* Weight value convention: Sequence step vertical (boundary) - sequence step horizontal (time) (zero filled to contain 4 digits)
*
*/
export const statuses = {
    // UI (new) -> Symfony Api File Upload (old)
    "WAITING_TO_UPLOAD":
    {
        priority: 10,
        description: "Waiting to upload",
        weight: 1000,
        isError: false
    },
    // Symfony Api File Upload (old) -> UI
    "PDF_UPLOADED":
    {
        priority: 11,
        description: "Processing...",
        weight: 1001,
        isError: false
    },
    "PDF_UPLOAD_FAILED":
    {
        priority: 23,
        description: "PDF failed to upload",
        weight: 1000,
        isError: true
    },
    // mr-convert-pdf-to-image
    "CONVERT_PDF_START":
    {
        priority: 12,
        description: "Converting to image",
        weight: 2000,
        isError: false
    },
    "CONVERT_PDF_SUCCESS":
    {
        priority: 13,
        description: "PDF converted to image",
        weight: 2001,
        isError: false
    },
    "CONVERT_PDF_ERROR":
    {
        priority: 24,
        description: "Failed to convert PDF to image",
        weight: 2002,
        isError: true
    },
    // mr-ocr-image
    "OCR_IMAGE_START":
    {
        priority: 14,
        description: "Extracting text...",
        weight: 3000,
        isError: false
    },
    "OCR_IMAGE_SUCCESS":
    {
        priority: 15,
        description: "Text extracted",
        weight: 3001,
        isError: false
    },
    "OCR_IMAGE_ERROR":
    {
        priority: 25,
        description: "Failed to extract text from image",
        weight: 3002,
        isError: true
    },
    // mr-predict-function
    "PREDICT_COMPANY_START":
    {
        priority: 17,
        description: "Guessing company name...",
        weight: 4000,
        isError: false
    },
    "PREDICT_COMPANY_SUCCESS":
    {
        priority: 18,
        description: "Company name predicted",
        weight: 4001,
        isError: false
    },
    "PREDICT_COMPANY_ERROR":
    {
        priority: 26,
        description: "Failed to predict company name",
        weight: 4002,
        isError: true
    },
    // mr-find-company
    "FIND_COMPANY_START":
    {
        priority: 16,
        description: "Matching company...",
        weight: 4003,
        isError: false
    },
    "FIND_COMPANY_SUCCESS":
    {
        priority: 30,
        description: "Company found",
        weight: 4004,
        isError: false
    },
    "FIND_COMPANY_ERROR":
    {
        priority: 27,
        description: "Failed to find company name",
        weight: 4005,
        isError: true
    },
    // mr-completed
    "COMPLETED_START":
    {
        priority: 1,
        description: "Saving to DB...",
        weight: 5000,
        isError: false
    },
    "COMPLETED_ERROR":
    {
        priority: 28,
        description: "Something went wrong. Contact the dev team.",
        weight: 5001,
        isError: true
    },
    "COMPLETED_SUCCESS":
    {
        priority: 0,
        description: "Saved",
        weight: 5002,
        isError: false
    },
} as any

export function isErrorStatus(messageString: any): boolean {
    let status = statuses[messageString]
    if (status && status.isError) {
        return true
    }
    return false
}