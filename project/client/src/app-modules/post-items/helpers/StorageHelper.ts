import { IMAGE_STORAGE_URL, PDF_STORAGE_URL } from '@/config/config';

export default class StorageHelper {
    private static imageStorageURL: string = IMAGE_STORAGE_URL
    private static pdfStorageURL: string = PDF_STORAGE_URL

    public static getImageUrl(imagePath: string): string {
        return `${StorageHelper.imageStorageURL}${imagePath}`
    }

    public static getPdfUrl(pdfFilename: string): string {
        return `${StorageHelper.pdfStorageURL}${pdfFilename}`
    }
}
