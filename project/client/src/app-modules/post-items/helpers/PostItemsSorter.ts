import {Status} from "@/app-modules/post-items/entities/Status";
import {debug} from "@/app-modules/helpers/debug";

export class PostItemsSorter {

    // Determine if a has priority over b based on priority and date modified
    static aHasPriority(a: any,b: any) {
        const aTime = new Date(a.dtm).getTime()
        const bTime = new Date(b.dtm).getTime()
        const aIsOlder = aTime < bTime
        const aPriority = Status.getPriority(a.operation_status)
        const bPriority = Status.getPriority(b.operation_status)
        let aHasPriority =  aPriority > bPriority
        if(aPriority === bPriority) {
            aHasPriority = aIsOlder
        }
        return aHasPriority
    }

    // sort items based on priority as ASC for priority score and dtm
    static sortByPriorityASC(trackedFiles: any) {
        if(trackedFiles) {
            try {
                return Object.values(trackedFiles).sort(function(a: any, b: any){
                    if(PostItemsSorter.aHasPriority(a,b)) {
                        return -1
                    } else {
                        return 1
                    }
                })
            } catch (e) {
                debug(e)
                return Object.values(trackedFiles)
            }

        }
    }

    // sort items based on priority as DESC for priority score and dtm
    static sortByPriorityDESC(trackedFiles: any) {
        if(trackedFiles) {
            try {
                return Object.values(trackedFiles).sort(function(a: any, b: any){
                    if(PostItemsSorter.aHasPriority(a,b)) {
                        return 1
                    } else {
                        return -1
                    }
                })
            } catch (e) {
                debug(e)
                return Object.values(trackedFiles)
            }
        }
    }
}