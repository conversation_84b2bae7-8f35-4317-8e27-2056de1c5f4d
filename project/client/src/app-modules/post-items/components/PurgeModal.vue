<template>
  <div id="purgeFilesConfirmationModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div v-if="hasTrackedFiles" class="modal-content">
        <div class="modal-header">
          <h5 class="text-center">
            Are you certain you want to delete <b>ALL</b> tracked files?
            <b> This action cannot be undone. </b>
          </h5>
        </div>
        <div class="modal-footer">
          <span v-if="render"> </span>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal" @click="purge()">
            <font-awesome-icon icon="fa-solid fa-trash fa-fw " />
            Delete
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <font-awesome-icon icon="fa-solid fa-xmark" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {AppState} from "@/app-modules/app-state/AppState";
import {store} from "@/store";
import { PostItem } from "../entities/PostItem";

export default defineComponent({
  name: "PurgeModal",
  components: {
  },
  data() {
    return {
      render: false,
      ready: false,
      busy: false,
      hasTrackedFiles: false
    }
  },
  methods: {
    async purge() {
      await PostItem.purgeTrackedFiles()
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated, async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
    })
  }
})
</script>