<template>
  <div id="deleteFileConfirmationModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div v-if="hasTrackedFiles" class="modal-content">
        <div class="modal-header">
          <h5 class="text-center">
            Do you really want to delete
            <a :href="getFileStorageUrl()"
               target="_blank" >
              {{ file.pdf_filename }}
            </a> ? <b> This action cannot be undone. </b>
          </h5>
        </div>
        <div class="modal-body">
          <div class="mx-2" v-html="getFilePreview()"></div>
        </div>
        <div class="modal-footer">
          <span v-if="render"> </span>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal" @click="destroy()">
            <font-awesome-icon icon="fa-solid fa-trash fa-fw " />
            Delete
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <font-awesome-icon icon="fa-solid fa-xmark" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {store} from "@/store";
import {AppState} from "@/app-modules/app-state/AppState";
import StorageHelper from '../helpers/StorageHelper';

export default defineComponent({
  name: "DeleteModal",
  data() {
    return {
      render: false,
      ready: false,
      busy: false,
      hasTrackedFiles: false,
    }
  },
  methods: {
    getFileStorageUrl() {
      return StorageHelper.getPdfUrl(this.file.pdf_filename)
    },
    getFilePreview() {
      let html = `<object  class="PDF-delete-preview" data="${this.getFileStorageUrl()}" type="application/pdf">`
      html += `<div>Unable to display pdf preview, click on the pdf filename above to open it in another page.</div>`
      html += `</object>`
      return html
    },
    async destroy() {
      let item = new PostItem()
      await item.build(this.file)
      item.save()
      await item.delete()
      store.commit("setSelectedFile", {})
    }
  },
  computed: {
    file() {
        return store.state.selectedFile
    },
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated,  async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
    })
  }
})
</script>
