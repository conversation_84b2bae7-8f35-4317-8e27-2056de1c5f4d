<template>
  <div id="operationStatusModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="text-center">
            Operation Status Details
          </h5>
        </div>
        <div class="modal-body">
          <div class="input-group mb-3">
            <span class="input-group-text text-muted" id="basic-addon">
            <font-awesome-icon icon="fa-solid fa-clock" />
              &nbsp; Last Updated at &nbsp;
              <span class="text-muted"
                    data-bs-toggle="tooltip" data-bs-placement="top"  :title="`Last update time (UTC)`">
                <small>
                  <small v-if="getTimeModified(item)">
                    {{ getTimeModified(item) }}
                  </small>
                </small>
              </span>
            </span>
            <div class="form-control" v-html="item.dtm"></div>
          </div>
          <div>
            <h4>Operation Info</h4>
            <pre style="white-space: pre-wrap;" v-html="JSON.stringify(item.operation_info, null, '\t')"></pre>
          </div>
          <hr >
        </div>
        <div class="modal-footer">
          <span v-if="render"> </span>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <font-awesome-icon icon="fas fa-times" />
            Dismiss
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {statuses} from "@/app-modules/post-items/StatusConfig";
import {store} from "@/store";
import {debug} from "@/app-modules/helpers/debug";
import {AppState} from "@/app-modules/app-state/AppState";

export default defineComponent({
  name: "OperationStatusModal",
  components: {
  },
  data() {
    return {
      render: false,
      ready: false,
      busy: false,
      hasTrackedFiles: false,
      operationStatuses: statuses,
      expandedProperty: '',
      item: {
        dtm: ``,
        pdf_filename: ``,
        operation_status: '',
        operation_info: '',
        typeMail: '',
        textPayload: "",
        companyName: '',
        companyId: '',
        companyNumber: '',
        confidence: 0,
        imagePath: "",
        ack_id: '',
        test: `${store.state.testMode}`
      }
    }
  },
  methods: {
    expandProperty(obj: any): void {
      this.expandedProperty = obj
      this.$forceUpdate()
    },
    getOperationWeight(item: any) {
      try {
        return item.weight
      } catch(e) {
        debug(e)
      }
    },
    getOperationDescription(item: any) {
      try {
        return item.description
      } catch(e) {
        debug(e)
      }
    },
    getTimeCreated(file: any) {
      const dtc = new Date(file.dtc)
      const dtcHours = dtc.getUTCHours()
      const dtcMinutes = dtc.getUTCMinutes()

      if((!isNaN(dtcHours)) && (!isNaN(dtcMinutes))) {
        return `${dtcHours}:${String(dtcMinutes).padStart(2,"0")}`
      }
      return ``
    },
    getTimeModified(file: any) {
      const dtm = new Date(file.dtm)
      const dtmHours = dtm.getUTCHours()
      const dtmMinutes = dtm.getUTCMinutes()

      if((!isNaN(dtmHours)) && (!isNaN(dtmMinutes))) {
        return `${dtmHours}:${String(dtmMinutes).padStart(2,"0")}`
      }
      return this.getTimeCreated(file)
    },
    async save() {
      let item = new PostItem(true)
      await item.build({
        pdf_filename: this.item.pdf_filename,
        operation_status: this.item.operation_status,
        operation_info: this.item.operation_info,
        typeMail: this.item.typeMail,
        textPayload: this.item.textPayload,
        companyName: this.item.companyName,
        companyId: this.item.companyId,
        companyNumber: this.item.companyNumber,
        confidence: this.item.confidence,
        imagePath: this.item.imagePath,
        ack_id: this.item.ack_id,
        test: this.item.test,
      })
      item.save()
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated, async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
      this.item = store.state.selectedFile
    })
  },
})
</script>
