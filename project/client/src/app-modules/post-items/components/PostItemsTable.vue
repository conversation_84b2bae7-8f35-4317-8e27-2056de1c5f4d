<template>
  <div>
    <table id="statusTable" class="table table-responsive table-sm table-bordered table-hover table-striped align-middle">
          <thead>
            <tr>
              <th v-if="batchId === '%'">
                BATCH ID
              </th>
              <th class="confidence">
                CONFIDENCE LEVEL
              </th>
              <th>
                PREDICTED COMPANY NAME
              </th>
              <th>
                SCANNED PDF
              </th>
              <th>
                CMS COMPANY NAME
              </th>
              <th @click="changeSortingOrder()">
                STATUS
                <span v-if="sortingOrder === 'ASC'">
                  <font-awesome-icon icon="fa-solid fa-sort-up" />
                </span>
                <span v-else>
                  <font-awesome-icon icon="fa-solid fa-sort-down" />
                </span>
              </th>
              <th>
                ACTIONS
              </th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(file, index) in trackedFiles">
              <tr :class="getColorFeedbackStatusClass(file)">
                <td v-if="batchId === '%'">
                  {{ file.batchId }}
                </td>
                <td :id="`${getFileId(file)}-confidence`" class="confidence">
                  <!-- CONFIDENCE LEVEL-->
                  <div v-if="isProcessing(file)">
                    <span class="text-muted">
                      <span role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                        <span class="sr-only">Loading...</span>
                      </span>
                    </span>
                  </div>
                  <div v-else-if="hasError(file)">
                    <span class="text-danger rounded p-1">
                      <font-awesome-icon icon="fa-solid fa-triangle-exclamation" />
                    </span>
                  </div>
                  <div v-else>
                    <span class="border border-success p-1 text-success rounded" v-if="file.autoMatched">
                      <small>
                        <small>
                           AUTO
                        </small>
                      </small>
                    </span>

                    <span v-if="getConfidenceLevelValue(file.confidence)" class="text-white rounded p-1" :class="getConfidenceLevelSelector(file.confidence)">
                      {{ getConfidenceLevelValue(file.confidence) }}
                    </span>
                    <span v-else class="text-white rounded p-1 bg-secondary">
                      N/A
                    </span>
                  </div>
                </td>
                <td :id="`${getFileId(file)}-image`" class="fileImage">
                  <!-- PREDICTED COMPANY NAME-->
                  <img
                      v-if="file.imagePath"
                      class="d-block m-auto mw-100"
                      :src="StorageHelper.getImageUrl(file.imagePath)"
                      :alt="file.companyName"
                  />
                </td>
                <td :id="`${getFileId(file)}-name`" class="filename">
                  <!-- SCANNED PDF -->
                  <a :href="StorageHelper.getPdfUrl(file.pdf_filename)"
                     target="_blank"
                     v-if="canOpenAsLink(file)">
                    {{ file.pdf_filename }}
                  </a>
                  <span v-else>
                    {{ file.pdf_filename }}
                  </span>
                </td>
                <td :id="`${getFileId(file)}-info`" class="companyInfo">
                  <!-- CMS COMPANY NAME -->
                  <span v-show="file.companyId">
                    ({{ file.companyId }})
                  </span>
                  <span v-show="file.companyName">
                    {{ file.companyName }}
                  </span>
                </td>
                <td :id="`${getFileId(file)}-status`" class="status">
                  <!-- STATUS -->
                  <div id="status-info" data-bs-toggle="modal" data-bs-target="#operationStatusModal"
                      @click="showOperationStatusInfo(file)"
                  >
                    {{ getOperationStatus(file) }}
                  </div>
                  <span v-if="render" class="bg-transparent rounded-pill badge"> &nbsp; </span>
                </td>
                <td :id="`${getFileId(file)}-actions`" class="actions">
                  <!-- ACTION -->
                  <div class="btn-group-vertical" v-if="!isCompleted(file)">
                    <!-- MATCH -->
                    <button class="w-100 text-left text-start px-2 py-1 btn btn-lg border-0 rounded-0"
                            :id="`${getFileId(file)}-match`"
                            :class="`btn-outline-${isMatchingAllowed(file) ? `success` : `secondary` }`"
                            @click="match(file, $event)" :disabled="!isMatchingAllowed(file)">
                      <span>
                        <font-awesome-icon icon="fa-solid fa-check-circle fa-fw" />
                      </span>
                      <span class="px-2"> Match </span>
                    </button>
                    <!-- PATCH -->
                    <button class="w-100 text-left text-start px-2 py-1 btn border-0 rounded-0 border-top btn-sm"
                            :id="`${getFileId(file)}-patch`"
                            :class="`btn-outline-${isPatchingAllowed(file) ? `primary` : `secondary` }`"
                            data-bs-toggle="modal" data-bs-target="#companySearchModal"
                            @click="patch(file, $event)" :disabled="!isPatchingAllowed(file)">
                      <small>
                        <font-awesome-icon icon="fa-solid fa-pencil fa-fw" />
                      </small>
                      <small class="px-2"> Edit </small>
                    </button>
                    <!-- DELETE -->
                    <button class="w-100 text-left text-start px-2 py-1 btn border-0 border-top rounded-0 btn-sm"
                            :class="`btn-outline-${isDeletingAllowed(file) ? `danger` : `secondary` }`"
                            :id="`${getFileId(file)}-delete`"
                            data-bs-toggle="modal" data-bs-target="#deleteFileConfirmationModal"
                            @click="destroy(file, $event)" :disabled="!isDeletingAllowed(file)">
                      <small>
                        <font-awesome-icon icon="fa-solid fa-trash fa-fw " />
                      </small>
                      <small class="px-2"> Delete </small>
                    </button>
                  </div>
                  <div class="btn-group-vertical" v-else>
                    <!-- DISMISS -->
                    <button class="w-100 text-left text-start px-2 py-1 btn btn-outline-primary border-0 rounded-0 btn-sm"
                            :id="`${getFileId(file)}-dismiss`"
                            v-show="isDismissingAllowed(file)" @click="dismiss(file, $event)" :disabled="!isDismissingAllowed(file)">
                      <span>
                        <font-awesome-icon icon="fa-solid fa-times fa-fw" />
                      </span>
                      <span class="px-2"> Dismiss </span>
                    </button>
                  </div>
                  <div class="btn-group-vertical" v-if="debugMode">
                    <!-- DEBUG -->
                    <button class="w-100 text-left text-start px-2 py-1 btn  border-0 rounded-0 btn-sm"
                            :class="`btn-outline-${debugMode ? `danger` : `secondary` }`"
                            :id="`${getFileId(file)}-debug`"
                            data-bs-toggle="modal" data-bs-target="#debugItemModal"
                            @click="debugFile(file, $event)" :disabled="!debugMode">
                      <small>
                        <font-awesome-icon icon="fa-solid fa-bug fa-fw " />
                      </small>
                      <small class="px-2"> Debug </small>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
    <div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {computed, onMounted, reactive, ref, watch} from "vue"
import {statuses} from "@/app-modules/post-items/StatusConfig";
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {CompanySearch} from "@/app-modules/cms/services/CompanySearch";
import { Status } from "../entities/Status";
import {debug} from "@/app-modules/helpers/debug";
import {PostItemsSorter} from "@/app-modules/post-items/helpers/PostItemsSorter";
import {AutoMatcher} from "@/app-modules/post-items/services/AutoMatcher";
import {AppState} from "@/app-modules/app-state/AppState";
import { useRoute } from "vue-router";
import {Batch} from "@/app-modules/batch/entities/Batch";
import {useStore} from "vuex";
import StorageHelper from "@/app-modules/post-items/helpers/StorageHelper";

// define constants, variables and props
const route = useRoute()
const store = useStore()
const props = withDefaults(
    defineProps<{
      batchId: string
    }>(),
    {
      batchId: ''
    },
);

let debugMode = computed(() => store.state.appState.debug),
    sortingOrder = 'ASC',
    trackedFiles = reactive({}) as any,
    render = ref(true)

// check if there is a debug mode flag
try {
  const debugMode = (route.query.debug === 'on') || (route.query.debug == 'true')
  new AppState({
    debug: debugMode
  })
} catch (e) {
  console.log(e)
}

// declare methods
function setTrackedFiles(): any {
  render.value = false
  const files = Batch.getTrackedFilesInBatchByBatchId(props.batchId)
  if(sortingOrder == 'ASC') {
    Object.assign(trackedFiles, PostItemsSorter.sortByPriorityASC(files))
  } else {
    Object.assign(trackedFiles, PostItemsSorter.sortByPriorityDESC(files))
  }
  render.value = true
}

function changeSortingOrder() {
  if(sortingOrder == 'ASC') {
    sortingOrder = 'DESC'
  } else {
    sortingOrder = 'ASC'
  }
  setTrackedFiles()
}

function getFileId(file: any) {
  try {
    return AutoMatcher.getFileId(file)
  } catch (e) {
    debug(e)
    return ``
  }
}

function canOpenAsLink(file: any) {
  return Status.getWeight(file.operation_status) > 1000
}

function isProcessing(file: any) {
  try {
    return !file.confidence && !Status.hasError(file) && !Status.isLocked(file)
  } catch (e) {
    debug(e)
    return true
  }
}

function hasError(file: any) {
  try {
    return Status.hasError(file)
  } catch (e) {
    debug(e)
    return false
  }
}

function showOperationStatusInfo(file: any) {
  try {
    store.commit("setSelectedFile", file)
  } catch (e) {
    debug(e)
  }
}

function getOperationStatus(file: any): string {
  try {
    return statuses[file.operation_status].description
  } catch (e) {
    return file.operation_status
  }
}

function getConfidenceLevelValue(confidenceLevel: string) {
  let confidence = Math.floor(parseFloat(confidenceLevel)) || ''
  if(confidence) {
    return `${confidence}%`
  }
}

function getConfidenceLevelSelector(confidenceLevel: string){
  let confidence = parseFloat(confidenceLevel)
  return (confidence > 90 ? 'bg-success' : confidence > 80 ? 'bg-warning' : 'bg-danger')
}

function isCompleted(file: any) {
  return Status.isCompleted(file)
}

function getColorFeedbackStatusClass(file: any): any {
  //
  if(Status.isSaving(file)) {
    return `table-warning`
  }
  //
  if(Status.hasError(file)) {
    return `table-danger`
  }
  //
  if(Status.isCompleted(file)) {
    return `table-success`
  }
}

function isMatchingAllowed(file: any) {
  return !Status.isLocked(file) && !Status.hasError(file) && Status.getWeight(file.operation_status) >= 4004
}

async function match(file: any, event: any) {
  event.target.blur()
  let item = new PostItem()
  await item.build(file)
  await item.match()
}

function isPatchingAllowed(file: any) {
  return !Status.isLocked(file) && Status.getWeight(file.operation_status) > 1000
}

async function patch(file: any, event: any) {
  event.target.blur()
  // CompanySearch.purge()
  // Auto search
  let search = new CompanySearch({
    companyName: file.companyName,
    parameter: `Company name`
  })
  // Pass values for the searchModal fields
  store.commit('updateSearchParameters', {
    companyName: file.companyName,
    officerName: ``,
    lpNumber: ``
  })
  // Pass selected file
  store.commit("setSelectedFile", file)
  // run auto search
  if(store.state.appState.allowAutoSearch) {
    await search.searchCompanyByQuery()
  }
}

function isDeletingAllowed(file: any) {
  return !Status.isLocked(file) && Status.getWeight(file.operation_status) > 1000
}

function isDismissingAllowed(file: any): boolean {
  return Status.isCompleted(file) && Status.isLocked(file)
}

async function dismiss(file: any, event: any) {
  event.target.blur()
  let item = new PostItem()
  await item.build(file)
  item.save()
  await item.deleteFromStorage()
}

function destroy(file: any, event: any) {
  event.target.blur()
  store.commit("setSelectedFile", file)
}

function debugFile(file: any, event: any) {
  try {
    event.target.blur()
    store.commit("setSelectedFile", file)
  } catch (e) {
    console.error(e)
  }
}

// call methods on component mount
onMounted(() => {
  setTrackedFiles()
})

watch(() => store.state.mutated, function() {
  // run soft update
  setTrackedFiles()
},{
  immediate: true
});

watch(() => store.state.trackedFiles, function() {
  // run hard reset update, some files might've been added or deleted
  trackedFiles = reactive({})
  setTrackedFiles()
  debug(`TrackedFiles changed, updating.`,'l')
},{
  immediate: true,
  deep: true
});
</script>
