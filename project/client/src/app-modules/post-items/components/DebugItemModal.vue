<template>
  <div id="debugItemModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div v-if="hasTrackedFiles" class="modal-content">
        <div class="modal-header">
          <h5 class="text-center">
            Debugging item
          </h5>
          <ul class="nav nav-tabs">
            <li class="nav-item" @click="showData()">
              <div :class="`nav-link ${(displayData ? `active` : ``)}`">DATA</div>
            </li>
            <li class="nav-item" @click="showLogs()">
              <div :class="`nav-link ${(!displayData && !displayMessages ? `active` : ``)}`">LOGS</div>
            </li>
            <li class="nav-item" @click="showMessages()">
              <div :class="`nav-link ${(displayMessages ? `active` : ``)}`">MESSAGES</div>
            </li>
          </ul>
        </div>
        <div class="modal-body">
          <div v-if="displayData" class="debug-data">
            <div class="alert alert-warning bg-transparent text-warning">
              <font-awesome-icon icon="fa-solid fa-triangle-exclamation" />
              Please be aware that any changes made here are merely visual and affect only this interface. This does not make any changes to the files.
            </div>
            <div>
              <pre>
                <code v-html="expandedProperty"></code>
              </pre>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon">
                dtm &nbsp;
                <span class="text-muted"
                      data-bs-toggle="tooltip" data-bs-placement="top"  :title="`Last update time (UTC)`">
                  <small>
                    <small v-if="getTimeModified(item)">
                      <font-awesome-icon icon="fa-solid fa-clock" />
                      {{ getTimeModified(item) }}
                    </small>
                  </small>
                </span>
              </span>
              <input type="text" class="form-control" v-model="item.dtm" placeholder="dtm" aria-label="dtm" aria-describedby="basic-addon" disabled>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon-1">
                batchId
              </span>
              <input type="text" class="form-control" v-model="item.batchId" placeholder="pdf_filename" aria-label="pdf_filename" aria-describedby="basic-addon-1">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon0">
                pdf_filename
              </span>
              <input type="text" class="form-control" v-model="item.pdf_filename" placeholder="pdf_filename" aria-label="pdf_filename" aria-describedby="basic-addon0">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon1">
                operation_status
              </span>
              <select class="form-select" aria-label="Operation Status" v-model="item.operation_status">
                <option v-if="item.operation_status" selected :value="item.operation_status">
                  {{ getOperationWeight(operationStatuses[item.operation_status]) }}:
                  {{ getOperationDescription(operationStatuses[item.operation_status]) }}
                </option>
                <option v-for="(operationStatus, index) in operationStatuses" :value="index">
                  {{ getOperationWeight(operationStatus) }}:
                  {{ getOperationDescription(operationStatus) }}
                </option>
              </select>
              <input disabled type="text" class="form-control" v-model="item.operation_status"
                     placeholder="operation_status" aria-label="operation_status" aria-describedby="basic-addon1">
            </div>
            <div class="input-group mb-3">
              <label data-bs-toggle="tooltip" data-bs-placement="top"  :title="`Click me to expand!`"
                     class="input-group-text text-muted" @click="expandProperty(item.operation_info)">
                operation_info
              </label>
              <textarea type="text" class="form-control" v-html="JSON.stringify(item.operation_info)"></textarea>
            </div>
            <div class="col">
              <fieldset class="form-group mt-3 font-weight-bold text-center">
                <legend class="bv-no-focus-ring col-form-label pt-0 text-muted">
                  <font-awesome-icon icon="fa-solid fa-envelopes-bulk" />
                  <span>
                 Mail type:
                </span>
                </legend>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" v-model="item.typeMail" name="type-mail-item" id="inlineRadio1" value="HMRC">
                  <label class="form-check-label" for="inlineRadio1">HMRC</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" v-model="item.typeMail" name="type-mail-item" id="inlineRadio2" value="COMPANIES_HOUSE">
                  <label class="form-check-label" for="inlineRadio2">Company House</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" v-model="item.typeMail" name="type-mail-item" id="inlineRadio3" value="OTHER">
                  <label class="form-check-label" for="inlineRadio3">Other</label>
                </div>
              </fieldset>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon4">
                companyName
              </span>
              <input type="text" class="form-control" v-model="item.companyName" placeholder="companyName" aria-label="companyName" aria-describedby="basic-addon4">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon5">
                companyId
              </span>
              <input type="text" class="form-control" v-model="item.companyId" placeholder="companyId" aria-label="companyId" aria-describedby="basic-addon5">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon6">
                companyNumber
              </span>
              <input type="text" class="form-control" v-model="item.companyNumber" placeholder="companyNumber" aria-label="companyNumber" aria-describedby="basic-addon6">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon7">
                confidence
              </span>
              <input type="text" class="form-control" v-model="item.confidence" placeholder="confidence" aria-label="confidence" aria-describedby="basic-addon7">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon8">
                imagePath
              </span>
              <input type="text" class="form-control" v-model="item.imagePath" placeholder="imagePath" aria-label="imagePath" aria-describedby="basic-addon8">
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addonAutoMatched">
                autoMatched
              </span>
              <select class="form-select" aria-label="Auto Matched" v-model="item.autoMatched">
                <option v-if="item.autoMatched" selected :value="item.autoMatched">
                  {{ item.autoMatched }}
                </option>
                <option :value="true">
                  true
                </option>
                <option :value="false">
                  false
                </option>
              </select>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon10">
                ack_id
              </span>
              <input type="text" class="form-control" v-model="item.ack_id" placeholder="ack_id" aria-label="ack_id" aria-describedby="basic-addon10">
            </div>
            <div class="input-group mb-3">
              <label data-bs-toggle="tooltip" data-bs-placement="top"  :title="`Click me to expand!`"
                     class="input-group-text text-muted" @click="expandProperty(logs)">
                Logs
              </label>
              <textarea type="text" class="form-control" v-html="JSON.stringify(logs)"></textarea>
            </div>
          </div>
          <div v-else-if="!displayData && !displayMessages" class="debug-logs">
            <table class="table table-bordered table-striped table-responsive">
              <thead>
              <tr>
                <th scope="col">Message</th>
                <th scope="col">OperationInfo</th>
                <th scope="col">Timestamp</th>
                <th scope="col">Elapsed since last entry</th>
                <th scope="col">Total time</th>
              </tr>
              </thead>
              <tbody v-if="logs.operationData">
              <tr v-for="(log,index) in logs.operationData" :key="index">
                <th scope="row">{{log.message}}</th>
                <td>
                  <div v-if="log.operation_info">
                    <div v-if="log.operation_status">
                      {{log.operation_status}}
                    </div>
                    <div v-if="log.operation_info.retry">
                      Attempt: {{log.operation_info.retry}}
                    </div>
                    <div v-if="log.operation_info['start_time']">
                      Started at: {{getTimestampFromUnixTime(log.operation_info["start_time"])}}
                    </div>
                    <div v-if="log.operation_info['end_time']">
                      Finished at: {{getTimestampFromUnixTime(log.operation_info["end_time"])}}
                    </div>
                  </div>
                </td>
                <td>{{getTimestamp(new Date(log.dtc))}}</td>
                <td>{{log.elapsedSinceLastEntry}}</td>
                <td>{{log.totalTimeElapsed}}</td>
              </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="debug-messages">
            <div v-for="message in messages">
              <div class="card mb-1">
                <div class="card-body">
                  <div v-html="JSON.stringify(message)"></div>
                </div>
              </div>
            </div>
        </div>
      </div>
        <div class="modal-footer">
          <span v-if="render"> </span>
          <button type="button" class="btn btn-primary" data-bs-dismiss="modal" @click="save()">
            <font-awesome-icon icon="fa-solid fa-floppy-disk" />
            Save
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <font-awesome-icon icon="fa-solid fa-xmark" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {statuses} from "@/app-modules/post-items/StatusConfig";
import {store} from "@/store";
import {debug} from "@/app-modules/helpers/debug";
import {PostItemLogEntry} from "@/app-modules/app-state/entities/PostItemLogEntry";
import {getTimestamp} from "@/app-modules/helpers/clock";
import {AppState} from "@/app-modules/app-state/AppState";
import { Stats } from "@/app-modules/app-state/entities/Stats";

export default defineComponent({
  name: "DebugItemModal",
  components: {
  },
  data() {
    return {
      render: false,
      ready: false,
      busy: false,
      hasTrackedFiles: false,
      operationStatuses: statuses,
      expandedProperty: '',
      displayData: false,
      displayMessages: false,
      messages: {},
      item: {
        dtm: ``,
        pdf_filename: ``,
        batchId: ``,
        operation_status: '',
        operation_info: '',
        typeMail: '',
        textPayload: "",
        companyName: '',
        companyId: '',
        companyNumber: '',
        confidence: 0,
        imagePath: "",
        autoMatched: false,
        ack_id: '',
        test: `${store.state.testMode}`,
      },
      logs: []
    }
  },
  methods: {
    showData() {
      this.displayData = true
      this.displayMessages = false
    },
    showLogs() {
      this.displayData = false
      this.displayMessages = false
    },
    showMessages() {
      this.displayData = false
      this.displayMessages = true
    },
    getTimestamp,
    getTimestampFromUnixTime(date: any) {
      return getTimestamp(new Date(date * 1000))
    },
    expandProperty(obj: any): void {
      this.expandedProperty = obj
      this.$forceUpdate()
    },
    getOperationWeight(item: any) {
      try {
        return item.weight
      } catch(e) {
        debug(e)
      }
    },
    getOperationDescription(item: any) {
      try {
        return item.description
      } catch(e) {
        debug(e)
      }
    },
    getTimeCreated(file: any) {
      const dtc = new Date(file.dtc)
      const dtcHours = dtc.getUTCHours()
      const dtcMinutes = dtc.getUTCMinutes()

      if((!isNaN(dtcHours)) && (!isNaN(dtcMinutes))) {
        return `${dtcHours}:${String(dtcMinutes).padStart(2,"0")}`
      }
      return ``
    },
    getTimeModified(file: any) {
      const dtm = new Date(file.dtm)
      const dtmHours = dtm.getUTCHours()
      const dtmMinutes = dtm.getUTCMinutes()

      if((!isNaN(dtmHours)) && (!isNaN(dtmMinutes))) {
        return `${dtmHours}:${String(dtmMinutes).padStart(2,"0")}`
      }
      return this.getTimeCreated(file)
    },
    async save() {
      let item = new PostItem(true)
      await item.build({
        pdf_filename: this.item.pdf_filename,
        operation_status: this.item.operation_status,
        operation_info: this.item.operation_info,
        typeMail: this.item.typeMail,
        textPayload: this.item.textPayload,
        companyName: this.item.companyName,
        companyId: this.item.companyId,
        companyNumber: this.item.companyNumber,
        confidence: this.item.confidence,
        imagePath: this.item.imagePath,
        autoMatched: this.item.autoMatched,
        ack_id: this.item.ack_id,
        test: this.item.test
      })
      item.save()
      await Stats.updatePostItemStatsInBatch(item)
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated,  async mutated => {
      try {
        this.ready = store.state.appState.ready
        this.busy = store.state.appState.busy
        this.hasTrackedFiles = await AppState.hasTrackedFiles()
        this.$forceUpdate()
        this.item = store.state.selectedFile
        this.logs = PostItemLogEntry.getLogs(store.state.selectedFile["pdf_filename"])
        this.messages = store.state.statusMessages[store.state.selectedFile["pdf_filename"]]
      } catch (e) {
        debug(e)
      }
    })
  },
})
</script>
