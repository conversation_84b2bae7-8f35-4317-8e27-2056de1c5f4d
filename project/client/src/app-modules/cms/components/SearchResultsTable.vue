<template>
    <div class="card p-3 my-1">
      <div class="card-header">
        <div class="card-title">
          Results found for {{ searchQueryParameter }}
        </div>
      </div>
      <div class="card-body p-0">
        <div v-show="performingSearch" class="text-center alert alert-secondary bg-transparent p-2">
          <span class="text-muted">
            <font-awesome-icon icon="fa-solid fa-triangle-exclamation" />
            <span role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
              <span class="sr-only">Loading...</span>
            </span>
          </span>
          {{ thread3 }}
        </div>
        <table v-show="!performingSearch" id="searchResultsTable" class="table table-sm table-bordered table-hover table-striped align-middle">
          <thead>
            <tr>
              <th>
                Number
              </th>
              <th>
                Name
              </th>
              <th>
                Services
              </th>
              <th>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!hasSearchResults()">
              <td colspan="5">
                <h5 class="text-center"> No results. </h5>
              </td>
            </tr>
            <tr v-else-if="failedToFetch">
              <td colspan="5">
                <h5 class="text-center text-danger"> Failed to retrieve or display search results. </h5>
              </td>
            </tr>
            <tr v-else v-for="(result, index) in searchResultsData.value">
              <td>
                <!--Number-->
                {{ result.companyNumber }}
                <div v-if="hasDirectors(result)">
                  <div v-for="(director, index) in getDirectorList(result)">
                    <span class="text-muted">
                      <font-awesome-icon icon="fa-solid fa-user" />
                    </span>
                    {{ director.name }}
                  </div>
                </div>
              </td>
              <td>
                <!--Name-->
                {{ result.companyName }}
              </td>
              <td>
                <!--Services-->
                <div class="text-capitalize">
                  <b>{{ result.serviceName ?? '' }}</b>
                </div>
                <div class="text-capitalize">
                  <span :class="getServiceStatus(result) ? 'text-success' : 'text-danger'">
                    {{ getServiceStatus(result) ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </td>
              <td class="actions">
                <!-- Actions -->
                <!-- MATCH -->
                <button id="match-button" class="w-100 text-left text-start px-2 py-1 btn btn-outline-success border-0 rounded-0"
                  @click="match(result)" :disabled="!isMatchingAllowed()" data-bs-dismiss="modal">
                    <span>
                      <font-awesome-icon icon="fa-solid fa-check-circle fa-fw" />
                    </span>
                  <span class="px-2"> Match </span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <span v-show="render"> </span>
    </div>
</template>
<script lang="ts" setup>
import {reactive, ref, watch} from "vue";
import {first, isNull, now, size} from "lodash";
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {Status} from "@/app-modules/post-items/entities/Status";
import {debug} from "@/app-modules/helpers/debug";
import {useStore} from "vuex";

const store = useStore()

let file = reactive({}) as any,
    performingSearch = ref(false),
    searchResultsData = reactive({}) as any,
    failedToFetch = ref(false),
    searchQueryParameter = ref(``),
    render = ref(false),
    thread3 = ref(``)

function updateSearchResultsData() {
  try {
    searchResultsData.value = store.state.searchResultsData[file.pdf_filename]
    failedToFetch.value = false
  } catch (e) {
    debug(e)
    failedToFetch.value = true
  }
}

function isMatchingAllowed(): any {
  try {
    if(file.operation_status) {
      return !Status.isLocked(file) && Status.getWeight(file.operation_status) > 1000
    }
    return false
  } catch(e) {
    debug(e)
    return false
  }
}

function getServiceData(searchResult: any): any {
  try {
    let services
    if(searchResult.services.constructor === Array) {
      /*
       * We're getting some weird return from our legacy systems, we need to tide it up a bit
       * [ [ "LP46722", "[\"POST_IMMEDIATELY\"]", "ACTIVE", null, "POST_IMMEDIATELY", "LOST", null ] ]
       */
      services = first(Object.values(searchResult.services)) as any
      services = (services).map(function(item: any){
        if(typeof item === `string`) {
          return item
                  .replaceAll(`[\"`,``)
                  .replaceAll(`\"]`,``)
                  .replaceAll(`_`,` `)
                  .replaceAll(`","`,`, `)
                  .toLowerCase();
        } else {
          return item
        }
      });
    } else if (typeof searchResult.services === 'object' ) {
      /*
      * { "PACKAGE_PRIVACY": [ "412078", "Package Privacy", "Suspended", "fa fa-times-circle em13 grey2", null, "STATUS_SUSPENDED", null ] }
      */
      services = first(Object.values(searchResult.services)) as any
    } else {
      /*
      * Unexpected input, we will display it as it comes.
      */
      services = searchResult.services
    }
    return services
  } catch (e) {
    debug(e)
    return ``
  }
}

function getServiceName(searchResult: any): any {
  try{
    const serviceData = getServiceData(searchResult)
    if(serviceData.constructor === Array) {
      // We assume the name of the service is at position 1 of the services array
      return serviceData[1]
    }
  } catch (e) {
    debug(e)
  }
  // Something is wrong, let's just display the whole thing and call it a day.
  return getServiceData(searchResult)
}

function getServiceStatus(searchResult: any): bool {
  if (isNull(searchResult.serviceName) || isNull(searchResult.seviceExpiration) || isNull(searchResult.seviceBeginning)) {
    return ``
  }

  return (new Date(searchResult.serviceExpiration)) >= now() && (new Date(searchResult.serviceBeginning)) <= now()
}

function getServiceStatusColor(searchResult: any) {
  return (getServiceStatus(searchResult) === `active` ? `text-success` : `text-danger`)
}

async function match(result: any) {
  let item = new PostItem()
  await item.build(file)
  await item.patch(result)
  await item.match()
}
function hasSearchResults(): boolean {
  return size(searchResultsData.value) > 0
}

function hasDirectors(result: any): boolean {
  return result && !isNull(result.members)
}

function getDirectorList(result: any): string {
  try {
    if(hasDirectors(result)) {
      return JSON.parse(result.members)
    }
  } catch (e) {
    debug(e)
  }
  return ``
}

watch(() => store.getters.mutated,  () => {
  performingSearch.value = store.state.appState.performingSearch
  thread3.value = store.state.appState.thread3
  searchQueryParameter.value = store.state.searchQueryParameter
  file = reactive(store.state.selectedFile)
  updateSearchResultsData()
})
</script>