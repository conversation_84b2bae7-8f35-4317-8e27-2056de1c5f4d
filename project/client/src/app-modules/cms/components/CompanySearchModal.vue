<template>
  <div id="companySearchModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div v-if="hasTrackedFiles" class="modal-content">
        <div class="modal-file-preview">
          <div class="mx-2" v-html="getFilePreview()"></div>
        </div>
        <div class="modal-header">
          <div v-show="file.imagePath">
            <span class="modal-title" id="exampleModalLabel">
              Predicted company name <small>(snapshot from letter)</small>:
            </span>
            <span v-if="file.imagePath">
              <!-- PREDICTED COMPANY NAME-->
              <img class="d-block m-auto mw-100"
                  :src="StorageHelper.getImageUrl(file.imagePath)"
                  :alt="file.companyName"
              />
            </span>
          </div>
        </div>
        <div class="modal-body">
          <div class="input-group mb-3">
            <span class="input-group-text text-muted" id="basic-addon0">
              <font-awesome-icon icon="fa-solid fa-building fa-fw " />
            </span>
            <input id="company-name-field" type="text" class="form-control" v-model="companyName" placeholder="Company name" aria-label="Company name" aria-describedby="basic-addon0"
              @keyup="captureKeypress($event)"
              @input="handleInputChange"
              @blur="hideAutofill"
            >
            <button id="search-company-name-button" class="btn btn-outline-secondary" type="button" @click="search(`Company name`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                  <span class="sr-only">Loading...</span>
              </span>
            </button>
            <ul v-show="showAutofill" class="autofill-options">
              <li
                v-for="(item, index) in filteredSearchTerms"
                :key="index"
                @click="selectAutofillItem(item.searchTerm)"
              >
                {{ item.searchTerm }}
              </li>
            </ul>
          </div>
          <div class="input-group mb-3">
            <span class="input-group-text text-muted" id="basic-addon1">
              <font-awesome-icon icon="fa-solid fa-id-badge fa-fw " />
            </span>
            <input type="text" class="form-control" v-model="officerName" @keyup="captureKeypress($event)" placeholder="Officer name" aria-label="Officer name" aria-describedby="basic-addon1">
            <button class="btn btn-outline-secondary" type="button" @click="search(`Officer name`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                  <span class="sr-only">Loading...</span>
              </span>
            </button>
          </div>
<!--          <div class="input-group mb-3">-->
<!--            <span class="input-group-text text-muted" id="basic-addon3">-->
<!--              <font-awesome-icon icon="fa-solid fa-user fa-fw " />-->
<!--            </span>-->
<!--            <input type="text" class="form-control" v-model="companyNumber" @keyup="captureKeypress($event)" placeholder="Company number" aria-label="Company number" aria-describedby="basic-addon3">-->
<!--            <button class="btn btn-outline-secondary" type="button" @click="search(`Company number`)" :disabled="performingSearch">-->
<!--              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />-->
<!--              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">-->
<!--                  <span class="sr-only">Loading...</span>-->
<!--              </span>-->
<!--            </button>-->
<!--          </div>-->
          <SearchResultsTable />
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal"> Cancel </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, reactive, ref, watch, onMounted } from "vue"
import SearchResultsTable from "@/app-modules/cms/components/SearchResultsTable.vue";
import {CompanySearch} from "@/app-modules/cms/services/CompanySearch";
import {AppState} from "@/app-modules/app-state/AppState";
import {useStore} from "vuex";
import StorageHelper from "@/app-modules/post-items/helpers/StorageHelper";
import { sortBy } from "lodash";

const toast: any = inject('toast');
const store = useStore()
let file = reactive(store.state.selectedFile),
    hasTrackedFiles = ref(false),
    performingSearch = ref(false)

async function captureKeypress(event: any) {
  if (event.key === `Enter`) {
    if (performingSearch.value) {
      // show toast notification that search is in progress
      toast.warning(`A search is already in progress.`)
    } else {
      await search(`any`)
    }
  }
}

function getFileUrl() {
  return StorageHelper.getPdfUrl(file.pdf_filename)
}

function getFilePreview()
{
  let html = `<object  class="PDF-delete-preview" data="${getFileUrl()}" type="application/pdf">`
  html += `<div>Unable to display pdf preview, click on the pdf filename above to open it in another page.</div>`
  html += `</object>`
  return html
}

function hasSearchParamsSet(): boolean {
  return !!(companyName.value || officerName.value || companyNumber.value)
}

async function search(by: string) {
  console.log('company name', companyName.value)
  console.log('officer name', officerName.value)
  console.log('company number', companyNumber.value)

  if (hasSearchParamsSet()) {
    performingSearch.value = true
    let search = new CompanySearch({
      companyName: companyName.value,
      officerName: officerName.value,
      companyNumber: companyNumber.value,
      parameter: by
    } as any)
    const result = await search.searchCompanyByQuery()
    if (result.error) {
      toast.error(result.error, {
        message: `Error trying to fetch search results, please try again in a few minutes. If this message persists, contact the system administrator. ${result.error}`,
        duration: 120000,
        dismissible: true,
      })
    }
  } else {
    toast.warning(`Search parameters not set.`)
  }
  performingSearch.value = false
  addToAutoFillStorage()
}

const companyName = computed({
  get () {
    return store.state.search.companyName
  },
  set (value) {
    store.commit('updateSearchParameters', {
      companyName: value,
      officerName: officerName.value,
      companyNumber: companyNumber.value
    })
  }
})

const officerName = computed({
  get () {
    return store.state.search.officerName
  },
  set (value) {
    store.commit('updateSearchParameters', {
      companyName: companyName.value,
      officerName: value,
      companyNumber: companyNumber.value
    })
  }
})

const companyNumber = computed({
  get () {
    return store.state.search.companyNumber
  },
  set (value) {
    store.commit('updateSearchParameters', {
      companyName: companyName.value,
      officerName: officerName.value,
      companyNumber: value
    })
  }
})

watch(() => store.getters.mutated, async () => {
  file = reactive(store.state.selectedFile)
  performingSearch.value = store.state.appState.performingSearch
  hasTrackedFiles.value = await AppState.hasTrackedFiles()
},{
  immediate: true
})

const showAutofill = ref(false)
let latestSearches = [] as any

function addToAutoFillStorage() {
  if (companyName.value !== "") {
    const searchTerm = companyName.value.trim().toLowerCase();
    latestSearches = getSearchHistory()
    const existingCompany = latestSearches.find((item: any) => item.searchTerm === searchTerm) as any;
    if (existingCompany) {
      existingCompany.timesSearch++;
      existingCompany.lastTimeSearched = new Date();
    } else {
      latestSearches.push(
        {
          searchTerm,
          timesSearch: 1,
          lastTimeSearched: new Date()
        }
      )
    }
    setSearchHistory(latestSearches)
    updateAutofill()
  }
}

interface SearchTerm {
  searchTerm: string;
  timesSearch: number;
  lastTimeSearched: string;
}
const filteredSearchTerms = ref<SearchTerm[]>([]);
const hideTimer = ref<number | undefined>(undefined);

function updateAutofill() {
  filteredSearchTerms.value = getSearchHistory()
}
function getSearchHistory() {
  const searchRecordsJSON = localStorage.getItem("latestSearches");
  const searchRecords = searchRecordsJSON ? JSON.parse(searchRecordsJSON) : [];
  return sanitizeSearchHistory(searchRecords)
}

function setSearchHistory(latestSearches: any) {
  localStorage.setItem("latestSearches", JSON.stringify(latestSearches));
}

function passedTwoWeeksCheck(date: Date) {
  const currentDate = new Date();
  const twoWeeksAgo = new Date(currentDate);
  twoWeeksAgo.setDate(currentDate.getDate() - 14);
  return date < twoWeeksAgo;
}

function sanitizeSearchHistory(searchRecords: any) {
  if(searchRecords.length > 10) {
    for (const record of searchRecords.slice().reverse()) {
      const lastTimeSearchedDate = new Date(record.lastTimeSearched);
      if (passedTwoWeeksCheck(lastTimeSearchedDate)) {
        const index = searchRecords.indexOf(record);
        if (index !== -1) {
          searchRecords.splice(index, 1);
        }
      }
    }
  }
  return sortBy(searchRecords, ['timesSearch', 'lastTimeSearched']).reverse();
}

const showSearchResults = () => {
  showAutofill.value = true;
};

const handleInputChange = (input: any) => {
  if (companyName.value === '') {
    hideAutofill();
  } else {
    autoSearch(input.target.value)
  }
};

const debounce = (func: Function, delay: number) => {
  let timerId: ReturnType<typeof setTimeout>;
  return (...args: any[]) => {
    clearTimeout(timerId);
    timerId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const autoSearch = debounce((input: string) => {
  if(input !== '') showSearchResults()

  const history = getSearchHistory() as SearchTerm[];
  const lowerCaseInput = input.toLowerCase();
  const filteredResults = history.filter((term) =>
    term.searchTerm.toLowerCase().startsWith(lowerCaseInput)
  );
  filteredResults.sort((a, b) => {
    const indexA = a.searchTerm.toLowerCase().indexOf(lowerCaseInput);
    const indexB = b.searchTerm.toLowerCase().indexOf(lowerCaseInput);
    if (indexA === -1 && indexB === -1) {
      return a.searchTerm.length - b.searchTerm.length;
    }
    if (indexA === -1) {
      return 1;
    }
    if (indexB === -1) {
      return -1;
    }
    return indexA - indexB;
  });
  filteredSearchTerms.value = filteredResults;
}, 100);


function hideAutofill() {
  hideTimer.value = setTimeout(() => {
    showAutofill.value = false;
  }, 200)
}

function selectAutofillItem(item: string) {
  companyName.value = item
}

onMounted(() => {
  updateAutofill()
})
</script>

<style scoped>
.autofill-options {
  list-style-type: none;
  position: absolute;
  top: 38px;
  width: 100%;
  z-index: 3;
  padding: 0;
  margin: 0;
  border: 1px solid #ccc;
  max-height: 180px;
  overflow-y: auto;
}

.autofill-options li {
  padding: 5px;
  cursor: pointer;
  background-color: #f9f9f9;
}

.autofill-options li:hover {
  background-color: #e5e5e5;
}
</style>
