import axios from "axios";
import {debug} from "@/app-modules/helpers/debug";
import {AppState} from "@/app-modules/app-state/AppState";
import {store} from "@/store";
import validator from "validator";
import {MAILROOM_SEARCH_API, COMPANIES_MADE_SIMPLE_AUTHORIZATION} from "@/config/config"

export const mailroomSearchApiUrl = MAILROOM_SEARCH_API
export const companiesMadeSimpleAuthorization = COMPANIES_MADE_SIMPLE_AUTHORIZATION

export class CompanySearch {
    companyName = ``
    officerName = ``
    companyNumber = ``
    query = ``
    data = {}

    constructor({
        companyName = '',
        officerName = '',
        companyNumber = '',
        parameter = ''
    }) {
        this.companyName = companyName
        this.officerName = officerName
        this.companyNumber = companyNumber
        this.query = `companyName=${companyName}&officerName=${officerName}`
        store.commit('updateSearchQueryParameter',parameter)
    }

    async searchCompanyByQuery() {

        try {
            if(
                !validator.isEmpty(this.companyName) ||
                !validator.isEmpty(this.officerName) ||
                !validator.isEmpty(this.companyNumber)
            ) {
                new AppState({
                    performingSearch: true,
                    thread3: `Running company search ( ${this.companyName} ${this.officerName} ${this.companyNumber} ).`
                })

                const {data} = await axios.get(`/api/old-company-search/?${this.query}`,{
                    withCredentials: true
                })
                this.data = data
                new AppState({
                    performingSearch: false,
                    thread3: ''
                })

                store.commit('updateSearchResultsData', this.data)

                return data
            }
            return {}
        } catch (e) {
            new AppState({
                performingSearch: false,
                thread3: 'Search failed'
            })

            debug(e)
            return {
                error: e
            }
        }
    }

    /**
     * Route for POST request from manual company search form.
     * It accepts data with company name or officer name or lp number and passes the request further to CMS endpoint
     * where the search in CMS and VO databases actually happens and result is then return back to the admin for further processing.
     * In the result there are more rows of possible matches - admin is supposed to pick the correct one.
     * todo: there could be too many rows found - some kind of pagination might be needed in here, or on CMS endpoint side.
     * todo: as soon as cors problem is fixed on cms, we can start using the method bellow instead of relying on the symfony backend
     */
    async directCompanySearchByQuery(searchApiUrl = mailroomSearchApiUrl): Promise<any>
    {
        try {
            new AppState({
                performingSearch: true,
                thread3: `Running company search ( ${this.companyName} ${this.officerName} ${this.companyNumber} ).`
            })

            const { data } = await axios.get(`${searchApiUrl}?${this.query}`,{
                headers: {
                    'X-Msg-Auth': companiesMadeSimpleAuthorization
                }
            })

            this.data = data

            new AppState({
                performingSearch: false,
                thread3: ''
            })

            store.commit('updateSearchResultsData', this.data)

            return data
        } catch (e) {
            new AppState({
                performingSearch: false,
                thread3: 'Search failed'
            })

            debug(e)
            return {
                error: e
            }
        }
    }

    static purge() {
        store.commit("resetSearch")
    }
}
