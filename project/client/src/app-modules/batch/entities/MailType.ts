export class MailType {
    public static TYPE_STATUTORY: string = 'STATUTORY'
    public static TYPE_NON_STATUTORY: string = 'NON_STATUTORY'

    public static SENDER_HMRC: string = 'HMRC'
    public static SENDER_COMPANIES_HOUSE: string = 'COMPANIES_HOUSE'
    public static SENDER_COURT_LETTER: string = 'COURT_LETTER'
    public static SENDER_OTHER: string = 'OTHER'

    public static STATUTORY_SENDERS: Array<string> = [
        MailType.SENDER_HMRC,
        MailType.SENDER_COMPANIES_HOUSE,
        MailType.SENDER_COURT_LETTER,
        MailType.SENDER_OTHER
    ]

    public static NON_STATUTORY_SENDERS: Array<string> = [
        MailType.SENDER_OTHER
    ]

    public static isMailTypeStatutory(mailType: string): boolean {
        return mailType === MailType.TYPE_STATUTORY
    }

    public static isMailTypeNonStatutory(mailType: string): boolean {
        return mailType === MailType.TYPE_NON_STATUTORY
    }

    public static isMailSenderCourtLetter(mailSender: string): boolean {
        return mailSender === MailType.SENDER_COURT_LETTER
    }

    public static isMailSenderOther(mailSender: string): boolean {
        return mailSender === MailType.SENDER_OTHER
    }

    public static isMailTypeValid(mailType: string): boolean {
        return MailType.isMailTypeStatutory(mailType) || MailType.isMailTypeNonStatutory(mailType)
    }

    public static isMailSenderValid(mailSender: string): boolean {
        return MailType.STATUTORY_SENDERS.includes(mailSender) || MailType.NON_STATUTORY_SENDERS.includes(mailSender)
    }

    public static isMailTypeAndSenderValid(mailType: string, mailSender: string): boolean {
        if (!MailType.isMailTypeValid(mailType) || !MailType.isMailSenderValid(mailSender)) {
            return false
        }

        if (MailType.isMailTypeStatutory(mailType)) {
            return MailType.STATUTORY_SENDERS.includes(mailSender)
        }

        if (MailType.isMailTypeNonStatutory(mailType)) {
            return MailType.NON_STATUTORY_SENDERS.includes(mailSender)
        }

        return false
    }

    public static getMailTypeFromBatchTypeForDatabaseInsertion(batchType: string): string {
        return MailType.getMailTypeFromBatchType(batchType).toLowerCase().replace(/_/g, '-')
    }

    public static getMailTypeFromBatchType(batchType: string): string {
        if (!MailType.isBatchTypeValid(batchType)) {
            return ''
        }

        return MailType.isMailTypeNonStatutory(batchType) ? MailType.TYPE_NON_STATUTORY : MailType.TYPE_STATUTORY
    }

    public static getSenderFromBatchType(batchType: string): string {
        if (!MailType.isBatchTypeValid(batchType)) {
            return ''
        }

        return MailType.isMailTypeNonStatutory(batchType) ? MailType.SENDER_OTHER : batchType
    }

    public static getBatchType(mailType: string, mailSender: string): string {
        if (!MailType.isMailTypeAndSenderValid(mailType, mailSender)) {
            return ''
        }

        if (MailType.isMailTypeNonStatutory(mailType)) {
            return MailType.TYPE_NON_STATUTORY
        }

        return mailSender
    }

    public static isBatchTypeValid(batchType: string): boolean {
        if (!batchType || batchType === '') {
            return false
        }

        return MailType.STATUTORY_SENDERS.includes(batchType) || batchType === MailType.TYPE_NON_STATUTORY
    }

    public static getMailTypeValidationFeedback(mailType: string): string {
        if (!mailType || mailType === '') {
            return 'Mail type is null or empty!'
        }

        if (!MailType.isMailTypeStatutory(mailType) && !MailType.isMailTypeNonStatutory(mailType)) {
            return `Invalid mail type: '${mailType}'`
        }

        return ''
    }

    public static getMailSenderValidationFeedback(mailSender: string): string {
        if (!mailSender || mailSender === '') {
            return 'Mail sender is null or empty!'
        }

        if (!MailType.STATUTORY_SENDERS.includes(mailSender) && !MailType.NON_STATUTORY_SENDERS.includes(mailSender)) {
            return `Invalid mail sender: '${mailSender}'`
        }

        return ''
    }
}
