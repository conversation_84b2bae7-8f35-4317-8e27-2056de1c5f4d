import BucketApi from '@/app-modules/mantle-apis/BucketApi';
import { AppState } from '@/app-modules/app-state/AppState';
import { debug } from '@/app-modules/helpers/debug';
import { filter, size } from 'lodash';
import { PostItem } from '@/app-modules/post-items/entities/PostItem';
import { Status } from '@/app-modules/post-items/entities/Status';
import { store } from '@/store';
import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper';
import axios from 'axios';

export class Batch {
    batchType: any
    operator: any
    batchId: any
    batchSize: any
    isTest: boolean | any
    isNewProcess: boolean | any
    dtc: any
    dte: any

    constructor() { }

    async build({
        batchId = null,
        batchType = null,
        operator = null,
        batchSize = null,
    }: any) {
        let defaultBatchId = ''
        if (!batchId) {
            defaultBatchId = await Batch.generateId(batchType)
        }
        this.batchId = await Batch.getValue(this.batchId, 'batchId', batchId, defaultBatchId)
        this.batchType = await Batch.getValue(this.batchId, 'batchType', batchType, '')
        this.operator = await Batch.getValue(this.batchId, 'operator', operator, store.state.uid)
        this.batchSize = await Batch.getValue(this.batchId, 'batchSize', batchSize, 0)
        this.isTest = await Batch.getValue(this.batchId, 'isTest', null, store.state.testMode)
        this.dtc = await Batch.getValue(this.batchId, 'dtc', null, new Date().getTime())
        this.isNewProcess = true
    }

    static async getBatchById(batchId: string): Promise<Batch> {
        const batch = new Batch()
        if (batchId && batchId != '') {
            const batchData = await store.state.batches[batchId]
            if (batchData) {
                await batch.build(batchData)
            }
        }
        return batch
    }

    static getOperator(batch: Batch): string {
        return batch.operator ? batch.operator : ''
    }

    static getBatchSize(batch: Batch): number {
        return batch.batchSize ? batch.batchSize : size(Batch.getTrackedFilesInBatch(batch))
    }

    static getBatchType(batch: Batch): string {
        return batch.batchType ? batch.batchType : ''
    }

    static getBatchId(batch: Batch): string {
        return batch.batchId ? batch.batchId : ''
    }

    static getTrackedFilesInBatch(batch: Batch): any {
        return filter(store.state.trackedFiles, (item) => {
            return item.batchId === batch.batchId
        })
    }

    static getTrackedFilesInBatchByBatchId(batchId: string): any {
        return filter(store.state.trackedFiles, (item) => {
            if (batchId === '%') {
                return true
            }
            return item.batchId === batchId
        })
    }

    static getDismissibleItemsInBatch(batchId: string): any {
        return filter(Batch.getTrackedFilesInBatchByBatchId(batchId), (item) => {
            return Status.isCompleted(item)
        })
    }

    static getLockedItemsInBatch(batchId: string): any {
        return filter(Batch.getTrackedFilesInBatchByBatchId(batchId), (item) => {
            return Status.isLocked(item)
        })
    }

    static hasDismissibleItemsInBatch(batchId: string): boolean {
        return (size(Batch.getDismissibleItemsInBatch(batchId)) > 0)
    }

    static hasLockedItemsInBatch(batchId: string): boolean {
        return (size(Batch.getLockedItemsInBatch(batchId)) > 0)
    }

    static hasOperator(batch: Batch): boolean {
        return (Batch.getOperator(batch) != null && Batch.getOperator(batch) != '')
    }

    static hasBatchType(batch: Batch): boolean {
        return (Batch.getBatchType(batch) != null && Batch.getBatchType(batch) != '')
    }

    static hasBatchId(batch: Batch): boolean {
        return (Batch.getBatchId(batch) != null && Batch.getBatchId(batch) != '')
    }

    static hasBatchSize(batch: Batch): boolean {
        return (Batch.getBatchSize(batch) != null && Batch.getBatchSize(batch) > 0)
    }

    static isValid(batch: Batch): boolean {
        return Batch.hasBatchId(batch)
            && Batch.hasBatchSize(batch)
            && Batch.hasBatchType(batch)
            && Batch.hasOperator(batch)
    }

    static async generateId(batchType): Promise<string> {
        const environmentTag = Batch.generateEvironmentTag()
        const devTag = !!store.state.isDeveloper ? `${store.state.uid.substring(0, 3)}-` : ''
        const typeTag = Batch.generateTypeTag(batchType)
        const dateTag = Batch.generateDateTag()
        const batchCounter = (await axios.get('/api/get-batch-count')).data['count']

        return `${environmentTag}${devTag}${typeTag}-${dateTag}-${batchCounter}`
    }

    static generateEvironmentTag(): string {
        let environmentTag = ''
        if (EnvironmentHelper.isDevelopmentEnvironment()) {
            environmentTag = 'DEV-'
        }
        if (EnvironmentHelper.isStagingEnvironment()) {
            environmentTag = 'STG-'
        }
        return `${environmentTag}`
    }

    static generateTypeTag(batchType): string {
        const types = {
            'HMRC': 'HMRC',
            'COMPANIES_HOUSE': 'CH',
            'OTHER': 'OTHER',
            'NON_STATUTORY': 'NONSTAT'
        }
        return types[batchType] ? types[batchType] : 'OTHER'
    }

    static generateDateTag(): string {
        const currentDate : Date = new Date()
        const d = currentDate.getUTCDate().toString().padStart(2, "0")
        const m = (currentDate.getUTCMonth() + 1).toString().padStart(2, "0")
        const y = currentDate.getUTCFullYear().toString().substring(2, 4)

        return `${d}${m}${y}`
    }

    getBatchId(): string {
        return this.batchId ? this.batchId : ''
    }

    static async getValue(batchId: string, key: string, input: any = null, defaultsTo: any = ``): Promise<any> {
        try {
            if (input !== null && input !== '') {
                return input
            } else {
                const storedValue = await Batch.getStoredValue(batchId, key)

                if (storedValue !== null && storedValue !== '') {
                    return storedValue
                } else {
                    return defaultsTo
                }
            }
        } catch (e) {
            return defaultsTo
        }
    }

    private static async getStoredValue(batchId: string, key: string): Promise<any> {
        try {
            const batchData = await store.getters.batches[batchId]

            if (!batchData) {
                return ''
            }

            return batchData[key]
        } catch (e) {
            return ''
        }
    }

    save() {
        store.commit("setBatch", this)
    }

    async delete(retries = 10) {
        let success = false

        while (retries-- > 0 && !success) {
            try {
                this.setStateAsBusy()
                let batchDeletionResponse = await this.batchDeletionRequest()
                if (!batchDeletionResponse) {
                    continue
                }
                await this.removeItemsFromStorage()
                this.setStateAsFree()
                success = true
            } catch (e) {
                debug(e)
                this.setStateAsFree()
            }
        }

        if (!success) {
            try {
                await this.removeItemsFromStorage()
                this.setStateAsFree()
            } catch (e) {
                debug(e)
            }
        }

        return success
    }

    async removeItemsFromStorage() {
        let trackedFilesInBatch = Batch.getTrackedFilesInBatch(this)
        var deletionPromises: Promise<boolean>[] = []
        for (const trackedFile of Object.keys(trackedFilesInBatch)) {
            if (trackedFilesInBatch[trackedFile]) {
                deletionPromises.push(this.itemDeletion(trackedFilesInBatch[trackedFile]))
            }
        }
        await Promise.all(deletionPromises)
        store.commit("deleteBatch", this)
    }

    public async batchDeletionRequest(): Promise<boolean> {
        try {
            const bucketApi = new BucketApi()
            return await bucketApi.deleteBatch(this.batchId)
        } catch (e) {
            debug(e)
            return false
        }
    }

    async itemDeletion(deleteItem) {
        try {
            let item = new PostItem()
            await item.build(deleteItem)
            await item.deleteFromStorage()
            return true
        } catch (e) {
            return false
        }
    }

    static async purge() {
        if (await AppState.hasBatches()) {
            store.commit("resetBatches")
        }
    }

    setStateAsBusy() {
        new AppState({
            ready: false,
            busy: true,
            acceptUploads: false
        })
    }

    setStateAsFree() {
        new AppState({
            ready: true,
            busy: false,
            acceptUploads: true
        })
    }
}
