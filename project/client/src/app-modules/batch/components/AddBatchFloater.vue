<template>
  <div v-if="hasTrackedFiles && ready && !busy">
    <div id="addBatchFloater" class="position-fixed bottom-0 end-0 rounded-circle mb-5 me-5"
         data-bs-toggle="tooltip"
         data-bs-placement="left"
         title="Add batch"
    >
      <button  type="button" class="position-relative btn btn-primary btn-lg rounded-circle"
               style="cursor: pointer;"
               data-bs-toggle="modal"
               data-bs-target="#addBatchModal"
      >
        <font-awesome-icon icon="fa-solid fa-plus" />
        <span class="visually-hidden">Add Batch</span>
      </button>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {store} from "@/store";
import {AppState} from "@/app-modules/app-state/AppState";

export default defineComponent({
  name: "AddBatchFloater",
  data() {
    return {
      ready: false,
      busy: false,
      hasTrackedFiles: false,
      render: false
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated,  async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
    })
  },
})
</script>