<template>
  <div :id="`deleteAllInBatchModal-${batchId}`" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div v-if="hasTrackedFiles" class="modal-content">
        <div class="modal-header">
          <h5 class="text-center">
            Are you certain you want to delete <b>ALL</b> tracked files for {{batchId}}?
            <b> This action cannot be undone. </b>
          </h5>
        </div>
        <div class="modal-footer">
          <span v-if="render"> </span>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal" @click="deleteAllInBatch()">
            <font-awesome-icon icon="fa-solid fa-trash fa-fw " />
            Delete
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <font-awesome-icon icon="fa-solid fa-xmark" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import {AppState} from "@/app-modules/app-state/AppState";
import {store} from "@/store";
import {Batch} from "@/app-modules/batch/entities/Batch";

export default defineComponent({
  name: "DeleteAllInBatchModal",
  props: {
    batchId: {
      type: String,
      default: ''
    }
  },
  components: {
  },
  data() {
    return {
      render: false,
      ready: false,
      busy: false,
      hasTrackedFiles: false
    }
  },
  methods: {
    async deleteAllInBatch() {
      const batch = await Batch.getBatchById(this.$props.batchId)
      await batch.delete()
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated, async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
    })
  }
})
</script>