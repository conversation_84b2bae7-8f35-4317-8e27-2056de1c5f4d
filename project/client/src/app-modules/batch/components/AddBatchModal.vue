<template>
  <div  id="addBatchModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-body">
          <div class="d-flex flex-column align-items-center justify-content-center">
            <div v-if="hasTrackedFiles && ready && !busy">
              <Uploader :allowAddingToExistingBatches="true" :asModal="true"/>
            </div>
            <div v-else>
              <Loader />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent} from "vue"
import Uploader from "@/app-modules/uploader/components/Uploader.vue";
import {store} from "@/store";
import {AppState} from "@/app-modules/app-state/AppState";
import Loader from "@/components/Loader.vue";

export default defineComponent({
  name: "AddBatchModal",
  components: {Loader, Uploader},
  data() {
    return {
      ready: false,
      busy: false,
      hasTrackedFiles: false,
      render: false
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated,  async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.hasTrackedFiles = await AppState.hasTrackedFiles()
      this.$forceUpdate()
    })
  },
})
</script>
