import {AppState} from "@/app-modules/app-state/AppState";
import {ScheduleBackgroundTasks} from "@/app-modules/app-state/Scheduler";
import { Status } from "../post-items/entities/Status";

export async function firstTimeSetup(): Promise<void> {
    // set appState as currently loading
    new AppState({
        ready: false,
        busy: true,
        task: `Setting up application`,
        thread0: ``,
        thread1: ``,
        thread2: ``,
        thread3: ``,
        thread4: ``,
        thread5: ``,
        updatingStatusInstances: 0,
        allowBackgroundUpdate: true,
        updatingWaiting: false,
        updatingStatus: false,
        performingSearch: false,
        cleaningMessages: false,
        acknowledging: false,
    })

    const hasTrackedFiles = await AppState.hasTrackedFiles()
    // clean batchInfo if there are no trackedFiles
    if(!hasTrackedFiles) {
        await AppState.purge()
    }
    // remove any cached files and status messages
    // AppState.resetTrackedFiles()

    // remove any stored messages
    Status.purgeAllMessages()

    // perform first time setup
    ScheduleBackgroundTasks().then() // don't wait, this will run in the background

    // Stop loading animation
    new AppState({
        ready: true,
        busy: false,
        task: ``
    })
}