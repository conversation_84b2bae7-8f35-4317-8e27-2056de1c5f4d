import {store} from "@/store";
import {debug} from "@/app-modules/helpers/debug";
import {AppState} from "@/app-modules/app-state/AppState";
import {PostItem} from "@/app-modules/post-items/entities/PostItem";

export class Stats {
    statusMessages: any
    batchStats: any

    constructor() {
    }

    async build() {
        this.statusMessages = await Stats.getStoredValue('statusMessages', this.initStatusMessagesStats())
        this.batchStats = await Stats.getStoredValue('batchStats', {})
        this.save()
    }

    initStatusMessagesStats() {
        return {
            messagesReceived: 0,
            messagesUsed: 0,
            messagesIgnored: 0,
            messagesACK: 0,
            messagesForMeButNotTracked: 0
        }
    }

    static async updatePostItemStatsInBatch({batchId, pdf_filename, operation_status}: PostItem) {
        try {
            const stats = new Stats()
            await stats.build()
            if(!stats.batchStats[batchId]) {
                stats.batchStats[batchId] = {}
                stats.batchStats[batchId][pdf_filename] = operation_status
            } else {
                stats.batchStats[batchId][pdf_filename] = operation_status
            }
            stats.save()
        } catch (e) {
            debug(e)
        }
    }

    static async removePostItemStatsInBatch({batchId, pdf_filename}: PostItem) {
        try {
            const stats = new Stats()
            await stats.build()
            if(stats.batchStats[batchId]) {
                delete stats.batchStats[batchId][pdf_filename]
            }
            stats.save()
        } catch (e) {
            debug(e)
        }
    }

    public static async incrementStatusMessagesStats(key: string) {
        try {
            let statusMessagesStats = new Stats()
            await statusMessagesStats.build()
            statusMessagesStats.statusMessages[key]++
            statusMessagesStats.save()
        } catch (e) {
            debug(e)
        }
    }

    public static async getStoredValue(key: string, defaultsTo: any): Promise<any> {
        try {
            return store.state.stats[key] ? store.state.stats[key] : defaultsTo
        } catch(e) {
            return defaultsTo
        }
    }

    public static getStats() {
        return store.state.stats ? store.state.stats : {}
    }

    public save() {
        store.commit("updateStats",this)
    }

    static async purge() {
        if(await AppState.hasStats()) {
            store.commit("resetStats")
        }
    }
}