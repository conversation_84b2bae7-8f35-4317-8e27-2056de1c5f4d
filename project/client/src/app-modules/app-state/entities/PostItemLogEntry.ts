import {store} from "@/store";
import {first, last} from "lodash";
import {getElapsedAsHumanTime} from "@/app-modules/helpers/clock";
import {AppState} from "@/app-modules/app-state/AppState";

export class PostItemLogEntry {
    pdf_filename: any
    operationData: any

    constructor() {
    }

    async build({
        pdf_filename = '',
        message = 'New entry',
        operation_info = {},
        operation_status = ``
    }) {
        this.pdf_filename = pdf_filename
        this.operationData = await this.getStoredValue('operationData',[])
        const data = {
            message: message,
            operation_info: operation_info,
            operation_status: operation_status,
            dtc: Date.now(),
            elapsedSinceLastEntry: `${this.getElapsedTime(Date.now())}`,
            totalTimeElapsed: `${this.getTotalElapsedTime(Date.now())}`
        }
        this.operationData.push(data)
        this.save()
    }

    private getTotalElapsedTime(dtc: number): string {
        try {
            const firstEntry = first(this.operationData) as any || dtc
            let elapsed = dtc - firstEntry.dtc || 0
            return getElapsedAsHumanTime(elapsed)
        } catch (e) {
            return getElapsedAsHumanTime(0)
        }
    }

    private getElapsedTime(dtc: number): string {
        try {
            const lastEntry = last(this.operationData) as any || dtc
            let elapsed = dtc - lastEntry.dtc || 0
            return getElapsedAsHumanTime(elapsed)
        } catch (e) {
            return getElapsedAsHumanTime(0)
        }
    }

    private async getStoredValue(key: string, defaultsTo: any): Promise<any> {
        try {
            return store.state.trackedFilesLogs[this.pdf_filename][key] ? store.state.trackedFilesLogs[this.pdf_filename][key] : defaultsTo
        } catch(e) {
            return defaultsTo
        }
    }

    public static getLogs(pdf_filename: string) {
        return store.state.trackedFilesLogs[pdf_filename] ? store.state.trackedFilesLogs[pdf_filename] : []
    }

    public static purgeOrphanLogs() {
        for (const pdf_filename in store.state.trackedFilesLogs) {
            if (!Object.keys(store.state.trackedFiles).includes(pdf_filename)) {
                delete store.state.trackedFilesLogs[pdf_filename]
            }
        }
    }

    save() {
        store.commit("updateTrackedFileLogs",this)
    }

    static async purge() {
        if (await AppState.hasLogs()) {
            store.commit("resetTrackedFileLogs")
        }
    }
}