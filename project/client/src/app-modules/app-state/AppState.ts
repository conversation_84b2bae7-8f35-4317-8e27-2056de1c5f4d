import {store} from "@/store";
import {size} from "lodash";

/*
*  This class is used with vuex in order to keep track and control what the application is doing at all times.
*/
export class AppState {
    debug = false // This will determine if the application is in debug mode
    ready = true // This determines if the status table can be displayed
    busy = false // This will determine if the page is shown as loading or not
    authenticated = false //
    lastVerified = '' //
    lastRequestedPage = '' //
    currentPage = '' //
    acceptUploads = false // Toggles file uploader show/hide
    task = '' // Any generic task the application is running will show up here
    thread0 = '' // pullWaiting usually updates this thread
    thread1 = '' // Status.pull() usually updates this thread
    thread2 = '' // delete requests use this thread
    thread3 = '' // company searches usually updates this thread
    thread4 = '' // match requests use this thread
    acknowledging = false // is a message acknowledgement currently taking place?
    preparingToUpload = false // period between upload is triggered and list of batches is shown
    uploading = false // are files being uploaded?
    updatingWaiting = false // is an updatingWaiting currently running?
    updatingStatus = false // is an updatingStatus currently running?
    updatingStatusInstances = 0 // # of pull status updates running at the same time
    matchingFile = false // Are any files currently being saved?
    autoMatchingFiles = false // Are any files currently being autoMatched?
    deletingFile = false // Are any files being deleted?
    performingSearch = false // is a search currently running?
    allowBackgroundUpdate = false // Should background updates be allowed?
    allowAutoMatch = false // Should it auto-match when it has high confidence?
    allowAutoSearch = false // Should it auto-search when clicking on the edit button?
    /*
    *  Sets the instance of AppState from parameters passed
    *  if any given parameter is absent (null) uses the data from vuex store in case it exists,
    *  otherwise resets them to default
    */
    constructor({
        debug = null,
        ready = null,
        busy = null,
        authenticated = null,
        lastVerified = null,
        lastRequestedPage = null,
        currentPage = null,
        acceptUploads = null,
        task = null,
        thread0 = null,
        thread1 = null,
        thread2 = null,
        thread3 = null,
        thread4 = null,
        acknowledging = null,
        preparingToUpload = null,
        uploading = null,
        updatingWaiting = null,
        updatingStatus = null,
        updatingStatusInstances = null,
        deletingFile = null,
        matchingFile = null,
        autoMatchingFiles = null,
        performingSearch = null,
        allowBackgroundUpdate = null,
        allowAutoMatch = null,
        allowAutoSearch = null
    }: any) {
        this.debug = AppState.getValue("debug", debug, false)
        this.ready = AppState.getValue("ready", ready, false)
        this.busy = AppState.getValue("busy", busy, false)
        this.authenticated = AppState.getValue("authenticated", authenticated, false)
        this.lastVerified = AppState.getValue("lastVerified", lastVerified, false)
        this.lastRequestedPage = AppState.getValue("lastRequestedPage", lastRequestedPage)
        this.currentPage = AppState.getValue("currentPage", currentPage)
        this.acceptUploads = AppState.getValue("acceptUploads", acceptUploads, false)
        this.task = AppState.getValue("task", task)
        this.thread0 = AppState.getValue("thread0", thread0)
        this.thread1 = AppState.getValue("thread1", thread1)
        this.thread2 = AppState.getValue("thread2", thread2)
        this.thread3 = AppState.getValue("thread3", thread3)
        this.thread4 = AppState.getValue("thread4", thread4)
        this.acknowledging = AppState.getValue("acknowledging", acknowledging, false)
        this.preparingToUpload = AppState.getValue("preparingToUpload", preparingToUpload)
        this.uploading = AppState.getValue("uploading", uploading)
        this.updatingWaiting = AppState.getValue("updatingWaiting", updatingWaiting, false)
        this.updatingStatus = AppState.getValue("updatingStatus", updatingStatus, false)
        this.updatingStatusInstances = AppState.getValue("updatingStatusInstances", updatingStatusInstances, 0)
        this.deletingFile = AppState.getValue("deletingFile", deletingFile, false)
        this.matchingFile = AppState.getValue("matchingFile", matchingFile, false)
        this.autoMatchingFiles = AppState.getValue("autoMatchingFiles", autoMatchingFiles, false)
        this.performingSearch = AppState.getValue("performingSearch", performingSearch, false)
        this.allowBackgroundUpdate = AppState.getValue("allowBackgroundUpdate", allowBackgroundUpdate, false)
        this.allowAutoMatch = AppState.getValue("allowAutoMatch", allowAutoMatch, false)
        this.allowAutoSearch = AppState.getValue("allowAutoSearch", allowAutoSearch, false)
        this.save()
    }

    private static getValue(key: string, input: any = null, defaultsTo: any = ``): any {
        try {
            return (input != null ? input : store.state.appState[key] || defaultsTo)
        } catch(e) {
            return defaultsTo
        }
    }

    static resetTrackedFiles(): void {
        store.commit("resetTrackedFiles")
    }

    static async hasTrackedFiles(): Promise<boolean> {
        try {
            const trackedFiles = await store.dispatch("getTrackedFiles")
            return size(trackedFiles) > 0
        } catch (e) {
            return false
        }
    }

    static async hasBatches(): Promise<boolean> {
        try {
            const batches = await store.dispatch("getBatches")
            return size(batches) > 0
        } catch (e) {
            return false
        }
    }

    static async hasLogs(): Promise<boolean> {
        try {
            const logs = await store.dispatch("getLogs")
            return size(logs) > 0
        } catch (e) {
            return false
        }
    }

    static async hasStats(): Promise<boolean> {
        try {
            const logs = await store.dispatch("getStats")
            return size(logs) > 0
        } catch (e) {
            return false
        }
    }

    static incrementUpdatingStatusInstances() {
        store.commit("incrementUpdatingStatusInstances")
    }

    static decrementUpdatingStatusInstances() {
        store.commit("decrementUpdatingStatusInstances")
    }

    save() {
        store.commit("setAppState", this)
    }

    static async purge() {
        store.commit("setSelectedFile", {})
        store.commit("resetTrackedFiles")
        store.commit("resetBatches")
        store.commit("resetStats")
        store.commit("resetTrackedFileLogs")
        store.commit("resetSearch")
        store.commit("resetAutoMatcher")
        store.commit("resetStatusMessages")
    }
}