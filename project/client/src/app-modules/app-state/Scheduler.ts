import { AppState } from '@/app-modules/app-state/AppState';
import { AutoMatcher } from '@/app-modules/post-items/services/AutoMatcher';
import { Batch } from '@/app-modules/batch/entities/Batch';
import { MAX_STATUS_UPDATE_INSTANCES } from '@/config/config';
import { PostItemLogEntry } from '@/app-modules/app-state/entities/PostItemLogEntry';
import { Stats } from '@/app-modules/app-state/entities/Stats';
import { Status } from '@/app-modules/post-items/entities/Status';
import { store } from '@/store';

/*
* Run update tasks on the background
*/
export async function ScheduleBackgroundTasks() {
    setInterval(function () {
        // Check if allow background updates is set to true every 2 seconds
        if (store.state.appState.allowBackgroundUpdate) {
            // If it isn't already updating the waiting queue, run the new update request
            if (!store.state.appState.updatingWaiting) {
                //pullWaiting().then()
            }
            // If it isn't already updating the status, run the new update request
            if (!store.state.appState.updatingStatus || store.state.appState.updatingStatusInstances < MAX_STATUS_UPDATE_INSTANCES) {
                Status.pull()
            }
        }
    }, 2000);

    // If it isn't already trying to match all files, run the auto matcher every 3 seconds
    setInterval(function () {
        if (store.state.appState.allowAutoMatch) {
            AutoMatcher.tryMatchAll().then()
        }
    }, 3000);

    // Ack all messages that are in the toAck array every 5 seconds, if the app isn't already acknowledging messages.
    setInterval(function () {
        if (!store.state.appState.acknowledging) {
            Status.acknowledge()
        }
    }, 5000);

    // Check if there are no tracked files (They've been all matched, purged or deleted) every second, if so, allow uploads
    setInterval(async function () {
        if (!await AppState.hasTrackedFiles()) {
            await Batch.purge()
            await PostItemLogEntry.purge()
            await Stats.purge()
            await Status.purgeAllMessages()
            new AppState({
                acceptUploads: !await AppState.hasTrackedFiles()
            })
        }
    }, 1000);

    // Clean the mailroom-images cache every two seconds, if AppState.hasTrackedFiles() is false
    setInterval(async function () {
        if (!await AppState.hasTrackedFiles()) {
            self.caches.keys().then(keys => {
                keys.forEach(key => {
                    if (key.includes("mailroom-images")) {
                        self.caches.delete(key)
                    }
                })
            })
        }
    }, 2000);

    // Check for orphan logs every 5 seconds
    setInterval(async function () {
        if (await AppState.hasLogs()) {
            PostItemLogEntry.purgeOrphanLogs()
        }
    }, 5000);
}
