<template>
  <div>
    <div class="alert alert-warning">
      If you’re experiencing a delay in image updates, please refresh the page.
    </div>
    <div v-if="acceptUploads" class="card p-3">
      <form method="post" action="#" id="" enctype="multipart/form-data">
        <div v-if="isAllowedToShowBatchSelector()" class="form-group mb-3">
          <p v-show="!isSelectedBatchValid()" class="text-danger">Please, select an option to continue</p>
          <select class="form-select" aria-label="Default select example" @change="setSelectedBatch($event)"
            :disabled="!isAllowedToSelectABatch()">
            <option value="" selected>New batch</option>
            <template v-for="batch in batches">
              <option :value="batch.batchId">{{ batch.batchId }}</option>
            </template>
          </select>
        </div>
        <div v-if="isAllowedToShowFilesInputSelector()" class="form-group input-group files text-center mb-3">
          <input id="formFileMultiple" class="form-control" type="file" :multiple="true" ref="file"
            accept=".pdf,application/pdf" @change="setInputFiles($event)">
          <button class="btn btn-sm" :class="isFormValid() ? 'btn-primary' : 'btn-outline-secondary'" type="button"
            id='upload-button' @click="submitFiles()" :data-bs-dismiss="`${asModal ? 'modal' : ''}`"
            :disabled="!isAllowedToUpload()">
            <small>
              <font-awesome-icon icon="fa-solid fa-upload" />
            </small>
            <small class="pl-2">
              Upload
            </small>
          </button>
        </div>
        <hr>
        <div class="col">
          <div v-if="isAllowedToShowMailTypeSelector()" class="form-group mt-3 font-weight-bold">
            <p v-show="!isSelectedMailTypeValid()" class="text-danger">Invalid mail type selected</p>
            <div class="bv-no-focus-ring col-form-label pt-0"
              :class="!isSelectedMailTypeValid() ? 'text-danger' : 'text-muted'">
              <font-awesome-icon icon="fa-solid fa-envelopes-bulk" />
              <span>Mail type:</span>
            </div>
            <div class="col">
              <div v-if="isAllowedToShowMailTypeSelector()" class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio1">
                  <input class="form-check-input" type="radio" v-model="selectedMailType" name="type-mail-item"
                    id="inlineRadio1" value="STATUTORY" :disabled="!isAllowedToSelectMailType()" checked="true">
                  <span>Statutory</span>
                </label>
              </div>
            </div>
            <div class="col">
              <div v-if="isAllowedToShowMailTypeSelector()" class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio2">
                  <input class="form-check-input" type="radio" v-model="selectedMailType" name="type-mail-item"
                    id="inlineRadio2" value="NON_STATUTORY" :disabled="!isAllowedToSelectMailType()">
                  <span>Non-statutory</span>
                </label>
              </div>
            </div>
          </div>
          <div class="form-group mt-3 font-weight-bold">
            <p v-show="!isSelectedMailSenderValid()" class="text-danger">Invalid mail sender selected</p>
            <div class="bv-no-focus-ring col-form-label pt-0"
              :class="!isSelectedMailSenderValid() ? 'text-danger' : 'text-muted'">
              <font-awesome-icon icon="fa-solid fa-envelopes-bulk" />
              <span>Mail sender:</span>
            </div>
            <div class="col">
              <div class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio3">
                  <input class="form-check-input" type="radio" v-model="selectedMailSender" name="mail-sender-item"
                    id="inlineRadio3" value="HMRC" :disabled="!isAllowedToSelectMailSender()">
                  <span>HMRC</span>
                </label>
              </div>
            </div>
            <div class="col">
              <div class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio4">
                  <input class="form-check-input" type="radio" v-model="selectedMailSender" name="mail-sender-item"
                    id="inlineRadio4" value="COMPANIES_HOUSE" :disabled="!isAllowedToSelectMailSender()">
                  <span>Company House</span>
                </label>
              </div>
            </div>
            <div class="col">
              <div class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio5">
                  <input class="form-check-input" type="radio" v-model="selectedMailSender" name="mail-sender-item"
                         id="inlineRadio5" value="COURT_LETTER" :disabled="!isAllowedToSelectMailSender()">
                  <span>Court Letter</span>
                </label>
              </div>
            </div>
            <div class="col">
              <div class="form-check form-check-inline">
                <label class="form-check-label" for="inlineRadio6">
                  <input class="form-check-input" type="radio" v-model="selectedMailSender" name="mail-sender-item"
                    id="inlineRadio6" value="OTHER" :disabled="!isAllowedToSelectMailSender()">
                  <span>Other</span>
                </label>
              </div>
            </div>
            <p v-show="isStatutoryCourtLetter()" class="text-danger">
              Do <b>not</b> match these items to VO clients.
            </p>
            <p v-show="isStatutoryOther()" class="text-danger">This option may not be used for non-statutory files!</p>
          </div>
        </div>
      </form>
    </div>
    <div v-else>
      <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading">Uploads are currently disabled</h4>
        <p class="mb-0">
          If you are seeing this message and the number of items and batches is low there might be an error,
          please contact the administrator.
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue"
import { Uploader } from '../services/Uploader'
import { AppState } from "@/app-modules/app-state/AppState"
import { store } from "@/store"
import { size } from "lodash"
import { debug } from "@/app-modules/helpers/debug"
import { Batch } from "@/app-modules/batch/entities/Batch"
import EnvironmentHelper from "@/app-modules/helpers/EnvironmentHelper"
import { MailType } from '../../batch/entities/MailType'
import { setupTestElements } from '@/main'

export default defineComponent({
  name: "Uploader",
  props: {
    allowAddingToExistingBatches: {
      type: Boolean,
      default: false
    },
    asModal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isProductionEnvironment: EnvironmentHelper.isProductionEnvironment(),
      batches: store.state.batches,
      selectedBatchId: '',
      selectedBatch: {} as Batch,
      selectedBatchType: '',
      selectedMailType: '',
      selectedMailSender: '',
      settingSelectedBatch: false,
      settingInputFiles: false,
      allowPickingMailType: true,
      render: false,
      uploaderFilesCount: 0,
      acceptUploads: true
    }
  },
  methods: {
    isStatusReady() {
      return !this.settingSelectedBatch && !this.settingInputFiles
    },
    isSelectedBatchValid(): boolean {
      if (this.selectedBatchId === '') {
        return true
      }

      if (Batch.isValid(this.selectedBatch)) {
        return Batch.getBatchId(this.selectedBatch) === this.selectedBatchId
          && Batch.getBatchType(this.selectedBatch) === this.selectedBatchType
      }

      return false
    },
    isFilesInputValid(): boolean {
      return this.uploaderFilesCount > 0
    },
    isSelectedMailTypeValid(): boolean {
      return MailType.isMailTypeValid(this.selectedMailType)
    },
    isSelectedMailSenderValid(): boolean {
      return MailType.isMailSenderValid(this.selectedMailSender)
    },
    isFormValid(): boolean {
      return this.isSelectedBatchValid() &&
        this.isFilesInputValid() &&
        this.isSelectedMailTypeValid() &&
        this.isSelectedMailSenderValid()
    },
    isAllowedToShowBatchSelector(): boolean {
      return this.allowAddingToExistingBatches && size(this.batches) > 0
    },
    isAllowedToShowFilesInputSelector(): boolean {
      return this.acceptUploads
    },
    isAllowedToShowMailTypeSelector(): boolean {
      return true
    },
    isAllowedToSelectABatch(): boolean {
      return !this.settingSelectedBatch
    },
    isAllowedToUpload(): boolean {
      return this.isFormValid() && this.isStatusReady()
    },
    isAllowedToSelectMailType(): boolean {
      return this.isSelectedBatchValid() && this.isStatusReady() && this.allowPickingMailType
    },
    isAllowedToSelectMailSender(): boolean {
      return this.isSelectedBatchValid()
        && this.isSelectedMailTypeValid()
        && this.isStatusReady()
        && this.allowPickingMailType
        && MailType.isMailTypeStatutory(this.selectedMailType)
    },
    async setSelectedBatch(event: any): Promise<void> {
      try {
        this.settingSelectedBatch = true

        this.selectedBatchId = event.target.value
        this.selectedBatch = {} as Batch
        this.selectedBatchType = ''

        if (this.selectedBatchId && this.selectedBatchId !== '') {
          const result = await Batch.getBatchById(this.selectedBatchId)
          this.selectedBatch = result

          if (this.selectedBatch && Batch.getBatchId(this.selectedBatch) === this.selectedBatchId) {
            this.handleBatchSelection()
            this.allowPickingMailType = false
          } else {
            throw new Error(`Batch (id: ${this.selectedBatchId}) was not found!`)
          }
        } else {
          this.resetSelectedMailType()
          this.resetSelectedMailSender()
          this.allowPickingMailType = true
        }
      } catch (e) {
        debug(e)

        this.resetSelectedBatch()
        this.resetSelectedMailType()
        this.resetSelectedMailSender()
        this.allowPickingMailType = true
      } finally {
        this.settingSelectedBatch = false
      }
    },
    setInputFiles(event: any) {
      try {
        this.settingInputFiles = true
        this.uploaderFilesCount = size(event.target.files)
      } catch (e) {
        debug(e)

        this.resetForm()
      } finally {
        this.settingInputFiles = false
      }
    },
    resetSelectedBatch() {
      this.selectedBatchId = ''
      this.selectedBatch = {} as Batch
      this.selectedBatchType = ''
    },
    resetInputFiles() {
      try {
        this.uploaderFilesCount = 0

        if (this.$refs.file) {
          // @ts-ignore
          if (this.$refs.file.files) {
            // @ts-ignore
            this.$refs.file.files = null
          }
          // @ts-ignore
          if (this.$refs.file.value) {
            // @ts-ignore
            this.$refs.file.value = null
          }
        }
      } catch (e) {
        debug(e)
      }
    },
    resetSelectedMailType() {
      this.selectedMailType = MailType.TYPE_STATUTORY
      this.selectedBatchType = ''
      this.allowPickingMailType = true
    },
    resetSelectedMailSender() {
      this.selectedMailSender = MailType.SENDER_HMRC
      this.selectedBatchType = ''
      this.allowPickingMailType = true
    },
    resetForm() {
      this.resetSelectedBatch()
      this.resetInputFiles()
      this.resetSelectedMailType()
      this.resetSelectedMailSender()
    },
    handleBatchSelection() {
      this.selectedBatchType = Batch.getBatchType(this.selectedBatch)

      if (this.selectedBatchType === 'NON_STATUTORY') {
        this.handleNonStatutoryMailType()
      } else if (this.selectedBatchType !== '') {
        this.handleStatutoryMailType(this.selectedBatchType)
      }

      this.allowPickingMailType = false
    },
    handleNonStatutoryMailType() {
      this.selectedBatchType = this.selectedMailType = 'NON_STATUTORY'
      this.selectedMailSender = 'OTHER'
    },
    handleStatutoryMailType(mailSender: string) {
      if (mailSender === 'NON_STATUTORY') {
        return
      }

      this.selectedMailType = 'STATUTORY'
      this.selectedMailSender = mailSender
      this.selectedBatchType = mailSender
    },
    isNonStatutory(): boolean {
      return MailType.isMailTypeNonStatutory(this.selectedBatchType) && MailType.isMailTypeNonStatutory(this.selectedMailType)
    },
    isStatutoryOther() {
      return MailType.isMailSenderOther(this.selectedMailSender) && !this.isNonStatutory()
    },
    isStatutoryCourtLetter() {
      return MailType.isMailSenderCourtLetter(this.selectedMailSender) && !this.isNonStatutory()
    },
    async submitFiles() {
      try {
        if (!this.isFormValid()) {
          throw new Error('Form is invalid!')
        }

        new AppState({
          ready: false,
          busy: false,
          preparingToUpload: true,
          task: 'Preparing to upload files'
        })

        // @ts-ignore
        const uploader: Uploader = new Uploader(this.$refs.file.files)

        if (this.isSelectedBatchValid() && this.selectedBatchId != '') {
          await uploader.setBatch(
              Batch.getBatchId(this.selectedBatch),
              Batch.getBatchType(this.selectedBatch)
          )
        } else {
          await uploader.setBatch(
            this.selectedBatchId,
            MailType.getBatchType(this.selectedMailType, this.selectedMailSender)
          )
        }

        await uploader.submitFilesInChunks()
      } catch (e) {
        debug(e)
      } finally {
        this.resetForm()

        new AppState({
          ready: true,
          busy: false,
          preparingToUpload: false,
          uploading: false,
          task: ''
        })

        this.$forceUpdate()
      }
    },
    mailTypeHandler(mailType: string) {
      if (mailType === 'NON_STATUTORY') {
        this.handleNonStatutoryMailType()
      } else if (mailType === 'STATUTORY') {
        this.handleStatutoryMailType(this.selectedMailSender)
      }
    },
    mailSenderHandler(mailSender: string) {
      if (this.selectedMailType === 'NON_STATUTORY') {
        this.handleNonStatutoryMailType()
      } else if (this.selectedMailType === 'STATUTORY') {
        this.handleStatutoryMailType(mailSender)
      }
    }
  },
  created() {
    Promise.resolve(this.resetForm()).then()
    this.$store.watch(() => this.$store.getters.mutated, mutated => {
      this.acceptUploads = store.state.appState.acceptUploads
      this.batches = store.state.batches
      this.$forceUpdate()
      setupTestElements()
    })
  },
  watch: {
    selectedMailType: function (val) {
      this.mailTypeHandler(val)
    },
    selectedMailSender: function (val) {
      this.mailSenderHandler(val)
    }
  },
  mounted() {
    this.resetForm()
    setupTestElements()
  },
})
</script>
