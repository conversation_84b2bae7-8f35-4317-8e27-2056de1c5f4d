// Sends uploaded files to endpoint "/api/upload-file"

import BucketApi from '@/app-modules/mantle-apis/BucketApi';
import { AppState } from '@/app-modules/app-state/AppState';
import { Batch } from '@/app-modules/batch/entities/Batch';
import { debug } from '@/app-modules/helpers/debug';
import { isErrorStatus } from '@/app-modules/post-items/StatusConfig';
import { PostItem } from '@/app-modules/post-items/entities/PostItem';
import { PostItemLogEntry } from '@/app-modules/app-state/entities/PostItemLogEntry';
import { Stats } from '@/app-modules/app-state/entities/Stats';
import { store } from '@/store';

const OK_RESPONSES = [200, 201, 204]

const FILE_UPLOAD_MAX_RETRIES: number = 5
const FILE_UPLOAD_STATUS_SUCCESS: string = "PDF_UPLOADED"
const FILE_UPLOAD_STATUS_WAITING: string = "WAITING_TO_UPLOAD"
const FILE_UPLOAD_STATUS_FAIL: string = "PDF_UPLOAD_FAILED"
const FILE_UPLOAD_TIMEOUT: number = 100000
const FILE_UPLOAD_RETRY_DELAY: number = 1000

const CHUNK_SIZE: number = 50
const CHUNK_DELAY: number = 2000

export class Uploader {
    files: Array<any>
    batch: Batch
    dtc: Date
    succeeded: Array<any>

    constructor(files: any, batch: Batch = new Batch()) {
        this.files = Array.from(files)
        this.batch = batch
        this.dtc = new Date()
        this.succeeded = []
        this.initTrackedFiles().then()
    }

    public async setBatch(batchId: string, batchType: string): Promise<void> {
        try {
            if (batchId) {
                this.batch = await Batch.getBatchById(batchId)

                if (!Batch.isValid(this.batch)) {
                    throw new Error(`Invalid batch! (id: ${batchId})`)
                }

                const newBatchSize = Batch.getBatchSize(this.batch) + this.files.length
                await this.batch.build({
                    batchId: batchId,
                    batchSize: newBatchSize
                })
            } else {
                if (!batchType) {
                    throw new Error('Invalid batch type!')
                }

                this.batch = new Batch()
                await this.batch.build({
                    batchType: batchType,
                    batchSize: this.files.length
                })
            }

            if (!Batch.isValid(this.batch)) {
                throw new Error('Invalid batch!')
            }

            this.batch.save()
        } catch (e) {
            this.batch = new Batch()
            this.files = []
            this.succeeded = []
            debug(e)
        }
    }

    public async submitFilesInChunks(): Promise<void> {
        try {
            for (let i = 0; i < this.files.length; i += CHUNK_SIZE) {
                const chunkOfFiles = this.files.slice(i, i + CHUNK_SIZE)
                await this.submitChunk(chunkOfFiles)
                await new Promise((f) => setTimeout(f, CHUNK_DELAY))
            }
        } catch (error) {
            debug(error)
        }

        new AppState({
            ready: true,
            busy: false,
            uploading: false,
            task: ``
        })
    }

    private async submitChunk(chunkArray: Array<any>): Promise<void> {
        try {
            for (let i = 0; i < chunkArray.length; i++) {
                let chunkFile = chunkArray[i]

                if (!chunkFile || !chunkFile.name || this.succeeded.includes(chunkFile)) {
                    debug(`Skip file ${chunkFile}`, 'w')
                    continue
                }

                let fileName = await this.getNewFileName(chunkFile.name)
                let fileFormData = await this.prepareFile(chunkFile)

                if (!fileFormData && store.state.testMode) {
                    await new PostItemLogEntry().build({
                        pdf_filename: fileName,
                        message: 'Failed to prepare file for upload',
                        operation_status: FILE_UPLOAD_STATUS_FAIL
                    })
                    continue
                }

                new AppState({
                    ready: true,
                    busy: false,
                    uploading: true,
                    task: `Uploading ${this.succeeded.length}/${this.files.length} (${fileName})`
                })

                let postItem = await this.trackFile(fileName)

                if (!postItem && store.state.testMode) {
                    await new PostItemLogEntry().build({
                        pdf_filename: fileName,
                        message: 'Failed to add to tracked files',
                        operation_status: FILE_UPLOAD_STATUS_FAIL
                    })
                    continue
                }

                if (Uploader.shouldStoreLog(FILE_UPLOAD_STATUS_WAITING)) {
                    await new PostItemLogEntry().build({
                        pdf_filename: postItem.pdf_filename,
                        message: 'Finished saving to vuex, preparing to send file to bucket...',
                        operation_status: FILE_UPLOAD_STATUS_WAITING
                    })
                }

                this.sendFile(fileFormData).then(async (uploadResult) => {
                    await postItem.setOperationData({
                        operation_status: uploadResult,
                        operationStatusTimestamp: new Date(),
                        operation_info: { message: uploadResult }
                    })
                    postItem.enableOperationDataProtection()
                    postItem.save()

                    if (uploadResult === FILE_UPLOAD_STATUS_SUCCESS) {
                        this.succeeded.push(chunkFile)
                    }

                    await Stats.updatePostItemStatsInBatch(postItem)

                    if (Uploader.shouldStoreLog(uploadResult)) {
                        await new PostItemLogEntry().build({
                            pdf_filename: postItem.pdf_filename,
                            message: 'Updated operation data for file, awaiting for status update...',
                            operation_status: uploadResult
                        })
                    }
                })
            }
        } catch (error) {
            debug(error)
        }
    }

    private async initTrackedFiles(): Promise<void> {
        try {
            if (!await AppState.hasTrackedFiles()) {
                AppState.resetTrackedFiles()
            }
        } catch (e) {
            debug(e)
        }
    }

    private async getNewFileName(oldFileName: string): Promise<string> {
        try {
            return `${(Batch.getBatchId(this.batch))}_${oldFileName}`
        } catch (e) {
            debug(e)
            return ''
        }
    }

    static shouldStoreLog(operation_status): boolean {
        return isErrorStatus(operation_status) && store.state.testMode
    }

    private async trackFile(fileName: string): Promise<any> {
        try {
            const item = new PostItem(true, false)

            await item.build({
                pdf_filename: fileName,
                batchId: this.batch.getBatchId(),
                operation_status: FILE_UPLOAD_STATUS_WAITING,
                operation_info: { message: `The file has been added to the locally tracked files and is awaiting upload.` },
                operationStatusTimestamp: new Date(),
                test: `${this.batch.isTest}`
            })

            item.save()
            await Stats.updatePostItemStatsInBatch(item)

            return item
        } catch (e) {
            debug(e)
            return null
        }
    }

    private async prepareFile(file: any): Promise<any> {
        try {
            if (!file || file.type !== 'application/pdf') {
                return false
            }

            if (!Batch.isValid(this.batch)) {
                throw new Error('Invalid batch!')
            }

            let formData = new FormData()
            formData.append(`file`, file, await this.getNewFileName(file.name))
            formData.append(`batchId`, this.batch.getBatchId())
            formData.append(`batchSize`, await Batch.getValue(this.batch.getBatchId(), 'batchSize'))
            formData.append(`typeMail`, await Batch.getValue(this.batch.getBatchId(), 'batchType'))
            formData.append(`operator`, await Batch.getValue(this.batch.getBatchId(), 'operator'))
            formData.append(`test`, await Batch.getValue(this.batch.getBatchId(), 'isTest', null, store.state.testMode))
            formData.append(`dtc`, this.dtc.toUTCString())

            return formData
        } catch (e) {
            debug(e)
            return false
        }
    }

    private async sendFile(formData: FormData): Promise<string> {
        let result = false
        try {
            result = await this.makeUploadRequestWithRetries(formData)
                .then(async (response) => await this.processUploadSuccessResponse(response))
                .catch(async (error) => await this.processUploadFailedResponse(error))
        } catch (error) {
            debug(error)
            result = false
        }
        return result ? FILE_UPLOAD_STATUS_SUCCESS : FILE_UPLOAD_STATUS_FAIL
    }

    private async makeUploadRequestWithRetries(formData: FormData, retries = 0): Promise<any> {
        try {
            const bucketApi = new BucketApi()
            return await bucketApi.uploadPostItem(formData, FILE_UPLOAD_TIMEOUT)
        } catch (error) {
            if (retries >= FILE_UPLOAD_MAX_RETRIES) {
                throw error
            }
            await new Promise((f) => setTimeout(f, FILE_UPLOAD_RETRY_DELAY))
            return this.makeUploadRequestWithRetries(formData, retries + 1)
        }
    }

    private async processUploadSuccessResponse(response: any): Promise<boolean> {
        if (!OK_RESPONSES.includes(response.status)) {
            debug(`Upload: Response returned ${response.status} status code! data: ${JSON.stringify(response.data)}`, 'w')
            return false
        }
        return true
    }

    private async processUploadFailedResponse(error: any): Promise<boolean> {
        if (error.response) {
            debug(JSON.stringify(error.response.data))
        } else {
            debug(error.message)
        }
        return false
    }

}
