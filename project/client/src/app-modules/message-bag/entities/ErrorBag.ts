import { __ } from "../../auth/assets/i18n"
import { size } from 'lodash'

export class ErrorBag {
    errors: any

    constructor() {
        this.buildLoginBag()
    }

    addToNamedBag(bag: string, value: string): void{
        try {
            this.errors[bag].push(value)
        } catch (err) {
            console.error(err)
        }
    }

    getErrors(bag: string): any {
        try {
            if(size(this.errors[bag]) > 0)
                return this.errors[bag]
            return null
        } catch {
            return null
        }
    }

    inputHasError(input: string): boolean {
        try {
            return !!this.getErrors(input);
        } catch {
            return false
        }
    }

    setMessageToNamedBag(bag: string,exc: string): void {
        let message
        message = __(exc)
        this.addToNamedBag(bag,message)
    }

    buildLoginBag(): void {
        this.errors = {
            email: [],
            password: [],
            generic: [],
        }
    }
}