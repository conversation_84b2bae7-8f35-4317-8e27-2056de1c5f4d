// Returns the difference between start date and end date in seconds
import {debug} from "@/app-modules/helpers/debug";

export function elapsedTimeInSeconds(start: string, end: any = Date.now()): number {
    const startTime = new Date(start)
    const endTime = new Date(end)
    let timeDiff = endTime.valueOf() - startTime.valueOf()
    timeDiff /= 1000

    return Math.round(timeDiff)
}

// adds zeroes to the beginning of any number and returns a string
export function zeroFill(n: number, pad = 2): string {
    return n.toString().padStart(pad,'0')
}

export function msToHours(n: number) {
    return Math.floor((n / 1000) / 3600)
}

// Receives the elapsed time as milliseconds and returns a human-readable formatted string
export function getElapsedAsHumanTime(elapsed: number): string {
    const h = msToHours(elapsed)
    const i = Math.floor((elapsed / 1000) % 3600 / 60)
    const s = Math.floor((elapsed / 1000) % 60)
    const ms = elapsed > 999 ? Math.floor(elapsed % 1000) : elapsed
    return `${zeroFill(h)}:${zeroFill(i)}:${zeroFill(s)}:${zeroFill(ms,3)}`
}

// Receives any date and returns it as a UTC human-readable formatted string
export function getTimestamp(timestamp: Date): string {
    try {
        const date = `${timestamp.getUTCFullYear()}/${zeroFill(timestamp.getUTCMonth())}/${zeroFill(timestamp.getUTCDate())}`
        const time = `${zeroFill(timestamp.getUTCHours())}:${zeroFill(timestamp.getUTCMinutes())}:${zeroFill(timestamp.getUTCSeconds())}:${zeroFill(timestamp.getUTCMilliseconds(), 3)}`
        return `${date} ${time}`
    } catch (e) {
        debug(e)
        return new Date().toString()
    }
}

// Sleep
export async function sleep(sleepTimer: any) {
    await new Promise(r => setTimeout(r, sleepTimer));
}