export enum Environment {
  DEVELOPMENT = "development",
  PRODUCTION = "production",
  STAGING = "staging",
}

export default class EnvironmentHelper {
  private static currentEnvironment: Environment = (process.env.NODE_ENV ?? Environment.DEVELOPMENT) as Environment

  public static getCurrentEnvironment(): Environment {
    return EnvironmentHelper.currentEnvironment
  }

  public static isDevelopmentEnvironment(): boolean {
    return EnvironmentHelper.currentEnvironment === Environment.DEVELOPMENT
  }

  public static isProductionEnvironment(): boolean {
    return EnvironmentHelper.currentEnvironment === Environment.PRODUCTION
  }

  public static isStagingEnvironment(): boolean {
    return EnvironmentHelper.currentEnvironment === Environment.STAGING
  }
}
