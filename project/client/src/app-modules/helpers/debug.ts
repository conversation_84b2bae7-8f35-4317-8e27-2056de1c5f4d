import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper';

export function debug(data: any, type = 'e') {
    if (!EnvironmentHelper.isProductionEnvironment()) {
        switch (type) {
            case 'l':
            case 'log':
                console.log(data)
                break
            case 'w':
            case 'warn':
                console.warn(data)
                break
            case 't':
            case 'trace':
                console.trace(data)
                break
            case 'e':
            default:
                console.error(data)
                break
        }
    }
}
