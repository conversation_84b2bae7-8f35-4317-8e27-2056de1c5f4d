import {debug} from "@/app-modules/auth/helpers/ExceptionHandler";

export function translateAxiosResponse(result: any): string {
    try{
        return result.response.data.error // out of the range of 2xx
    } catch {
        try{ // no response was received
            return result.request // instance of XMLHttpRequest
        } catch {
            debug(result.message);
            return result.message;
        }
    }
}

export function getAxiosResponseStatus(result: any): any {
    try{
        return result.status
    } catch {
        debug(result);
    }
}