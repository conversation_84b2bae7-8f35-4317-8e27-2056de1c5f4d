<template>
  <div v-if="authenticated">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import {defineComponent} from "vue"
import {store} from "@/store";

export default defineComponent({
  name: "Auth",
  data() {
    return {
      authenticated: store.state.appState.authenticated,
    }
  },
  created() {
    this.$store.watch(() => this.$store.getters.mutated,  mutated => {
      this.authenticated = store.state.appState.authenticated
    })
  }
})
</script>