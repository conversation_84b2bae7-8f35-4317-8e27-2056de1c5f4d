import {defineComponent} from "vue"
import { Login } from "../services/auth/Login"
import { __ } from '../assets/i18n'
import { ErrorBag } from "../../message-bag/entities/ErrorBag";

export default defineComponent({
    name: "<PERSON>ginForm",
    data() {
        return {
            showPassword: false,
            canSubmit: false,
            formSubmitStatusIsLoading: false,
            customToken: "",
            form: {
                email: '',
                password: '',
            },
            _login: {} as any,
            messageBag: {} as any
        }
    },
    methods: {
        __,
        inputHasError(input: string): string {
            return this.messageBag.inputHasError(input) ? 'has-error' : ''
        },
        getErrors(input: string) {
            return this.messageBag.getErrors(input)
        },
        togglePassword(): void {
            this.showPassword = !this.showPassword
        },
        toggleButton(): void {
            this.canSubmit = !this.canSubmit
        },
        toggleFormSubmitStatus(): void {
            this.formSubmitStatusIsLoading = !this.formSubmitStatusIsLoading
        },
        async loginSubmit() {
            try {
                this.toggleButton()
                this.toggleFormSubmitStatus()
                this._login.setEmail(this.form.email)
                this._login.setPassword(this.form.password)
                await this._login.loginWithEmailAndPassword()
                this.toggleFormSubmitStatus()
                this.toggleButton()
            } catch(err) {
                console.error(err)
            }
        },    
        handleQuickLoginError(event) {
            this.messageBag.setMessageToNamedBag('generic', event.detail);
        }
    },
    async beforeMount() {
        this.messageBag = new ErrorBag();
        this._login = new Login(this.messageBag);
        this.customToken = getParamFromUrl('x_factor');
    },
    mounted() {
        document.addEventListener('quickLoginError', this.handleQuickLoginError);
        if (this.customToken !== '') {
            try {
                this._login.loginWithCustomToken(this.customToken);
            } catch (error) {
                console.error(error);
                this.messageBag.setMessageToNamedBag('generic', `An unexpected error occurred: ${error}. Please contact support.`);
            }
        } else {
            const quickLoginButton = document.getElementById('quick-login-button');
            if (quickLoginButton) {
                quickLoginButton.removeAttribute('disabled')
                this.canSubmit = true
            }
        }
    },
});

function getParamFromUrl(paramName: string): string {
    const urlParams = new URLSearchParams(window.location.search);
    const param = urlParams.get(paramName)
    if (param) {
        return param;
    } else {
        return '';
    }
    
}