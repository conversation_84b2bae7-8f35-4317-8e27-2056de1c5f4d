<template>
    <div class="container mt-5 btm20">
      <div class="row">
        <div class="form-horizontal col-sm-10 col-md-8 col-lg-6 m-auto">
          <div class="form-group" :class="inputHasError('email') || inputHasError('generic')">
            <!--EMAIL-->
            <div class="col">
              <div class="input-group">
                <span class="input-group-text text-muted" id="basic-addon0" :class="inputHasError('email') ? 'border border-danger' : ''">
                  <font-awesome-icon icon="fa-solid fa-envelope fa-fw " />
                </span>
                <input type="email"
                       id="login_form_email_"
                       class="form-control"
                       :class="inputHasError('email') ? 'border border-danger' : ''"
                       required="required"
                       v-model="form.email"
                       :placeholder="__('Email')"
                       :aria-label="__('Email')"
                       aria-describedby="basic-addon0">
              </div>
            </div>
            <div class="col text-danger mt-1" v-if="inputHasError('email')">
              <span id="login_form_email-error_" class="help-block">
                <ul class="list-unstyled">
                  <li class="error-message" v-for="error in getErrors('email')" v-html="error">
                  </li>
                </ul>
              </span>
            </div>
          </div>
          <!--PASSWORD-->
          <div class="form-group mt-3" :class="inputHasError('password') || inputHasError('generic')">
            <div class="col">
              <div class="input-group">
                <span class="input-group-text text-muted" id="basic-addon1"
                :class="inputHasError('password') ? 'border border-danger' : ''">
                  <font-awesome-icon icon="fa-solid fa-lock fa-fw" />
                </span>
                <input :type="showPassword ? 'text' : 'password'"
                       id="login_form_password_"
                       v-model="form.password"
                       required="required"
                       class="form-control"
                       :class="inputHasError('password') ? 'border border-danger' : ''"
                       :placeholder="__('Password')"
                       :aria-label="__('Password')"
                       aria-describedby="basic-addon1">
                       <span class="input-group-text password-icon" :class="inputHasError('password') ? 'border border-danger' : ''" @click="showPassword = !showPassword">
                        <font-awesome-icon  role="button" :icon="showPassword ? 'fa-solid fa-eye-slash fa-fw' : 'fa-solid fa-eye fa-fw'" />
                      </span>
              </div>
            </div>
            <div class="col p-2 text-danger" v-if="inputHasError('password') || inputHasError('generic')">
              <span id="login_form_password-error_" class="help-block" v-show="inputHasError('password')">
                <ul class="list-unstyled">
                  <li class="error-message" v-for="error in getErrors('password')" v-html="error">
                  </li>
                </ul>
              </span>
              <span id="login_form_error_" class="help-block" v-show="inputHasError('generic')">
                <ul class="list-unstyled">
                  <li class="error-message" v-for="error in getErrors('generic')" v-html="error">
                  </li>
                </ul>
              </span>
            </div>
          </div>
          <!--SUBMIT BTN-->
          <div class="form-group mt-5">
            <div class="col text-center">
              <button id="login_form-submit_button" class="m-auto btn btn-md btn-secondary w-100" name="send" @click="loginSubmit" :disabled="!canSubmit" style="max-width: 185px;">
                <small v-if="!formSubmitStatusIsLoading"> {{ __('Sign in with email') }} </small>
                <span v-else > {{ __('Loading') }} &nbsp;
                  <font-awesome-icon icon="fa-solid fa-circle-notch fa-spin" />
              &nbsp; </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script src="./LoginForm.ts" lang="ts"></script>

<style scoped>
  #login_form_password_{
    border-right: none!important;
  }
  .password-icon{
    background-color: transparent!important;
  }
  .error-message{
    font-size: 0.9rem!important;
  }
</style>