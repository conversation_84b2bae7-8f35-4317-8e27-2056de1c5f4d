import { defineComponent } from "vue";
import { __ } from "../assets/i18n";
import { LoginException } from "../exceptions/LoginException"
import axios from 'axios';
import { UNIVERSAL_LOGIN_URL } from "@/config/config";

export default defineComponent({
    methods: {
        __,
        async isServerUp(url: string): Promise<boolean> {
        try {
            const response = await axios.get('/api/isserverup', {
            params: {
                url: url
            }
            });
            return response.data.status === 'up';
        } catch (error) {
            throw new LoginException(`Mantle is down, error: ${error}`);
        }
        },
        disableQuickLoginButton(status: string = 'true') {
            const button = document.getElementById('quick-login-button');
            if (status === 'true') {
                button?.setAttribute('disabled', status);    
            } else {
                button?.removeAttribute('disabled');
            }
            
        },
        disableRegularLoginButton(status: string = 'true') {
            const button = document.getElementById('login_form-submit_button');
            if (status === 'true') {
                button?.setAttribute('disabled', status);    
            }
        },
        async loginWithUniversalLogin() {
            try {
                this.disableQuickLoginButton();
                this.disableRegularLoginButton();
                if (await this.isServerUp(UNIVERSAL_LOGIN_URL)) {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.search = '';
                    const currentUrlWithoutGetParams = encodeURIComponent(currentUrl.href);
                    window.location.href = `${UNIVERSAL_LOGIN_URL}/?link=${currentUrlWithoutGetParams}`;
                } else {
                    throw new LoginException(`Mantle is down.`);
                }
            } catch (error) {
                const event = new CustomEvent('quickLoginError', { 'bubbles': true, detail: error });
                this.disableQuickLoginButton('false');
                document.dispatchEvent(event);
            }
        }
    }
});
