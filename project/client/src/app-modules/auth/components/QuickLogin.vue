<template>
  <div class="brick-desktop brick-desktop--lg btm20">
    <div id="firebaseui-auth-container" lang="en">
      <div
        class="firebaseui-container firebaseui-page-provider-sign-in firebaseui-id-page-provider-sign-in firebaseui-use-spinner">
        <div class="firebaseui-card-content">
            <ul class="firebaseui-idp-list">
              <li class="firebaseui-list-item">
                <button @click="loginWithUniversalLogin" id="quick-login-button" disabled="true"
                  class="firebaseui-idp-button mdl-button mdl-js-button mdl-button--raised firebaseui-idp-google firebaseui-id-idp-button"
                  data-provider-id="google.com" style="background-color:#ffffff" data-upgraded=",MaterialButton"><span
                    class="firebaseui-idp-icon-wrapper"><img class="firebaseui-idp-icon" alt=""
                      src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"></span><span
                    class="firebaseui-idp-text firebaseui-idp-text-long">Sign in with Google</span><span
                    class="firebaseui-idp-text firebaseui-idp-text-short">Google</span>
                </button>
              </li>
            </ul>
        </div>
        <div class="firebaseui-card-footer firebaseui-provider-sign-in-footer"></div>
      </div>
    </div>
  </div>
</template>
<script src="./QuickLogin.ts" lang="ts"></script>