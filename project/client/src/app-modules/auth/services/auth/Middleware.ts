import router from '@/router';
import { AppState } from '@/app-modules/app-state/AppState';
import { debug } from '@/app-modules/helpers/debug';
import { LOGIN_ROUTE_PATH } from '@/config/config';
import { store } from '@/store';

export function isUserAuthenticated(): boolean {
    return store.state.appState.authenticated;
}

export async function routeRequiresAuth(to: any, from: any, next: any) {
    if (isUserAuthenticated()) {
        new AppState({
            currentPage: to.path
        })
        next()
    } else {
        debug(`403 :: NOT AUTHORIZED (${to.path})`, 'w')
        new AppState({
            lastRequestedPage: to.path
        })
        await router.push(LOGIN_ROUTE_PATH)
    }
}
