import { FirebaseAuthenticator } from "../../../firebase/auth/FirebaseAuthenticator"
import { Session } from "./Session"
import { FirebaseAuth, session } from "../../main"

export class Auth {
    private readonly _FirebaseAuthenticator: FirebaseAuthenticator
    private readonly _Session: Session

    constructor() {
        this._FirebaseAuthenticator = FirebaseAuth
        this._Session = session
    }

    firebaseInstance() {
        return this._FirebaseAuthenticator
    }

    session() {
        return this._Session
    }
}