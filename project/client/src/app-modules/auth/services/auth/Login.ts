import { Auth } from "./Auth"
import { ErrorBag } from "../../../message-bag/entities/ErrorBag"
import { debug, HandleException } from "../../helpers/ExceptionHandler"
import {LoginValidator} from "@/app-modules/auth/validators/LoginValidator";
import axios, { AxiosResponse } from "axios";
import { useStore } from "vuex";
import {CUSTOM_TOKEN_ENCRYPTION_KEY} from "@/config/config"

export class Login extends Auth {
    // @ts-ignore
    private _email: string
    // @ts-ignore
    private _password: string
    protected signInMethods: any
    public ErrorBag: ErrorBag
    private store = useStore()

    constructor(ErrorBag: ErrorBag) {
        super()
        this.ErrorBag = ErrorBag
    }

    setEmail(email: string): void {
        this._email = email
    }

    setPassword(password: string): void {
        this._password = password
    }

    getPassword(): string {
        return this._password
    }

    getEmail(): string {
        return this._email
    }

    async setSignInMethods() {
        try {
            this.signInMethods = await this.firebaseInstance().fetchSignInMethods(this.getEmail())
        } catch(error) {
            this.signInMethods = []
            HandleException(error, this.ErrorBag)
        }
    }

    async loginWithFirebaseUsingEmailProvider() {
        return await this.firebaseInstance().signInWithEmail(this.getEmail(), this.getPassword())
    }

    static async setSession(authToken: string,refreshToken: string) {
        try {
            return await axios.post("/api/session/",{},{
                withCredentials: true,
                headers: {
                    'Authorization': authToken,
                    'X-Auth': refreshToken
                }
            })
        } catch(e) {
            debug(e)
        }
    }

    async loginWithEmailAndPassword(): Promise<any> {
        try {
            this.ErrorBag.buildLoginBag()
            LoginValidator.validateEmail(this.getEmail())
            LoginValidator.validatePassword(this.getPassword())
            await this.setSignInMethods()
            if(this.firebaseInstance().hasEmailAndPasswordProvider(this.signInMethods)) {
                let response = await this.loginWithFirebaseUsingEmailProvider()
                this.store.commit('setUserEmail', this.getEmail())
                await Login.setSession(response.auth.currentUser.accessToken,response.auth.currentUser.refreshToken);
            }
        } catch(error) {
            HandleException(error, this.ErrorBag)
        }
    }

    async loginWithCustomToken(encryptedCustomToken: string): Promise<any> {
        const customToken = convertData(encryptedCustomToken, CUSTOM_TOKEN_ENCRYPTION_KEY);
        
        if (customToken) { 
            try {
                let response = await this.firebaseInstance().signInWithCustomToken(customToken);
                if (response && response.user) {
                const user = response.user;
                const idToken = await user.getIdToken();
                const refreshToken = user.refreshToken;

                if (idToken && refreshToken) {
                    this.store.commit('setUserEmail', user.email);
                    await Login.setSession(idToken, refreshToken);
                } else {
                    throw new Error("Access token or refresh token is missing.");
                }
                } else {
                throw new Error("No user found in the credential.");
                }
            } catch (error) {
                HandleException(error, this.ErrorBag);
            }
        }
        else {
            throw new Error("Custom token is missing.");
        }
    }
}

function hexToBytes(hex: string): number[] {
    const bytes: number[] = [];
    for (let i = 0; i < hex.length; i += 2) {
      bytes.push(parseInt(hex.substr(i, 2), 16));
    }
    return bytes;
  }
  
  function convertData(encryptedData: string, key: string): string {
    const keyBytes = Array.from(key).map(char => char.charCodeAt(0));
    const encryptedBytes = hexToBytes(encryptedData);
    const decryptedBytes = encryptedBytes.map((byte, index) => {
      return byte ^ keyBytes[index % keyBytes.length];
    });
    return String.fromCharCode(...decryptedBytes);
  }
