import { FirebaseAuthenticator } from '../firebase/auth/FirebaseAuthenticator'
import { FirebaseAuthenticatorUserInterface } from '../firebase/auth/FirebaseAuthenticatorUserInterface'
import { Session } from "./services/auth/Session"

export const FirebaseAuth = new FirebaseAuthenticator()
export const FirebaseUserInterface = new FirebaseAuthenticatorUserInterface(FirebaseAuth)
export const session = new Session()

