import validator from 'validator';
import { ALLOWED_AUTH_PROVIDER_DOMAINS } from '@/config/config';
import { LoginException } from '@/app-modules/auth/exceptions/LoginException';

export class LoginValidator {
    static validateEmail(email: string) {
        if (validator.isEmpty(email))
            throw new LoginException('emptyEmail')
        if (!validator.isEmail(email))
            throw new LoginException('invalidEmail')
    }

    static validatePassword(password: string) {
        if (validator.isEmpty(password))
            throw new LoginException('emptyPassword')
    }

    static validateAllowedDomains(email: string): boolean {
        const domain = email.split("@")[1]
        return ALLOWED_AUTH_PROVIDER_DOMAINS.includes(domain)
    }
}
