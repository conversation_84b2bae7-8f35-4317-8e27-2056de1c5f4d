import { en } from './en'
import { size } from 'lodash'

export function __(key: string): string {
    try {
        if(typeof en[key] == 'string' && size(en[key]) > 0)
            return en[key]
        return key
    } catch {
        return key
    }
}

export function getMessageAndReplace(key: string, strToReplace: string, replacement: string ): string {
    try {
        return __(key).replace(strToReplace, replacement)
    } catch(err) {
        console.error(err)
        return key
    }
}