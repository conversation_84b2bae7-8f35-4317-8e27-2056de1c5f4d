import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper';
import { ErrorBag } from '@/app-modules/message-bag/entities/ErrorBag';
import { firebaseMessages } from '@/app-modules/firebase/auth/messages';

const transformTextToKebabCase = (text: string): string => {
    return text.replace(/([a-z])([A-Z])/g, '$1-$2')
}

const getKeyFromMessage = (keys: any, message: string): string => {
    return keys.find((key: string) => message.toLowerCase().includes(transformTextToKebabCase(key).toLowerCase()))
}

const isFirebaseError = (message: string): boolean => {
    return message.toLowerCase().includes('firebase')
}

export function translateFirebaseResponse(message: string): any {
    const keys = Object.keys(firebaseMessages);
    const matchingKey = getKeyFromMessage(keys, message);
    return matchingKey ? firebaseMessages[matchingKey] : null;
}

export function HandleException(exception: any, ErrorBag: ErrorBag) {
    debug(exception)

    let exc = exception.message

    addExcToNamedErrorBag(exc, ErrorBag)
}

export function addExcToNamedErrorBag(exc: any, ErrorBag: ErrorBag) {

    if (exc.toLowerCase().includes('email') && !exc.toLowerCase().includes('password'))
        ErrorBag.setMessageToNamedBag('email', exc)
    else if (exc.toLowerCase().includes('password') && !exc.toLowerCase().includes('email'))
        ErrorBag.setMessageToNamedBag('password', isFirebaseError(exc) ? translateFirebaseResponse(exc) : exc)
    else
        ErrorBag.setMessageToNamedBag('generic', exc)

}

export function debug(data: any, type = 'e') {
    if (!EnvironmentHelper.isProductionEnvironment()) {
        switch (type) {
            case 'l':
                console.log(data)
                break;
            case 'w':
                console.warn(data)
                break;
            default:
                console.error(data)
                break;
        }
    }
}
