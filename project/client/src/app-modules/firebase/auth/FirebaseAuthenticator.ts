import { firebaseConfig } from "./config"
import firebase from "firebase/compat/app"
import { size } from 'lodash'
import { FirebaseAuthenticatorException } from "../exceptions/FirebaseAuthenticatorException"
import { debug } from "../../auth/helpers/ExceptionHandler"

const FIREBASE_EMAIL_LOGIN_METHOD_KEY = 'password'
const FIREBASE_FB_LOGIN_METHOD_KEY = 'facebook.com'
const FIREBASE_GOOGLE_LOGIN_METHOD_KEY = 'google.com'
const FIREBASE_ERROR_NO_SIGN_IN_METHODS = 'Sorry, we can\'t find an account with this email address. Please try again.'

export class FirebaseAuthenticator {
    private _app: any
    protected _auth: any

    constructor() {
        try {
            this._app = firebase.initializeApp(firebaseConfig)
            this._auth = this._app.auth()
        } catch(error) {
            throw new FirebaseAuthenticatorException(error)
        }
    }

    getAuth(): any {
        return this._auth
    }

    getProviderByKey(key: string): any {
        if (key.includes(FIREBASE_GOOGLE_LOGIN_METHOD_KEY)) {
            return new firebase.auth.GoogleAuthProvider()
        }
        else if (key.includes(FIREBASE_FB_LOGIN_METHOD_KEY))
        {
            return new firebase.auth.FacebookAuthProvider()
        }
        else if (key.includes(FIREBASE_EMAIL_LOGIN_METHOD_KEY))
        {
            return new firebase.auth.EmailAuthProvider()
        }
        else
        {
            throw new FirebaseAuthenticatorException("No authentication providers correspond with the given key.")
        }
    }

    async getIdToken() {
        return await this._auth.currentUser.getIdToken(false)
            .then((idToken: any) => {
                return idToken
            })
            .catch((error: any) => {
                throw new FirebaseAuthenticatorException(error)
            })
    }

    async signInWithCustomToken(customToken: string): Promise<firebase.auth.UserCredential> {
        if (this.isTokenExpired(customToken)) {
            throw new FirebaseAuthenticatorException('Custom Token is expired.');
          } else {
            return await firebase.auth().signInWithCustomToken(customToken);
          }
        
    }

    getUID() {
        return this._auth.currentUser.uid
    }

    hasEmailAndPasswordProvider(signInMethods: any): boolean {
        return signInMethods.includes(FIREBASE_EMAIL_LOGIN_METHOD_KEY)
    }

    hasFacebookProvider(signInMethods: any): boolean {
        return signInMethods.includes(FIREBASE_FB_LOGIN_METHOD_KEY)
    }

    hasGoogleProvider(signInMethods: any): boolean {
        return signInMethods.includes(FIREBASE_GOOGLE_LOGIN_METHOD_KEY)
    }

    hasQuickLoginProvider(signInMethods: any): boolean {
        return this.hasGoogleProvider(signInMethods) || this.hasFacebookProvider(signInMethods)
    }

    async fetchSignInMethods(email: string): Promise<any> {
        const signInMethods = await this._auth.fetchSignInMethodsForEmail(email)
        if(size(signInMethods) > 0)
            return signInMethods
        throw new FirebaseAuthenticatorException(FIREBASE_ERROR_NO_SIGN_IN_METHODS)
    }

    async signInWithEmail(email: string, password: string): Promise<any> {
        return await this._auth.signInWithEmailAndPassword(email, password)
        .then((userCredential: any) => {
            return userCredential.user
        })
        .catch((error: any) => {
            throw new FirebaseAuthenticatorException(error)
        });
    }

    async createUserWithEmailAndPassword(email: string, password: string): Promise<any> {
        return await this._auth.createUserWithEmailAndPassword(email, password)
        .then((userCredential: any) => {
            return userCredential
        })
        .catch((error: any) => {
            throw new FirebaseAuthenticatorException(error)
        });
    }

    static async linkWithCredential(auth: any, credentials: any): Promise<any> {
        try {
            return auth.currentUser.linkWithCredential(credentials)
                .then(() => {
                    return true;
                }).catch((error: any) => {
                    console.error("Account linking error", error)
                    throw new FirebaseAuthenticatorException(error)
                });
        } catch(error) {
            throw new FirebaseAuthenticatorException(error)
        }
    }

    async linkEmailAndPassword(email: string, password: string): Promise<any> {
        try {
            const credentials = firebase.auth.EmailAuthProvider.credential(email, password)
            const auth = this._auth
            firebase.auth().onAuthStateChanged(function(user: any) {
                if (user) {
                    FirebaseAuthenticator.linkWithCredential(auth, credentials)
                } 
            });
        } catch(error) {
            debug(error)
            throw new FirebaseAuthenticatorException(error)
        }
    }

    async signOut(): Promise<boolean> {
        try {
            await this._auth.signOut()
                .then(function () {
                    return true
                }, function (error: any) {
                    throw new FirebaseAuthenticatorException(error)
                });
            this.destroy()
            return true
        } catch(error) {
            debug(error)
            this.destroy()
            return false
        }
    }

    destroy() {
        try {
            this._app = null;
            this._auth = null;
        } catch(error) {
            debug(error)
        }
    }

    base64UrlDecode(base64Url: string): string {
        const base64 = base64Url
            .replace(/-/g, '+')
            .replace(/_/g, '/');
    
        const jsonPayload = decodeURIComponent(
            atob(base64)
                .split('')
                .map((c) => {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                })
                .join('')
        );
    
        return jsonPayload;
    }
      
    decodeJwt(token: string): any {
        const parts = token.split('.');
        if (parts.length !== 3) {
            throw new Error('Invalid token');
        }
        const payload = parts[1];
        return JSON.parse(this.base64UrlDecode(payload));
    }
      
    isTokenExpired(token: string): boolean {
        try {
            const decoded = this.decodeJwt(token);
            const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
            return decoded.exp < currentTime;
        } catch (error) {
            console.error('Invalid token', error);
            return true;
        }
    }
}