import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper';
import { debug } from '@/app-modules/auth/helpers/ExceptionHandler';
import { FIREBASE_KEY } from '@/config/config';

function getFirebaseConfiguration() {
    try {
        if (EnvironmentHelper.isProductionEnvironment()) {
            return FIREBASE_KEY
        } else {
            debug('You\'re on a development environment, auth is configured to work in debug mode.', 'w')
            return FIREBASE_KEY
        }
    } catch (error) {
        debug(error)
    }
}

export const firebaseConfig = { ...getFirebaseConfiguration() }
