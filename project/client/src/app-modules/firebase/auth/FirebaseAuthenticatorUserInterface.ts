import { FirebaseAuthenticator } from "./FirebaseAuthenticator"
import * as firebaseUi from 'firebaseui'
import 'firebaseui/dist/firebaseui.css'
import firebase from "firebase/compat/app";

export class FirebaseAuthenticatorUserInterface {
    private _FirebaseAuthenticator: FirebaseAuthenticator
    public layout: any

    constructor(FirebaseAuthenticator: FirebaseAuthenticator) {
        this._FirebaseAuthenticator = FirebaseAuthenticator
        this.layout = new firebaseUi.auth.AuthUI(this._FirebaseAuthenticator.getAuth())
    }

    setupWithCallback (
        signInSuccessWithAuthResult: Function,
        uiShown: Function,
        signInSuccessUrl = '/',
        signInFlow = 'redirect'
    ) {
        return {
            signInOptions: [
                firebase.auth.GoogleAuthProvider.PROVIDER_ID
            ],
            callbacks: {
                signInSuccessWithAuthResult,
                uiShown
            },
            signInFlow: signInFlow,
            signInSuccessUrl: signInSuccessUrl
        };
    }
}