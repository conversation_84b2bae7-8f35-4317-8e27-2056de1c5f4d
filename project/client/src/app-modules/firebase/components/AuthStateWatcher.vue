<template>
</template>
<script lang="ts" setup>
import {onBeforeMount} from "vue"
import * as firebaseAuth from "firebase/auth";
import {FirebaseAuth} from "@/app-modules/auth/main";
import {AppState} from "@/app-modules/app-state/AppState";
import {getCurrentPage, getLastRequestedPage} from "@/app-modules/helpers/navigation";
import {LoginValidator} from "@/app-modules/auth/validators/LoginValidator";
import {debug} from "@/app-modules/helpers/debug";
import {Login} from "@/app-modules/auth/services/auth/Login";
import {useRouter} from "vue-router";
import {useStore} from "vuex";
import { isUserDeveloper } from "@/app-modules/helpers/IsDeveloper";
import { DASHBOARD_ROUTE_PATH, LOGIN_ROUTE_PATH, LOGOUT_ROUTE_PATH } from "@/config/config";

const router = useRouter()
const store = useStore()

function isCurrentPageLastRequestedPage() {
  return getCurrentPage() === getLastRequestedPage()
}

function isCurrentPageLogin() {
  return getCurrentPage() === LOGIN_ROUTE_PATH
}

async function checkRoute() {
  const lastRequestedPage = getLastRequestedPage()

  if (lastRequestedPage &&
      (!isCurrentPageLastRequestedPage()) &&
      (!isCurrentPageLogin())
  ) {
    new AppState({
      ready: true,
      busy: false,
      lastRequestedPage: ``
    })
    await router.push(lastRequestedPage)
  } else if (isCurrentPageLogin()) {
    new AppState({
      ready: true,
      busy: false,
      lastRequestedPage: DASHBOARD_ROUTE_PATH
    })
    await router.push(DASHBOARD_ROUTE_PATH)
  }
}

async function setUser(user: any) {
  try {
    if (getCurrentPage() !== LOGOUT_ROUTE_PATH) {
      await Login.setSession(user.accessToken,user.refreshToken)
      store.commit('setUid', user.uid)
      let isDeveloper = await isUserDeveloper(user.uid)
      store.commit('setIsDeveloper', isDeveloper)
      if (!isDeveloper) {
        store.commit('setTestMode', false)
      }
    }

    new AppState({
      authenticated: LoginValidator.validateAllowedDomains(user.email),
      lastVerified: Date.now()
    })

    await checkRoute()

  } catch(e) {
    debug(e)
  }
}

onBeforeMount(async () => {
  firebaseAuth.onAuthStateChanged(
      FirebaseAuth.getAuth(),
      async (user: any) => {
        if(user) {
          await setUser(user)
        } else {
          new AppState({
            authenticated: false,
            lastRequestedPage: ``,
            lastVerified: Date.now()
          })
        }
      }
  )
})
</script>
