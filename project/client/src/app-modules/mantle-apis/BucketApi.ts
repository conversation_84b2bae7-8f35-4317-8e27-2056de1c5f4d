import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { debug } from '@/app-modules/helpers/debug';
import { MANTLE_API_BUCKET_AUTH_TOKEN } from '@/config/config';
import { MANTLE_API_BUCKET_URL } from '@/config/config';

export default class BucketApi {
    private axiosInstance: AxiosInstance

    constructor() {
        this.axiosInstance = axios.create({
            baseURL: MANTLE_API_BUCKET_URL,
            headers: {
                'Authorization': `Basic ${MANTLE_API_BUCKET_AUTH_TOKEN}`
            },
        })
    }

    public async deleteBatch(batchId: string): Promise<boolean> {
        try {
            return this.axiosInstance.delete(`/delete-batch/${batchId}`)
                .then((response) => {
                    const responseData = response.data
                    return responseData.result
                }).catch((error) => {
                    debug(error)
                    return false
                })
        } catch (error) {
            debug(error)
            return false
        }
    }

    public async deletePostItem(pdfFileName: string): Promise<boolean> {
        try {
            return this.axiosInstance.delete(`/delete/${pdfFileName}`)
                .then((response) => {
                    const responseData = response.data
                    return responseData.result
                }).catch((error) => {
                    debug(error)
                    return false
                })
        } catch (error) {
            debug(error)
            return false
        }
    }

    public async uploadPostItem(formData: FormData, timeout: number): Promise<boolean | AxiosResponse> {
        try {
            return this.axiosInstance.post(
                '/upload',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    timeout: timeout
                })
        } catch (error) {
            debug(error)
            return false
        }
    }
}
