import axios, { AxiosInstance } from 'axios';
import { debug } from '@/app-modules/helpers/debug';
import { MANTLE_API_MATCH_AUTH_TOKEN } from '@/config/config';
import { MANTLE_API_MATCH_URL } from '@/config/config';

export default class MatchApi {
    private axiosInstance: AxiosInstance

    constructor() {
        this.axiosInstance = axios.create({
            baseURL: MANTLE_API_MATCH_URL,
            headers: {
                'Authorization': `Basic ${MANTLE_API_MATCH_AUTH_TOKEN}`
            },
        })
    }

    public async publish(formData: FormData): Promise<any> {
        try {
            const response = await this.axiosInstance.post('/publish', formData)
            return response.data
        } catch (error) {
            debug(error)
            return { success: false, error: error }
        }
    }
}
