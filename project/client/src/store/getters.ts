export default {
    appState(state: any) {
        return state.appState;
    },
    acceptUploads(state: any) {
        return state.appState.acceptUploads
    },
    mutated(state: any) {
        return state.mutated;
    },
    trackedFiles(state: any) {
        return state.trackedFiles;
    },
    batches(state: any) {
        return state.batches;
    },
    trackedFilesLogs(state: any) {
        return state.trackedFilesLogs
    },
    preparingToUpload(state: any) {
        return state.appState.preparingToUpload
    },
    selectedFile(state: any) {
        return state.selectedFile
    },
    statusMessages(state: any) {
        return state.statusMessages
    },
    searchResultsData(state: any) {
        return state.searchResultsData
    },
    searchQueryParameter(state: any) {
        return state.searchQueryParameter
    },
    stats(state: any) {
        return state.stats
    },
    autoMatcher(state: any) {
        return state.autoMatcher
    },
    toAck(state: any) {
        return state.toAck
    },
    search(state: any) {
        return state.search
    },
}
