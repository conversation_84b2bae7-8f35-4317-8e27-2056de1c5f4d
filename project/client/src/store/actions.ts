import {store} from "@/store";

export default {
    async getAppState(): Promise<any> {
        try {
            return store.state.appState
        } catch (e) {
            return {}
        }
    },
    async getTrackedFiles(): Promise<any> {
        try {
            return store.state.trackedFiles
        } catch (e) {
            return {}
        }
    },
    async getBatches(): Promise<any> {
        try {
            return store.state.batches
        } catch (e) {
            return {}
        }
    },
    async getLogs(): Promise<any> {
        try {
            return store.state.trackedFilesLogs
        } catch (e) {
            return {}
        }
    },
    async getStats(): Promise<any> {
        try {
            return store.state.stats
        } catch (e) {
            return {}
        }
    }
}