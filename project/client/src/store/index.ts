import actions from '@/store/actions';
import createPersistedState from 'vuex-persistedstate';
import EnvironmentHelper from '@/app-modules/helpers/EnvironmentHelper';
import getters from '@/store/getters';
import mutations from '@/store/mutations';
import throttle from 'lodash/throttle';
import { createStore } from 'vuex';

export const store = createStore({
  plugins: [
    createPersistedState({
      setState: throttle((key, state, storage) => {
        storage.setItem(key, JSON.stringify(state))
      }, 500),
    }),
  ],
  state: {
    appState: {} as any,
    mutated: Date.now(),
    uid: '' as string,
    userEmail: '' as string,
    batches: {} as any,
    trackedFiles: {} as any,
    trackedFilesLogs: {} as any,
    selectedFile: {} as any,
    statusMessages: {} as any,
    searchResultsData: {} as any,
    searchQueryParameter: `` as string,
    stats: {} as any,
    isDeveloper: false as boolean,
    testMode: !EnvironmentHelper.isProductionEnvironment() as boolean,
    testModeBatchNoticeDismissed: false,
    autoMatcher: {} as any,
    toAck: [] as any,
    search: {
      companyName: ``,
      officerName: ``,
      companyNumber: ``,
    }
  },
  getters,
  mutations,
  actions,
  modules: {
  }
})
