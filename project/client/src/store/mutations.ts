import {AppState} from "@/app-modules/app-state/AppState";
import {PostItem} from "@/app-modules/post-items/entities/PostItem";
import {debug} from "@/app-modules/helpers/debug";
import {store} from "@/store";
import {Status} from "@/app-modules/post-items/entities/Status";
import {Batch} from "@/app-modules/batch/entities/Batch";
import {PostItemLogEntry} from "@/app-modules/app-state/entities/PostItemLogEntry";
import {Stats} from "@/app-modules/app-state/entities/Stats";

export default {
    setAppState(state: any, appStateData: AppState) {
        state.appState = appStateData
        state.mutated = Date.now()
    },
    setUid(state: any, uid: String) {
        state.uid = uid
        state.mutated = Date.now()
    },
    setUserEmail(state: any, email: string) {
        state.userEmail = email
        state.mutated = Date.now()
    },
    setIsDeveloper(state: any, isDeveloper: boolean) {
        state.isDeveloper = isDeveloper
        state.mutated = Date.now()
    },
    setBatch(state: any, batch: Batch) {
        state.batches[batch.batchId] = batch
        state.mutated = Date.now()
    },
    deleteBatch(state: any, batch: Batch) {
        try {
            if(state.batches[batch.batchId]) {
                delete state.batches[batch.batchId]
            }
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    resetBatches(state: any) {
        debug('Purging batches','w')
        state.batches = {}
        state.mutated = Date.now()
    },
    updateMessagesToAck(state: any, toAck: Status) {
        state.toAck = toAck
        state.mutated = Date.now()
    },
    persistStatusMessage(state: any, {data}) {
        try {

            if(!state.statusMessages[data.pdf_filename]) {
                state.statusMessages[data.pdf_filename] = []
            }
            // due to storage limitations on VUEX we must exclude the textPayload and other items from the status message data
            // if(data.textPayload) {
            //     delete data.textPayload // not used
            // }
            if(data.operator) {
                delete data.operator // already present on postItem
            }
            if(data.dtc) {
                delete data.dtc // already present on postItem
            }

            state.statusMessages[data.pdf_filename].push(data)
            state.mutated = Date.now()
        } catch (e) {
            debug(e)
        }
    },
    resetStatusMessages(state: any) {
        debug('Purging status messages','w')
        state.statusMessages = {}
        state.mutated = Date.now()
    },
    deleteStatusMessage(state: any, statusMessage: Status) {
        try {
            if(state.statusMessages[statusMessage.ack_id]) {
                delete state.statusMessages[statusMessage.ack_id]
            }
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    incrementUpdatingStatusInstances(state: any) {
        state.appState.updatingStatusInstances++
    },
    decrementUpdatingStatusInstances(state: any) {
        state.appState.updatingStatusInstances--
    },
    updateStats(state: any, stats: Stats) {
        state.stats = stats
        state.mutated = Date.now()
    },
    resetStats(state: any) {
        debug('Purging stats','w')
        state.stats = {}
        state.mutated = Date.now()
    },
    updateTrackedFile(state: any, postItemData: PostItem) {
        try {
            if(state.trackedFiles[postItemData.pdf_filename] || !postItemData.protectAckId) {
                state.trackedFiles[postItemData.pdf_filename] = postItemData
            }
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    updateTrackedFileLogs(state: any, loggerData: PostItemLogEntry) {
        try {
            state.trackedFilesLogs[loggerData.pdf_filename] = loggerData
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    resetTrackedFileLogs(state: any) {
        debug('Purging logs','w')
        state.trackedFilesLogs = {}
        state.mutated = Date.now()
    },
    resetTrackedFiles(state: any) {
        debug('Purging tracked files and batches','w')
        state.batches = {}
        state.trackedFiles = {}
        state.mutated = Date.now()
    },
    updateSearchResultsData(state: any, searchResultsData: any) {
        try {
            let pdf_filename = store.state.selectedFile.pdf_filename;
            state.searchResultsData[pdf_filename] = searchResultsData
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    updateSearchQueryParameter(state: any, searchQueryParameter: any) {
        try {
            state.searchQueryParameter = searchQueryParameter
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    resetSearch(state: any) {
        state.search.companyName = ``
        state.search.officerName = ``
        state.search.companyNumber = ``
        state.searchResultsData = {}
        state.searchQueryParameter = ``
    },
    deleteTrackedFile(state: any, postItemData: PostItem) {
        try {
            if(state.trackedFiles[postItemData.pdf_filename]) {
                delete state.trackedFiles[postItemData.pdf_filename]
            }
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    setSelectedFile(state: any, file: PostItem) {
        try {
            state.selectedFile = file
        } catch(e) {
            debug(e)
        }
        state.mutated = Date.now()
    },
    updateSearchParameters(state: any, {
        companyName = null,
        officerName = null,
        companyNumber = null,
    }) {
        try {
            state.search.companyName = (companyName !== null ? companyName : store.state.search.companyName)
            state.search.officerName = (officerName !== null ? officerName : store.state.search.officerName)
            state.search.companyNumber = (companyNumber !== null ? companyNumber : store.state.search.companyNumber)
        } catch(e) {
            debug(e)
        }
    },
    updateAutoMatcher(state: any, {autoMatcherHistory}: any) {
        try {
            state.autoMatcher = autoMatcherHistory
        } catch(e) {
            debug(e)
        }
    },
    resetAutoMatcher(state: any) {
        try {
            state.autoMatcher = []
        } catch(e) {
            debug(e)
        }
    },
    setRedirectAlert(state: any, message: string) {
        state.redAlert = message
        state.mutated = Date.now()
    },
    setTestMode(state: any, testMode: boolean) {
        state.testMode = testMode
        state.mutated = Date.now()
    },
    setTestModeBatchNoticeDismissed(state: any, testModeBatchNoticeDismissed: boolean) {
        state.testModeBatchNoticeDismissed = testModeBatchNoticeDismissed
        state.mutated = Date.now()
    }
}