import { createApp, computed } from 'vue'
import App from './App.vue'
import '@/registerServiceWorker'
import router from './router'
import { store} from "@/store"
import 'bootstrap'
import "./assets/css/main.scss"
import Toaster from '@meforma/vue-toaster'
import { Tooltip } from 'bootstrap'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import DirectPagesHeader from '@/components/DirectPagesHeader.vue'
import DirectPageFooter from '@/components/Footer.vue'
import * as Sentry from '@sentry/vue'
import { SENTRY_DSN } from "@/config/config";

const testMode = computed(() => store.state.testMode)

export const app = createApp(App)
if (document.getElementById('app')) {
    app.use(store)
    app.use(router)
    app.use(Toaster).provide('toast', app.config.globalProperties.$toast)
    app.component('font-awesome-icon', FontAwesomeIcon)
    app.mount('#app')
}

Sentry.init({
    app,
    dsn: SENTRY_DSN,
    integrations: [
        Sentry.browserTracingIntegration({
            router
        }),
        Sentry.replayIntegration(),
    ],
    tracesSampleRate: 1.0,
    tracePropagationTargets: [/^https:\/\/mailroom\.msg\.cool/, /^https:\/\/mailroom\.msg\.cool\/api/],
    replaysSessionSampleRate: 1.0,
    replaysOnErrorSampleRate: 1.0,
});

app.config.warnHandler = (msg, instance, trace) => {
    Sentry.captureMessage(`Vue warning: ${msg}`, {
        level: 'warning',
        extra: {
            trace,
        }
    });
};

export const appHeader = createApp(DirectPagesHeader, { title: 'Post Items' })
if (document.getElementById('header')) {
    appHeader.use(store)
    appHeader.component('font-awesome-icon', FontAwesomeIcon)
    appHeader.mount('#header')
}

export const appFooter = createApp(DirectPageFooter)
if (document.getElementById('footer')) {
    appFooter.mount('#footer')
}

export function setupTestElements(): void {
    setTestModeOnlyElementsVisibility(testMode.value)
    setTestModeOffElementsVisibility(!testMode.value)
}

/**
 * Displays and enables all elements with the test-mode class on the entire page.
 * If a parameter is passed as false, it instead hides and disable all elements.
 * @param display - Whether to display/enable or hide/disable the related elements.
 */
function setTestModeOnlyElementsVisibility(display = true) {
    const testModeOnlyElements = document.querySelectorAll('.test-mode')
    testModeOnlyElements.forEach((element) => {
        if (display) {
            element.removeAttribute('hidden')
            element.removeAttribute('disabled')
        } else {
            element.setAttribute('hidden', 'hidden')
            element.setAttribute('disabled', 'disabled')
        }
    });
}

/**
 * Displays and enables all elements with the test-mode-off class on the entire page.
 * If a parameter is passed as false, it instead hides and disable all elements.
 * @param display - Whether to display/enable or hide/disable the related elements.
 */
function setTestModeOffElementsVisibility(display = true) {
    const testModeOffElements = document.querySelectorAll('.test-mode-off')
    testModeOffElements.forEach((element) => {
        if (display) {
            element.removeAttribute('hidden')
            element.removeAttribute('disabled')
        } else {
            element.setAttribute('hidden', 'hidden')
            element.setAttribute('disabled', 'disabled')
        }
    });
}

new Tooltip(document.body, {
    selector: "[data-bs-toggle='tooltip']",
    trigger : 'hover'
})
