<template>
  <div>
    <Header title="Login"></Header>
    <div class="container mt-4">
      <div class="card border-0">
        <div class="card-body">
            <UnAuth>
              <div v-if="redAlert" class="d-flex justify-content-center">
                <div class="alert alert-danger mb-auto" role="alert">{{ redAlert }}</div>
              </div>
              <main class="d-flex flex-column align-items-center justify-content-center">
                <LoginForm></LoginForm>
                <QuickLogin></QuickLogin>
              </main>
            </UnAuth>
            <Auth>
              <main class="d-flex flex-column align-items-center justify-content-center">
                <loader />
                Signed you in. Redirecting...
              </main>
            </Auth>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Auth from "@/app-modules/auth/components/Auth.vue";
import UnAuth from "@/app-modules/auth/components/UnAuth.vue";
import LoginForm from "@/app-modules/auth/components/LoginForm.vue";
import QuickLogin from "@/app-modules/auth/components/QuickLogin.vue";
import Loader from "@/components/Loader.vue";
import Header from "@/components/Header.vue";
import {AppState} from "@/app-modules/app-state/AppState";
import {onBeforeMount, ref, watch} from "vue";
import {useStore} from "vuex";
import { LOGIN_ROUTE_PATH } from "@/config/config";

const store = useStore()

let ready = ref(false),
    busy = ref(false),
    redAlert = ref('')

onBeforeMount(async () => {
  new AppState({
    ready: false,
    busy: true,
    lastRequestedPage: LOGIN_ROUTE_PATH
  })
  try {
    redAlert.value = store.state.redAlert;
    store.commit('setRedirectAlert', ``);
  } catch (e) {
    console.log(e);
  }
})

watch(() => store.getters.mutated,  async () => {
  ready.value = store.state.appState.ready
  busy.value = store.state.appState.busy
})
</script>
