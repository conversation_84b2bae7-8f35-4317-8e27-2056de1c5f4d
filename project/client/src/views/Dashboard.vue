<template>
  <div>
    <DashboardHeader />
    <div class="py-2">
      <div class="container-fluid container-xl">
        <main id="dash-batches-wrapper" v-if="hasBatches() && !isPreparingToUpload()">
          <TestModeBatchNotice />
          <div v-for="batch in batches" class="accordion mb-1" :id="`accordion-${batch.batchId}`" >
            <div class="accordion-item" >
              <div class="accordion-header" :id="`heading-${batch.batchId}`">
                <div class="container-flex">
                  <div class="row justify-content-between">
                    <div class="col align-items-center d-inline-flex" style="max-width: max-content; min-width: max-content;">
                      <div class="text-success fw-light fst-italic m-1 align-items-center gap-1 ps-2 d-inline-flex" v-if="batch.isTest && !EnvironmentHelper.isProductionEnvironment()">
                        <div class="bg-success" style="width: 6px; height: 6px; border-radius: 3px;" />
                        <span style="min-width: max-content;">TEST</span>
                      </div>
                      <div class="text-danger fw-light fst-italic m-1 align-items-center gap-1 ps-2 d-inline-flex" v-if="!batch.isTest && !EnvironmentHelper.isProductionEnvironment()">
                        <div class="bg-danger" style="width: 6px; height: 6px; border-radius: 3px;" />
                        <span style="min-width: max-content;">NOT A TEST</span>
                      </div>
                      <small class="text-muted m-1">BATCH</small>
                      <small class="rounded-pill pill bg-transparent border-muted border border-1 p-1 text-center" style="min-width: 7rem;">
                        <b id="batch-id" class="text-dark">{{ batch.batchId }}</b>
                      </small>
                    </div>
                    <div class="col align-items-center d-inline-flex">
                      <button id="collapse-button" class="accordion-button collapsed d-flex justify-content-end"
                            type="button"
                            data-bs-toggle="collapse"
                            :data-bs-target="`#collapse-${batch.batchId}`"
                            aria-expanded="false"
                            :aria-controls="`#collapse-${batch.batchId}`">
                        <span class="me-auto" style="min-width: 13rem;">
                          <small>
                            <small :class="getBatchInfoSelectorClass(batch.batchId,'PROCESSING')">
                              &nbsp; <font-awesome-icon icon="fa-solid fa-hourglass-half" />
                              <span v-if="render"> &nbsp; {{ getBatchInfoProcessing(batch.batchId) }} </span>
                            </small>
                          </small>
                          <small>
                            <small :class="getBatchInfoSelectorClass(batch.batchId,'COMPANY_FOUND')">
                              &nbsp; <font-awesome-icon icon="fa-solid fa-building" />
                              <span v-if="render"> &nbsp; {{ getBatchInfoCompanyFound(batch.batchId) }} </span>
                            </small>
                          </small>
                          <small>
                            <small :class="getBatchInfoSelectorClass(batch.batchId,'DONE')">
                              &nbsp; <font-awesome-icon icon="fa-solid fa-check" />
                              <span v-if="render"> &nbsp; {{ getBatchInfoDone(batch.batchId) }} </span>
                            </small>
                          </small>
                          <small>
                            <small :class="getBatchInfoSelectorClass(batch.batchId,'ERROR')">
                              &nbsp; <font-awesome-icon icon="fa-solid fa-triangle-exclamation" />
                              <span v-if="render"> &nbsp; {{ getBatchInfoError(batch.batchId) }} </span>
                            </small>
                          </small>
                        </span>
                        <div v-if="debugMode" class="progress flex-fill mx-4" style="height: 0.5rem; max-width: 20rem;">
                          <div
                            v-if="!!getBatchInfoProcessing(batch.batchId)"
                            class="progress-bar bg-primary"
                            role="progressbar"
                            :style="{width: `${((getBatchStatsSize(batch.batchId) - getBatchInfoProcessing(batch.batchId)) / getBatchStatsSize(batch.batchId)) * 100}%`}"
                            aria-valuemin="0"
                            aria-valuemax="100">
                          </div>
                          <div
                            v-if="!getBatchInfoProcessing(batch.batchId)"
                            class="progress-bar bg-info"
                            role="progressbar"
                            :style="{width: `${(getBatchInfoDone(batch.batchId) / getBatchStatsSize(batch.batchId)) * 100}%`}"
                            aria-valuemin="0"
                            aria-valuemax="100">
                          </div>
                          <div
                            v-if="!getBatchInfoProcessing(batch.batchId)"
                            class="progress-bar bg-success"
                            role="progressbar"
                            :style="{width: `${(getBatchInfoCompanyFound(batch.batchId) / getBatchStatsSize(batch.batchId)) * 100}%`}"
                            aria-valuemin="0"
                            aria-valuemax="100">
                          </div>
                          <div
                            v-if="!getBatchInfoProcessing(batch.batchId)"
                            class="progress-bar bg-danger"
                            role="progressbar"
                            :style="{width: `${(getBatchInfoError(batch.batchId) / getBatchStatsSize(batch.batchId)) * 100}%`}"
                            aria-valuemin="0"
                            aria-valuemax="100">
                          </div>
                        </div>
                        <span v-if="debugMode" class="text-end pe-3" style="min-width: 4.5rem;">
                          {{ getBatchElapsedTime(batch) }}
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div :id="`collapse-${batch.batchId}`"
                   class="accordion-collapse collapse"
                   :aria-labelledby="`#heading-${batch.batchId}`"
                   :data-bs-parent="`#accordion-${batch.batchId}`">
                <div class="accordion-body p-0">
                  <div v-if="batch.batchId">
                    <!-- BATCH ACTIONS -->
                    <nav class="navbar navbar-light bg-light p-1">
                      <form class="form-inline pt-1 pb-1">
                        <button
                            v-if="canDeleteBatch(batch.batchId)"
                            class="btn btn-sm btn-outline-danger"
                            type="button"
                            data-bs-toggle="modal"
                            :data-bs-target="`#deleteAllInBatchModal-${batch.batchId}`"
                        >
                          <font-awesome-icon icon="fa-solid fa-trash" />
                          Delete Entire Batch
                        </button>
                      </form>
                      <div v-if="canDeleteBatch(batch.batchId)">
                        <deleteAllInBatchModal :batchId="batch.batchId" />
                      </div>
                    </nav>
                    <!-- BATCH ITEMS TABLE -->
                    <PostItemsTable :batchId="batch.batchId"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
        <main id="dash-uploader-wrapper" v-else-if="canUpload() && !isPreparingToUpload()" class="d-flex flex-column align-items-center justify-content-center">
          <div>
            <Uploader />
          </div>
        </main>
        <main v-else-if="hasOrphanFiles()" class="d-flex flex-column align-items-center justify-content-center">
          <div class="card">
            <div class="card-header">
              <div class="card-title">
                The following files have been found but are not on any batch currently being tracked:
              </div>
            </div>
            <div class="card-body">
              <PostItemsTable :batchId="`%`"/>
            </div>
          </div>
        </main>
        <main v-else class="d-flex flex-column align-items-center justify-content-center">
          <div v-if="displayResetButton">
            <button class="btn btn-danger" @click="resetAppState()">Reset App State</button>
          </div>
          <div>
            <Loader />
          </div>
        </main>
        <div>
          <AddBatchFloater />
          <AddBatchModal />
          <DeleteModal />
          <DebugItemModal />
          <CompanySearchModal />
          <PurgeModal />
          <OperationStatusModal />
        </div>
      </div>
    </div>
  </div>
</template>

<style v-if="!EnvironmentHelper.isProductionEnvironment()">
  .accordion-button::after {
    margin-left: inherit;
  }
</style>

<script lang="ts" setup>
import {computed, onBeforeMount, reactive, ref, watch} from "vue"
import {firstTimeSetup} from "@/app-modules/app-state/SetupApp";
import {AppState} from "@/app-modules/app-state/AppState";
import {size} from "lodash";
import {debug} from "@/app-modules/helpers/debug";
import {Batch} from "@/app-modules/batch/entities/Batch";
import DashboardHeader from "@/components/DashboardHeader.vue";
import Uploader from "@/app-modules/uploader/components/Uploader.vue"
import PostItemsTable from "@/app-modules/post-items/components/PostItemsTable.vue"
import Loader from "@/components/Loader.vue";
import AddBatchFloater from "@/app-modules/batch/components/AddBatchFloater.vue";
import OperationStatusModal from "@/app-modules/post-items/components/OperationStatusModal.vue";
import PurgeModal from "@/app-modules/post-items/components/PurgeModal.vue";
import DebugItemModal from "@/app-modules/post-items/components/DebugItemModal.vue";
import DeleteModal from "@/app-modules/post-items/components/DeleteModal.vue";
import CompanySearchModal from "@/app-modules/cms/components/CompanySearchModal.vue";
import AddBatchModal from "@/app-modules/batch/components/AddBatchModal.vue";
import DeleteAllInBatchModal from "@/app-modules/batch/components/DeleteAllInBatchModal.vue";
import TestModeBatchNotice from "@/components/TestModeBatchNotice.vue";
import {useStore} from "vuex";
import EnvironmentHelper from "@/app-modules/helpers/EnvironmentHelper";
import { PostItem } from "@/app-modules/post-items/entities/PostItem";

const store = useStore()

let render = ref(false),
    ready = ref(false),
    busy = ref(false),
    hasTrackedFiles = ref(false),
    batches = reactive(store.state.batches || {}),
    batchStats = reactive(store.state.stats.batchStats || {}),
    batchInfo = reactive({}),
    debugMode = computed(() => store.state.appState.debug),
    displayResetButton = ref(false)

function hasBatches() {
  return size(batches) > 0 && hasTrackedFiles.value && ready.value && !busy.value
}

function canUpload() {
  return !hasTrackedFiles.value && !hasBatches() && ready.value && !busy.value
}

function isPreparingToUpload() {
  return store.state.appState.preparingToUpload
}

function hasOrphanFiles() {
  return hasTrackedFiles.value && !hasBatches() && ready.value && !busy.value
}

function updateBatchInfo(){
  try {
    if(batchStats) {
      Object.keys(batchStats).forEach(batchId => {
        batchInfo[batchId] = {}
        buildBatchInfoForBatch(batchId)
        countEachStatusTypePerBatch(batchId)
      })
    }
  } catch (e) {
    debug(e)
  }
}

function buildBatchInfoForBatch(batchId) {
  batchInfo[batchId]["ERROR"] = 0
  batchInfo[batchId]["DONE"] = 0
  batchInfo[batchId]["COMPANY_FOUND"] = 0
  batchInfo[batchId]["PROCESSING"] = 0
}

function getBatchStatsSize(batchId) {
  return size(batchStats[batchId])
}

function getBatchElapsedTime(batch){
  if(!batch.dtc) {
    return "No recorded start time."
  }

  const elapsed = Math.floor(((batch.dte || new Date().getTime()) - batch.dtc) / 1000)
  const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0')
  const seconds = (elapsed % 60).toString().padStart(2, '0')
  return `${minutes}:${seconds}`
}

function getBatchStats(batchId) {
  return batchStats[batchId]
}

function getBatchInfo(batchId) {
  return batchInfo[batchId]
}

function getBatchInfoProcessing(batchId) {
  try {
    if(batchInfo[batchId]) {
      return batchInfo[batchId]["PROCESSING"] || 0
    }
  } catch (e) {
    debug(e)
  }
}

function getBatchInfoCompanyFound(batchId) {
  try {
    if(batchInfo[batchId]) {
      return batchInfo[batchId]["COMPANY_FOUND"] || 0
    }
  } catch (e) {
    debug(e)
  }
}

function getBatchInfoDone(batchId) {
  try {
    if(batchInfo[batchId]) {
      return batchInfo[batchId]["DONE"] || 0
    }
  } catch (e) {
    debug(e)
  }
}

function getBatchInfoError(batchId) {
  try {
    if(batchInfo[batchId]) {
      return batchInfo[batchId]["ERROR"] || 0
    }
  } catch (e) {
    debug(e)
  }
}

function countEachStatusTypePerBatch(batchId) {
  render.value = false
  Object.keys(batchStats[batchId]).forEach(batchItem => {
    let item = batchStats[batchId][batchItem]
    if(item) {
      if(item.includes("ERROR") || item.includes("FAILED")) {
        batchInfo[batchId]["ERROR"] += 1
      } else if(item.includes("COMPLETED_SUCCESS")) {
        batchInfo[batchId]["DONE"] += 1
      } else if(item.includes("FIND_COMPANY_SUCCESS")) {
        batchInfo[batchId]["COMPANY_FOUND"] += 1
      } else {
        batchInfo[batchId]["PROCESSING"] += 1
      }
    }
  })
  render.value = true
}

function getBatchInfoSelectorClass(batchId, type) {
  switch (type) {
    case "PROCESSING":
      return getBatchInfoProcessing(batchId) > 0 ? "text-primary" : "text-muted"
    case "COMPANY_FOUND":
      return getBatchInfoCompanyFound(batchId) > 0 ? "text-success" : "text-muted"
    case "DONE":
      return getBatchInfoDone(batchId) > 0 ? "text-info" : "text-muted"
    case "ERROR":
      return getBatchInfoError(batchId) > 0 ? "text-danger" : "text-muted"
    default:
      return "text-muted"
  }
}

function canDeleteBatch(batchId) {
  return !Batch.hasLockedItemsInBatch(batchId)
}

function canDismissALlInBatch(batchId) {
  return Batch.hasDismissibleItemsInBatch(batchId)
}

watch(() => store.getters.mutated,  async () => {
  ready.value = store.state.appState.ready
  busy.value = store.state.appState.busy
  hasTrackedFiles.value = await AppState.hasTrackedFiles()
})

watch(() => store.getters.batches,  async (value, oldValue) => {
  if (store.state.batches && value != oldValue) {
    batches = reactive(store.state.batches)
  }
}, {
  deep: true,
  immediate: true
})

watch(() => store.getters.stats,  async (value, oldValue) => {
  if(store.state.stats.batchStats && value != oldValue) {
    batchStats = reactive(store.state.stats.batchStats)
    updateBatchInfo()
  }
}, {
  deep: true,
  immediate: true
})

watch(() => store.state.appState && store.state.appState.uploading, async (value, oldValue) => {
  if (!value) {
    displayResetButton.value = false
    return
  }

  if (value != oldValue) {
    setTimeout(() => {
      displayResetButton.value = store.state.appState && store.state.appState.uploading
    }, 30000)
  }
}, {
  deep: true,
  immediate: true
})

setInterval(() => {
  updateBatchInfo()
  for (const batchId of Object.keys(batches)) {
    endBatchTimerIfNeeded(batchId)
  }
}, 1000)

function endBatchTimerIfNeeded(batchId) {
  if (!isDoneProcessing(batches[batchId]) && !getBatchInfoProcessing(batchId)) {
    setDoneProcessing(batches[batchId])
  }
}

function isDoneProcessing(batch): boolean {
  return !!batch.dte
}

function setDoneProcessing(batch) {
  batch.dte = new Date().getTime()
}

function resetAppState() {
  let confirmation = confirm('WARNING: This will PURGE ALL local files and reset the app state. Are you sure you want to proceed?')
  if (!confirmation) return

  debug('Reseting app state...')
  new AppState({
    preparingToUpload: false,
    uploading: false
  })
  PostItem.purgeTrackedFiles()
}

onBeforeMount( async () => {
  // Performs first time setup and starts scheduled tasks
  await firstTimeSetup()
  updateBatchInfo()
})
</script>
