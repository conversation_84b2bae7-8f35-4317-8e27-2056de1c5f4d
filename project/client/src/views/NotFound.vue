<template>
  <div>
    <Header title="Not Found"></Header>
    <div class="container mt-4">
      <div class="card border-0">
        <div class="card-body">
            404 - Page Not Found
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent} from "vue"
import Loader from "@/components/Loader";
import Header from "@/components/Header";
import {store} from "@/store"
import {AppState} from "@/app-modules/app-state/AppState";

export default defineComponent({
  components: {
    Header,
    Loader
  },
  data() {
    return {
      render: false,
      ready: false,
      busy: false
    }
  },
  async created() {
    new AppState({
      ready: false,
      busy: true,
      lastRequestedPage: ``
    })
    // Start watching for changes on vuex store properties
    this.$store.watch(() => this.$store.getters.mutated,  async mutated => {
      this.ready = store.state.appState.ready
      this.busy = store.state.appState.busy
      this.$forceUpdate()
    })
  },
  mounted() {

  }
})
</script>
