<template>
  <div>
    <Header title="Test CORS"></Header>
    <div class="container pt-2 pb-2">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <h5> Test new company search CORS </h5>
          </div>
        </div>
        <div class="card-body">
          <small class="form-text text-muted"> Valid API URL format: {{ $data.form.searchApiUrl }} </small>
          <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon-1">
                API URL
              </span>
            <input type="text" class="form-control" v-model="form.searchApiUrl" placeholder="Review APP URL" aria-label="Search API URL" aria-describedby="basic-addon-1">
          </div>
          <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon0">
                <font-awesome-icon icon="fa-solid fa-building fa-fw " />
              </span>
            <input type="text" class="form-control" v-model="form.companyName" placeholder="Company name" aria-label="Company name" aria-describedby="basic-addon0">
            <button class="btn btn-outline-secondary" type="button" @click="search(`Company name`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                    <span class="sr-only">Loading...</span>
                </span>
            </button>
          </div>
          <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon1">
                <font-awesome-icon icon="fa-solid fa-id-badge fa-fw " />
              </span>
            <input type="text" class="form-control" v-model="form.officerName" placeholder="Officer name" aria-label="Officer name" aria-describedby="basic-addon1">
            <button class="btn btn-outline-secondary" type="button" @click="search(`Officer name`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                    <span class="sr-only">Loading...</span>
                </span>
            </button>
          </div>
          <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon2">
                <font-awesome-icon icon="fa-solid fa-barcode fa-fw " />
              </span>
            <input type="text" class="form-control" v-model="form.companyNumber" placeholder="LP number" aria-label="LP number" aria-describedby="basic-addon2">
            <button class="btn btn-outline-secondary" type="button" @click="search(`LP number`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                    <span class="sr-only">Loading...</span>
                </span>
            </button>
          </div>
          <div class="input-group mb-3">
              <span class="input-group-text text-muted" id="basic-addon3">
                <font-awesome-icon icon="fa-solid fa-user fa-fw " />
              </span>
            <input type="text" class="form-control" v-model="form.companyNumber" placeholder="Account holder" aria-label="Account holder" aria-describedby="basic-addon3">
            <button class="btn btn-outline-secondary" type="button" @click="search(`Account holder`)" :disabled="performingSearch">
              <font-awesome-icon v-show="!performingSearch" icon="fa-solid fa-search fa-fw " />
              <span v-show="performingSearch" role="status" class="spinner-border text-primary" style="width: 1rem; height: 1rem;">
                    <span class="sr-only">Loading...</span>
                </span>
            </button>
          </div>
        </div>
        <hr>
        <div class="p-2">
          <pre style="white-space: pre-line;">
            <code v-html="JSON.stringify(result)"></code>
          </pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {defineComponent} from "vue"
import {CompanySearch} from "@/app-modules/cms/services/CompanySearch";
import Header from "@/components/Header.vue";
import {MAILROOM_SEARCH_API} from "@/config/config"

export default defineComponent({
  name: "Test.vue",
  components: {
    Header
  },
  data() {
    return {
      file: {},
      render: false,
      form: {
        searchApiUrl: MAILROOM_SEARCH_API,
        companyName: '',
        officerName: '',
        companyNumber: ''
      },
      result: {},
      performingSearch: false,
      thread3: ``,
      searchBy: `query`,
    }
  },
  methods: {
    async search(by: string) {
      let search = new CompanySearch({
        companyName: this.form.companyName,
        officerName: this.form.officerName,
        companyNumber: this.form.companyNumber,
        parameter: by
      })
      if(this.form.searchApiUrl) {
        this.result = await search.directCompanySearchByQuery(this.form.searchApiUrl)
      }
      else {
        this.result = await search.directCompanySearchByQuery()
      }
      this.$forceUpdate()
    }
  }
})
</script>

<style scoped>

</style>
