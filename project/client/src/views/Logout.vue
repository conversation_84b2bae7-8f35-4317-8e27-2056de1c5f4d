<template>
  <div class="container mt-4">
    <div class="card border-0">
      <div class="card-body">
          <main class="d-flex flex-column align-items-center justify-content-center">
            <Loader />
            Signed you out. Redirecting...
          </main>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {defineComponent} from 'vue';
import { signOut } from 'firebase/auth';
import {FirebaseAuth} from "@/app-modules/auth/main";
import {AppState} from "@/app-modules/app-state/AppState";
import {store} from "@/store";
import Loader from "@/components/Loader.vue";
import {Stats} from "@/app-modules/app-state/entities/Stats";
import {Status} from "@/app-modules/post-items/entities/Status";
import {Batch} from "@/app-modules/batch/entities/Batch";
import {PostItemLogEntry} from "@/app-modules/app-state/entities/PostItemLogEntry";
import {CompanySearch} from "@/app-modules/cms/services/CompanySearch";
import {AutoMatcher} from "@/app-modules/post-items/services/AutoMatcher";
import { useRoute } from 'vue-router';
import { LOGOUT_API_ROUTE_PATH } from "@/config/config";

export default defineComponent({
  name: 'LogoutView',
  components: {Loader},
  setup() {
    const route = useRoute();
    try{
      let redirectAlert = route.query.redAlert;
      store.commit('setRedirectAlert', redirectAlert);
    } catch (e) {
      console.log(e);
    }
    return {};
  },
  async mounted() {
    new AppState({
      ready: false,
      busy: true,
      authenticated: false,
      lastRequestedPage: ``
    })

    await store.commit('setUserEmail', '')
    await store.commit('setUid', '')
    await store.commit('setIsDeveloper', false)

    await AppState.purge()
    await Status.purgeAllMessages()
    await Batch.purge()
    await CompanySearch.purge()
    await Stats.purge()
    await PostItemLogEntry.purge()
    await AutoMatcher.purge()

    await signOut(FirebaseAuth.getAuth());
    window.location.assign(LOGOUT_API_ROUTE_PATH);
  }
});
</script>
