@import "~firebaseui/dist/firebaseui.css";
@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/variables-dark";
@import "~bootstrap/scss/maps";
@import "~bootstrap/scss/mixins";
@import "~bootstrap/scss/utilities";
@import "~bootstrap/scss/helpers";
@import "~bootstrap/scss/utilities/api";

$modal-sm:                          90vw;
$modal-md:                          85vw;
$modal-lg:                          80vw;
$modal-xl:                          1140px;
//$theme-colors: (
//        "light":      #eae4e2,
//        "dark":       #262626,
//        "primary":    #009fda,
//        "secondary":  #44697D,
//        "info":       #1d47df,
//        "success":    #88c444,
//        "warning":    #EEAF30,
//        "danger":     #ab0101,
//);
@import '~bootstrap/scss/bootstrap';

a {
 text-decoration: none !important;
}

.dropdown {
  .dropdown-menu {
    .dropdown-item:hover {
      cursor: pointer;
    }
  }
}

main {
  min-height: 65vh;
}

#statusTable {
  thead {
    tr {
      th {
        font-size: .65rem;
        text-align: center;
      }
      .confidence {
        max-width: 85px;
      }
    }
  }
  tbody {
    tr {
      td.fileImage {
        min-width: 200px;
        img {
          height: 5rem;
        }
      }
      td {
        font-size: .8rem;
        text-align: center;
      }
      .confidence {
        max-width: 85px;
      }
      .actions {
        font-size: .9rem;
        min-width: 160px;
        span:hover {
          cursor: pointer;
        }
        .btn-sm {
          padding: .05rem;
          font-size: .9rem;
        }
      }
    }
  }
}

#companySearchModal {
  .modal-file-preview {
      padding-top: 10px;
  }
  .modal-file-preview .PDF-delete-preview {
    width: 100%;
    height: 35vh;
  }
}

#deleteFileConfirmationModal {
  .PDF-delete-preview {
    width: 100%;
    height: 50vh;
  }
}

.btn:focus {
  outline: none !important;
  box-shadow: none !important;
}

.text-orange {
  color: orange;
}

.msg-header-brand {
  border-right: solid 1px #d2d2d2;
}

.v-toast {
  z-index: 9999 !important;
}

#addBatchFloater {
  z-index: 1050 !important;
}

.add-post-item-selected-company {
  background-color: #c4c4c4 !important;
}
