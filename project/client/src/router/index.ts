import { AppState } from '@/app-modules/app-state/AppState';
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { debug } from '@/app-modules/helpers/debug';
import { routeRequiresAuth } from '@/app-modules/auth/services/auth/Middleware';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'login',
    meta: { requiresAuth: false },
    component: () => import(/* webpackChunkName: "login" */ '../views/Login.vue')
  },
  {
    path: '/logout',
    name: 'logout',
    meta: { requiresAuth: false },
    component: () => import(/* webpackChunkName: "logout" */ '../views/Logout.vue')
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    meta: { requiresAuth: true },
    component: () => import(/* webpackChunkName: "dashboard" */ '../views/Dashboard.vue')
  },
  {
    path: '/test-cors',
    name: 'test-cors',
    meta: { requiresAuth: true },
    component: () => import(/* webpackChunkName: "testCors" */ '../views/TestCors.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import(/* webpackChunkName: "notFound" */ '../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory('/'),
  routes
})

router.beforeEach(async (to, from, next) => {
  new AppState({
    busy: true
  })

  if (to.meta?.requiresAuth) {
    try {
      await routeRequiresAuth(to, from, next)
    } catch (e) {
      debug(e)
    }
  } else {
    new AppState({
      currentPage: to.path
    })
    next()
  }
})

/*
* This navigation guard handles 404 page and bypass backend URIs
*/
router.beforeEach((to, from, next) => {
  const pathMatch = to.params.pathMatch
  const path = Array.isArray(pathMatch) ? pathMatch.join('/') : pathMatch

  if (path && (path.startsWith('direct/') || path.startsWith('api/'))) {
    next()
  } else if (to.name === 'not-found' || routes.map(route => route.path).includes(to.path)) {
    next()
  } else {
    next({ name: 'not-found' })
  }
})

export default router
