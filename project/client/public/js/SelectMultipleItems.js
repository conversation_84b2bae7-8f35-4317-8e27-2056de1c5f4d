class SelectMultipleItemsHelper {
    static MainCheckBox
    static CheckBoxes

    static initialize() {
        document.addEventListener('DOMContentLoaded', function() {
            SelectMultipleItemsHelper.MainCheckBox = document.querySelector('.select-check-main');
            SelectMultipleItemsHelper.CheckBoxes = document.querySelectorAll('.select-check');

            SelectMultipleItemsHelper.rigMainCheckBox();
            SelectMultipleItemsHelper.rigCheckBoxes();
        })
    }

    /**
     * Rigs the main check-box to select/unselect all check-boxes.
     */
    static rigMainCheckBox() {
        if (SelectMultipleItemsHelper.MainCheckBox != null) {
            SelectMultipleItemsHelper.MainCheckBox.addEventListener('change', function(event){
                if (SelectMultipleItemsHelper.MainCheckBox.checked) {
                    SelectMultipleItemsHelper.selectAll();
                    return true;
                }
                SelectMultipleItemsHelper.unselectAll();
                return false;
            });
        }
    }

    /**
     * Rigs the regular check-boxes to: update the main check-box when its checked status change manually by the user; update the other check-boxes' disabled status; update the label that shows the amount of selected check-boxes; and update the status of the button that brings up the bulk collect modal.
     */
    static rigCheckBoxes() {
        if (SelectMultipleItemsHelper.CheckBoxes != null) {
            SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
                checkBox.addEventListener('change', function(event) {
                    SelectMultipleItemsHelper.updateMainCheckBox();
                    SelectMultipleItemsHelper.updateDisabledStatuses(checkBox);
                    SelectMultipleItemsHelper.updateSelectedLabel();
                    SelectMultipleItemsHelper.updateBulkCollectButtonStatus();
                });
            });
        }
    }

    /**
     * Checks all check-boxes that are not disabled, then updates the label that shows the amount of selected check-boxes and updates the status of the button that brings up the bulk collect modal.
     */
    static selectAll() {
        SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
            if (!checkBox.disabled) {
                checkBox.checked = true;
            }
        })
        SelectMultipleItemsHelper.updateSelectedLabel();
        SelectMultipleItemsHelper.updateBulkCollectButtonStatus();
    }

    /**
     * Unchecks all check-boxes, then updates the disabled status of all check boxes, updates the label that shows the amount of selected check-boxes and updates the status of the button that brings up the bulk collect modal.
     */
    static unselectAll() {
        SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
            checkBox.checked = false;
        })
        SelectMultipleItemsHelper.updateDisabledStatuses();
        SelectMultipleItemsHelper.updateSelectedLabel();
        SelectMultipleItemsHelper.updateBulkCollectButtonStatus();
    }

    /**
     * Updates the main check box's status based on the check-boxes selected on the page.
     */
    static updateMainCheckBox() {
        let enabledUncheckedExists = false;
        let enabledCheckedExists = false;

        SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
            if (checkBox.disabled) {
                return;
            }
            if (checkBox.checked){
                enabledCheckedExists = true;
            } else {
                enabledUncheckedExists = true;
            }
            return;
        })

        if (enabledCheckedExists && enabledUncheckedExists) {
            SelectMultipleItemsHelper.MainCheckBox.checked = false;
            SelectMultipleItemsHelper.MainCheckBox.indeterminate = true;
            return;
        }

        if (enabledCheckedExists && !enabledUncheckedExists) {
            SelectMultipleItemsHelper.MainCheckBox.indeterminate = false;
            SelectMultipleItemsHelper.MainCheckBox.checked = true;
            return;
        }

        if (!enabledCheckedExists && enabledUncheckedExists) {
            SelectMultipleItemsHelper.MainCheckBox.indeterminate = false;
            SelectMultipleItemsHelper.MainCheckBox.checked = false;
            SelectMultipleItemsHelper.updateDisabledStatuses();
            return;
        }
    }

    /**
     * By default, when no parameters are passed (null), this function enables all check-boxes. When a check-box HTML element is passed, this function updates the status of all other checkboxes based on the given check-box, by checking that their company match the given check-box's own company.
     * @param {HTML Element|null} triggerCheckBox - The HTML element of the check-box based on which to update the status of all other check-boxes. When not passed (null), all check-boxes are enabled.
     */
    static updateDisabledStatuses(triggerCheckBox = null) {
        if (triggerCheckBox == null) {
            SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
                checkBox.disabled = false;
            });
            return;
        }

        const company = SelectMultipleItemsHelper.getCompany(triggerCheckBox);
    
        if (triggerCheckBox.checked) {
            SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
                const checkBoxCompany = SelectMultipleItemsHelper.getCompany(checkBox);

                if (
                    checkBoxCompany.companyName != company.companyName ||
                    checkBoxCompany.companyNumber != company.companyNumber
                ) {
                    checkBox.checked = false;
                    checkBox.disabled = true;
                    return;
                }
                checkBox.disabled = false;
            })
        }

        SelectMultipleItemsHelper.updateSelectedLabel();
        SelectMultipleItemsHelper.updateBulkCollectButtonStatus();
    }

    /**
     * Returns the name and number of the company of the post item related to the given check-box, in the form of an object.
     * @param {HTML Element} checkBox - The check-box of the item from which to get the company of.
     * @returns An object containing the company's name and number.
     */
    static getCompany(checkBox) {
        const itemRow = checkBox.parentElement.parentElement.id;
        const dataType = 'postItem';
        const postItem = ModalToolsHelper.getDataByRow(itemRow, dataType);
        return {
            'companyName' : postItem.companyName,
            'companyNumber' : postItem.companyNumber,
        };
    }

    /**
     * Updates the label that shows the amount of selected check-boxes.
     */
    static updateSelectedLabel() {
        const selectedLabel = document.getElementById('selectedAmountLabel');
        const defaulText = " items selected";
        let checkBoxCount = 0;
        SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
            if (checkBox.checked) {
                checkBoxCount++;
            }
        })
        selectedLabel.innerHTML = checkBoxCount + defaulText;
        return;
    }

    /**
     * Updates the status of the button that brings up the bulk collect modal.
     */
    static updateBulkCollectButtonStatus() {
        const selectedItems = SelectMultipleItemsHelper.getSelectedItems();
        const bulkCollectButton = document.getElementById('bulkCollectButton');
    
        if(selectedItems.length == 0) {
            SelectMultipleItemsHelper.disableBulkButton(bulkCollectButton, false, false, true);
            return;
        }

        let oldCompanyName = '';
        let newCompanyName = '';
        let multipleCompanies = false;
        let hasRestrictedAction = false;
        let restrictedActions = SelectMultipleItemsHelper.getRestrictedAtions(bulkCollectButton.id);

        selectedItems.forEach(postItem => {
            oldCompanyName = newCompanyName;
            newCompanyName = postItem.companyName;
            if (oldCompanyName != newCompanyName && oldCompanyName != ''){
                multipleCompanies = true;
            }
            postItem.events.forEach(event => {
                if (restrictedActions.includes(event.eventName)) {
                    hasRestrictedAction = true;
                }
            });
    
        });

        if(multipleCompanies || hasRestrictedAction){
            SelectMultipleItemsHelper.disableBulkButton(bulkCollectButton, multipleCompanies, hasRestrictedAction);
        } else {
            SelectMultipleItemsHelper.enableBulkButton(bulkCollectButton);
        }
    }

    /**
     * Disables the button than brings up the bulk collect modal. Then, this function sets a tooltip up with the appropriate message of why you cannot collect the selected items in bulk.
     * @param {HTML Element} button - The html element of the button that brings up the bulk collect modal.
     * @param {Boolean} multipleCompanies - Whether the selected items are for more than one company.
     * @param {Boolean} hasRestrictedAction - Whether one or more of the selected companies have a restricted action taken upon them.
     * @param {Boolean} noneSelected - Whether no items are selected.
     */
    static disableBulkButton(button, multipleCompanies, hasRestrictedAction, noneSelected = false) {
        const noneSelectedMessage = "Please select one or more items."
        const multipleCompaniesMessage = "The items you have selected belong to different companies. "
        const hasRestrictedActionMessage = "One or more of the selected items have already had an action taken upon. "
        let finalMessage = "";
    
        if(multipleCompanies){
            finalMessage = finalMessage + multipleCompaniesMessage;
        }
        if(hasRestrictedAction){
            finalMessage = finalMessage + hasRestrictedActionMessage;
        }
        if(noneSelected){
            finalMessage = noneSelectedMessage;
        }
    
        button.disabled = true;
        const buttonWrapper = button.parentElement;
        buttonWrapper.setAttribute('data-bs-toggle', 'tooltip');
        buttonWrapper.setAttribute('data-bs-original-title', finalMessage);
        buttonWrapper.setAttribute('data-bs-placement', 'right');
    }

    /**
     * Enables the button that brings up the bulk colled modal, and removes the explanatory tooltip.
     * @param {HTML Element} button - The html element of the button that brings up the bulk colled modal.
     */
    static enableBulkButton(button) {
        button.disabled = false;
        const buttonWrapper = button.parentElement;
        buttonWrapper.removeAttribute('data-bs-toggle')
        buttonWrapper.removeAttribute('data-bs-original-title');
        buttonWrapper.removeAttribute('data-bs-placement');
    }

    /**
     * Returns an array list of the names of the events that are retricted actions.
     * @param {String} buttonID - The ID of the button that brings up the bulk colled modal, which contains the list of restricted actions.
     * @returns An array of strings related to the restricted action names.
     */
    static getRestrictedAtions(buttonID) {
        const restrictedActionsObj = ModalToolsHelper.getDataByRow(buttonID, 'restricted-actions');
        const restrictedActionsArr = Object.values(restrictedActionsObj);
        return restrictedActionsArr;
    }

    /**
     * Returns an array of post item objects, selected by their check-box 'checked' status.
     * @returns An array of the selected post item objects.
     */
    static getSelectedItems() {
        let selected = [];
        SelectMultipleItemsHelper.CheckBoxes.forEach(checkBox => {
            if (checkBox.checked) {
                const itemRow = checkBox.parentElement.parentElement.id;
                const dataType = 'postItem';
                const postItem = ModalToolsHelper.getDataByRow(itemRow, dataType);
                selected.push(postItem);
            }
        })
        return selected;
    }
}

