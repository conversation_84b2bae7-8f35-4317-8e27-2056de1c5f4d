/**
 * InjectModalValues
 * Functions called by the modal-generating buttons, to populate modals with data.
*/
class InjectModalValuesHelper {
    /**
     * Populates the Collect Item modal.
     * @param {string} itemRow - ID of the html element that contains the data needed to populate the collect item modal.
     */
    static updateCollectModal(itemRow) {
        ModalToolsHelper.clearCollectFormInput();
        var postItem = ModalToolsHelper.getDataByRow(itemRow, "postItem");
        ModalToolsHelper.setFields(postItem, "collect_item", "collect");
    }

    /**
     * Populates the Log RTS modal.
     * @param {string} itemRow - ID of the html element that contains the data needed to populate the Log RTS modal.
     */
    static updateRTSModal(itemRow) {
        var postItem = ModalToolsHelper.getDataByRow(itemRow, "postItem");
        ModalToolsHelper.setFields(postItem, "log_rts", "RTS");
    }

    /**
     * Populates the Securely Destroy modal.
     * @param {string} itemRow - ID of the html element that contains the data needed to populate the Securely Destroy modal.
     */
    static updateSecurelyDestroyModal(itemRow) {
        var postItem = ModalToolsHelper.getDataByRow(itemRow, "postItem");
        ModalToolsHelper.setFields(postItem, "securely_destroy", "securelyDestroy");
    }

    /**
     * Populates the Details modal.
     * @param {string} itemRow - ID of the html element that contains the data needed to populate the details modal.
     */
    static updateDetailModal(itemRow) {
        const postItem = ModalToolsHelper.getDataByRow(itemRow, "postItem");
        const details = postItem.details;
        const events = postItem.events;
        const detailTable = document.getElementById('detailTable');
        const eventTable = document.getElementById('eventTable');
        ModalToolsHelper.purgeChildren(detailTable);
        ModalToolsHelper.purgeChildren(eventTable);
        ModalToolsHelper.setBasicInfoPanel(postItem);
        ModalToolsHelper.setAdditionalDetails(details, detailTable);
        ModalToolsHelper.setHistory(events, eventTable, postItem, details);
    }

    /**
     * Populates the item forwarding confirmation modal's selected options area.
     */
    static updateForwardingModal() {
        const fwdFormDataTable = document.getElementById('fwdFormDataTable');
        const fwdForm = document.getElementById('forward_item_form');
        ModalToolsHelper.purgeChildren(fwdFormDataTable);
        ModalToolsHelper.setupFwdFormDataTable(fwdFormDataTable, fwdForm);
    }

    /**
     * Populates the bulk collect item modal.
     */
    static updateBulkCollectModal() {
        ModalToolsHelper.clearCollectFormInput(true);
        const hiddenFieldID = 'bulk_collect_item_form_selected_items';
        const bulkCollectItemList = document.getElementById('bulkCollectItemList');
        const selectedItems = SelectMultipleItemsHelper.getSelectedItems();
        ModalToolsHelper.setBulkCollectCompanyName(selectedItems);
        ModalToolsHelper.purgeChildren(bulkCollectItemList);
        ModalToolsHelper.setupBulkCollectItemList(bulkCollectItemList, selectedItems);
        ModalToolsHelper.addSelectedItemsToForm(selectedItems, hiddenFieldID);
    }
}