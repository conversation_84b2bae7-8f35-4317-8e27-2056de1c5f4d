/**
 * ModalTools
 * A toolkit to be used for modal data manipulation.
*/
class ModalToolsHelper {
    /**
     * Returns the data object store in a data attribute of an html tag. Used to acquire data to be injected into modals dynamically.
     * @param {string} itemRow - ID of the html element from which you wish to get data.
     * @param {string} dataType - Name of the data attribute you wish to get the data of (e.g., 'postItem' for a `data-postItem` attribute).
     * @returns Object
     */
    static getDataByRow(itemRow, dataType) {
        return JSON.parse(
            document
            .getElementById(itemRow)
            .getAttribute('data-' + dataType));
    }

    /**
     * Purges all child elements of a given element.
     * @param {HTML element} element - The element from which to purge all children.
     */
    static purgeChildren(element) {
        while (element.firstChild) {
            element.removeChild(element.firstChild);
        }
    }

    /**
     * Creates an anchor element that can be clicked to show the full string that is being truncated by the truncateMiddleOfString() function.
     * @param {string} fullString - The full string that will be ellipsed.
     * @returns A clicable ellipsis anchor element to be used with the truncateMiddleOfString() function.
     */
    static createExpandableEllipsis(fullString) {
        const DataAttr = 'data-full-string';
        const anchor = document.createElement('a');
        anchor.setAttribute(DataAttr, fullString);
        anchor.setAttribute('onclick', 'ModalToolsHelper.expandEllipsis(this)')
        anchor.setAttribute('class', 'link')
        anchor.innerHTML= " [...] ";

        return anchor;
    }

    /**
     * Replaces the given ellipsis' parent element with an element of equal tag and attributes. It displays the full string passed as a parameter to the ellipsis. Clicking it truncates the string once more. This function is meant to be used by the createExpandableEllipsis() function.
     * @param {HTML Element} ellipsis - The html element reference of the ellipsis anchor clicked to expand the truncated string.
     */
    static expandEllipsis(ellipsis) {
        const newParent = document.createElement(ellipsis.parentElement.tagName);
        if (ellipsis.parentElement.hasAttributes()) {
            const attributes = ellipsis.parentElement.attributes;
            for (const attribute of attributes){
                newParent.setAttribute(attribute.nodeName, attribute.nodeValue);
            }
        }
        newParent.setAttribute('data-old-innerHTML', ellipsis.parentElement.innerHTML);
        newParent.setAttribute('onclick', 'ModalToolsHelper.collapseEllipsis(this)');
        newParent.innerHTML = ellipsis.getAttribute('data-full-string');
        ellipsis.parentElement.outerHTML = newParent.outerHTML;
    }

    /**
     * Collapses the string inside of the parent element given with the expanded string. Also remove the attributes added by the expandEllipsis() function. This function is meant to be used by the expandEllipsis() function.
     * @param {HTML Element} parent - The html element reference of the parent element clicked to collapse the string once more.
     */
    static collapseEllipsis(parent) {
        parent.innerHTML = parent.getAttribute('data-old-innerHTML');
        parent.removeAttribute('data-old-innerHTML');
        parent.removeAttribute('onclick');
    }

    /**
     * Truncates a String in omitting its middle part.
     * @param {string} url - The string to truncate
     * @param {int} maxLength - The max lenght of the truncated string.
     * @param {bool} expandable - Whether to create a an expandable ellipsis that can be clicked to show the whole context. Works poorly inside of anchor tags with an 'href' attribute.
     * @returns The truncated string
     */
    static truncateMiddleOfString(string, maxLength, expandable=false) {
        if (string.length <= maxLength) {
            return string;
        }
        const leftHalfLength = Math.floor((maxLength - 7) / 2);
        const rightHalfLength = Math.ceil((maxLength - 7) / 2);
        const leftHalf = string.substring(0, leftHalfLength);
        const rightHalf = string.substring(string.length - rightHalfLength);
        if (expandable) {
            const ellipsis = ModalToolsHelper.createExpandableEllipsis(string);
            return leftHalf + ellipsis.outerHTML + rightHalf;
        }
        return `${leftHalf} [...] ${rightHalf}`;
    }

    /**
     * Capitalizes a given string and replaces its underscores with spaces. Returns the formatted string.
     * @param {string} string - The string to be modified.
     * @returns - The string with the modified Format.
     */
    static capitalizeAndReplaceUnderscores(string) {
        const capitalizedString = string.replace(/^[a-z]/, match => match.toUpperCase());
        const capitalizedAndSpacedString = capitalizedString.split('_').join(' ');
        return capitalizedAndSpacedString;
    }

    /**
     * Formats a PHP DateTime string into the following pattern: 'DD/MM/YYYY hh:mm'.
     * @param {string} PHPDateTime - A PHP formatted DateTime string.
     * @returns A formated date-time string in the following pattern: 'DD/MM/YYYY hh:mm'.
     */
    static formatDateTimeString(PHPDateTime) {
        const dateTime = new Date(PHPDateTime);
        const day = dateTime.getDate().toString().padStart(2, '0');
        const month = (dateTime.getMonth() + 1).toString().padStart(2, '0');
        const year = dateTime.getFullYear();
        const hour = dateTime.getHours().toString().padStart(2, '0');
        const minute = dateTime.getMinutes().toString().padStart(2, '0');
        const formattedDateTime = `${day}/${month}/${year} ${hour}:${minute}`;
        return formattedDateTime;
    }

    /**
     * Creates a table cell with the desired data.
     * @param {mixed} data - The desired contents of the cell.
     * @param {tagName} cellType - The type of html element of the table cell. Usually 'td' or 'th'.
     * @returns The generated table cell element containing the desired data.
     */
    static createCellWithData(data, cellType) {
        const cell = document.createElement(cellType);
        cell.innerHTML = data;
        return cell;
    }


    /**
     * Creates and adds a cell of a given type, with the desired data, to a given row.
     * @param {HTML Element} row - The row to which to add a cell.
     * @param {mixed} data - The data to be displayed inside the table cell.
     * @param {tagName} cellType - The type of html element of the table cell. Usually 'td' or 'th'.
     */
    static addCellToRow(row, data, cellType) {
        const cell = ModalToolsHelper.createCellWithData(data, cellType);
        row.appendChild(cell);
    }

    /**
     * Clears any unsubmitted input left over inside the collected item modal or bulk collected modal.
     */
    static clearCollectFormInput(bulk = false) {
        if (!bulk) {
            document.getElementById('collect_item_form_collected_name').value = "";
            document.getElementById('collect_item_form_collected_number').value = "";
        } else {
            document.getElementById('bulk_collect_item_form_collected_name').value = "";
            document.getElementById('bulk_collect_item_form_collected_number').value = "";
        }
    }

    /**
     * Sets the fields of a given modal type to match the given item's.
     * @param {Object} postItem - Post item from which to get data
     * @param {string} formName - Name of the form ID set by Twig/Symfony of the form loaded inside the modal. Used to set the item ID field.
     * @param {string} type - The type of modal being used. ('RTS' or 'collect')
     */
    static setFields (postItem, formName, type) {
        document.getElementById(formName + '_form_item_id').value = postItem.id;
        document.getElementById(type + 'ItemBatchNumber').innerHTML = postItem.batchNumber ?? "N/A";
        document.getElementById(type + 'ItemCompanyNumber').innerHTML = postItem.companyNumber ?? "N/A";
        document.getElementById(type + 'ItemCompanyName').innerHTML = postItem.companyName ?? "N/A";
        document.getElementById(type + 'ItemType').innerHTML = postItem.type ?? "N/A";
        document.getElementById(type + 'ItemSender').innerHTML = postItem.sender ?? "N/A";
        document.getElementById(type + 'ItemDtc').innerHTML = !!postItem.dtc ? ModalToolsHelper.formatDateTimeString(postItem.dtc) : "N/A";
    }

    /**
     * Sets the data for the 'Additional details' panel.
     * @param {ObjectArray} details - List of details to be loaded into the panel.
     * @param {HTML element} detailTable - HTML element inside which the details are inserted.
     */
    static setAdditionalDetails(details, detailTable) {
        details.forEach(detail => {
            var row = document.createElement('tr');
            var key = document.createElement('th');
            var value = document.createElement('td');

            key.innerHTML = ModalToolsHelper.capitalizeAndReplaceUnderscores(detail.key);
            value.innerHTML = detail.value;
            if (detail.key === "pdf_link") {
                key.innerHTML = "PDF link";
                var a = document.createElement('a');
                a.setAttribute('href', detail.value);
                a.setAttribute('target', '_blank');
                a.innerHTML = ModalToolsHelper.truncateMiddleOfString(detail.value, 60);
                value.innerHTML = "";
                value.appendChild(a);
            }
            row.appendChild(key);
            row.appendChild(value);
            detailTable.appendChild(row);
        });

        if (!detailTable.firstChild) {
            var noInfoDiv = document.createElement('div');
            noInfoDiv.innerHTML = "No information to be shown.";
            noInfoDiv.setAttribute('class', 'container-fluid')
            detailTable.appendChild(noInfoDiv);
        }
    }

    /**
     * Adds an event to the 'History' panel's table.
     * @param {Object} event - The event that will be added to the table.
     * @param {HTML element} eventTable - The table to which the event will be added.
     * @param {Object} postItem - The post item for which we are adding an event.
     * @param {ObjectArray} details - The details of the item for which we are creating the event.
     */
    static setHistoryElement(event, eventTable, postItem, details) {
        const row = document.createElement('tr');
        const name = document.createElement('th');
        const dtc = document.createElement('td');
        const operator = document.createElement('td');
        name.setAttribute('class', 'align-middle');
        dtc.setAttribute('class', 'align-middle');
        operator.setAttribute('class', 'align-middle');
        name.innerHTML = ModalToolsHelper.capitalizeAndReplaceUnderscores(event.eventName);
        dtc.innerHTML = ModalToolsHelper.formatDateTimeString(event.dtc);
        operator.innerHTML = event.operator;
        row.appendChild(name);
        row.appendChild(dtc);
        row.appendChild(operator);
        if (event.eventName == 'address_label_generated') {
            const labelButtonTd = document.createElement('td');
            labelButtonTd.setAttribute('class', 'text-end align-middle');
            labelButtonTd.appendChild(ModalToolsHelper.createLabelButton(postItem, details));
            row.appendChild(labelButtonTd);
        } else {
            const TailingTd = document.createElement('td');
            TailingTd.setAttribute('class', 'align-middle');
            row.appendChild(TailingTd);
        }
        eventTable.appendChild(row);
    }

    /**
     * Sets up the 'History' panel of the details modal.
     * @param {ObjectArray} events - The list of events to be added to the panel.
     * @param {HTML element} eventTable - The table to which the events will be added.
     * @param {Object} postItem - The post item from which to add data.
     * @param {ObjectArray} details - The details for the post item for which we are adding details.
     */
    static setHistory (events, eventTable, postItem, details) {
        events.forEach(event => {
            ModalToolsHelper.setHistoryElement(event, eventTable, postItem, details);
        });
    }

    /**
     * Creates a button that points to the route of the given item's post label.
     * @param {Object} postItem - The post item to which the post label button is being created.
     * @param {ObjectArray} details - The details for the post item for which we are creating this button.
     * @returns A button element that points to the given post item's post label.
     */
    static createLabelButton(postItem, details) {
        const href = '/direct/post/label/' + postItem.id + '/' + ModalToolsHelper.getPostageClass(details);

        const button = document.createElement('a');
        button.setAttribute('class', 'btn btn-sm btn-outline-dark');
        button.setAttribute('target', '_blank');
        button.setAttribute('href', href);
        button.innerHTML = 'Reprint';

        return button;
    }

    /**
     * Returns the postage class for an item from a given list of details.
     * @param {ObjectArray} details - The details from which we want to extract the postage class value.
     * @returns A string corresponding to the value of the postage_class key.
     */
    static getPostageClass(details) {
        let postageClass = '';
        details.forEach(detail => {
            if (detail.key == 'postage_class'){
                postageClass = detail.key;
            }
        });
        return postageClass;
    }

    /**
     * Adds the needed data from a given post item to the 'Basic information' panel
     * @param {Object} postItem - The post item from which to add data.
     */
    static setBasicInfoPanel(postItem) {
        document.getElementById('detailPostItemCompany').innerHTML = postItem.companyNumber + " - " + postItem.companyName;
        document.getElementById('detailPostItemId').innerHTML = postItem.id;
        document.getElementById('detailPostItemType').innerHTML = postItem.type;
        document.getElementById('detailPostItemStatus').innerHTML = postItem.status;
        document.getElementById('detailPostItemSender').innerHTML = postItem.sender;
    }

    /**
     * Returns a string value that represents the formated data collected from the given input element.
     * If there is no data, it returns a formated "(unselected) *" value, signifying that the user must still select it.
     * @param {HTML Element} inputElement - The form input element to check for data.
     * @param {String} unit - The unit for the data acquired from the form.
     * @returns The string value to be used on a row in the table of selected options or "(unselected) *" if a value was not defined in the form, by the operator.
     */
    static setIfUnselected(inputElement="", unit="") {
        try {
            if (inputElement == "" || inputElement.value == ""){
                const b = document.createElement('b');
                const i = document.createElement('i');
                const p = document.createElement('p');
                b.setAttribute('class', 'text-danger');
                i.setAttribute('class', 'text-secondary');
                b.innerHTML = ' *';
                i.innerHTML = '(unselected)';
                p.appendChild(i);
                p.appendChild(b);
                return p.innerHTML;
            } else {
                return inputElement.value + " " + unit;
            }
        } catch (error) {
            return false;
        }
    }

    /**
     * Returns the data from a form field organized in an object with properties "key" and "value",
     * to be used for generating a data row. This function needs to be updated whenever a new possible
     * field is added to the form.
     * @param {HTML Element} inputElement - The input element from which to get data.
     * @returns An object with a key/value pair based on the given input element.
     */
    static getOrganizedFormData(inputElement) {
        const id = inputElement.id;
        let key = "";
        let value = "";
        switch (id) {
            case 'forward_item_form_statutory_item':
                key = "Statutory item type:";
                value = ModalToolsHelper.setIfUnselected(inputElement);
                break;

            case 'forward_item_form_description':
                key = "Description:";
                value = inputElement.value || 'None';
                break;

            case 'forward_item_form_items_quantity':
                key = "Item quantity:";
                value = ModalToolsHelper.setIfUnselected(inputElement, "item(s)");
                break;

            case 'forward_item_form_total_weight':
                key = "Total Weight:";
                value = ModalToolsHelper.setIfUnselected(inputElement, "grams");
                break;

            case 'forward_item_form_extra_charge':
                key = "Extra charge:";
                value = ModalToolsHelper.setIfUnselected(inputElement); // , "&pound");
                break;

            case 'forward_item_form_custom_price':
                key = "Charge costumer:";
                if (inputElement.checked) {
                    value = "No"
                } else {
                    value = "Yes"
                }
                break;

            case id.match(/^forward_item_form_postage_class/)?.input:
                if (!inputElement.checked) {
                    return false;
                }

                if (id.match(/_0$/)) {
                    value = 'First class';
                }

                if (id.match(/_1$/)) {
                    value = 'Second class';
                }
                key = 'Postage class:';
                break;

            case 'forward_item_form__token':
                return false;
                break;

            default:
                key = "";
                value = "";
                return false;
                break;
        }

        return {
            'key': key,
            'value': value
        };
    }

    /**
     * Appends a new data row to a given data table body from a provided form input element or a provided set of data.
     * @param {HTML Element} tbody - The tbody element to which to add a new data row.
     * @param {HTML Element|string} inputElement - The input element from the form to get data from. Can be given as an empty string if providing data manually.
     * @param {Object} data - Optional object parameter to set a new row as a predefined one (e.g.: {'key': 'Volume:', 'value': volume})
     */
    static appendFwdFormDataTableRow(tbody, inputElement, data={}) {
        if (Object.keys(data).length === 0) {
            data = ModalToolsHelper.getOrganizedFormData(inputElement);
        }
        if (!!data && Object.keys(data).length !== 0) {
            const tr = document.createElement('tr');
            const th = document.createElement('th');
            const td = document.createElement('td');
            th.setAttribute('class', 'align-middle');
            td.setAttribute('class', 'align-middle');
            th.innerHTML = data.key;
            td.innerHTML = data.value;
            tr.appendChild(th);
            tr.appendChild(td);
            tbody.appendChild(tr);
        }
    }

    /**
     * Returns whether there is already a postage class field among the listed data for a given tbody element.
     * Since both radio buttons come as unselected by default, this function is used to evaluate if there is a
     * need to create the field or not.
     * @param {HTML Element} tbody - The table body on which to check if there is already a postage class field among the listed data.
     * @returns boolean value of whether there is already a postage class among field the listed data.
     */
    static hasPostageClass(tbody){
        let has = false;
        tbody.querySelectorAll('th').forEach(th => {
            if (th.innerHTML=='Postage class:') {
                has = true;
            }
        });
        return has;
    }

    /**
     * Sets up the table of selected options from the given form.
     * @param {HTML Element} fwdFormDataTable - The table that displays the selected options on the confirmation modal.
     * @param {HTML Element} fwdForm - The form that has the info we need to supply.
     */
    static setupFwdFormDataTable(fwdFormDataTable, fwdForm) {
        const formData = fwdForm.querySelectorAll('input, select');
        const tbody = document.createElement('tbody');
        formData.forEach(inputElement => {
            ModalToolsHelper.appendFwdFormDataTableRow(tbody, inputElement);
        })
        if (!ModalToolsHelper.hasPostageClass(tbody)) {
            ModalToolsHelper.appendFwdFormDataTableRow(tbody, '', {
                'key': 'Postage class:',
                'value': ModalToolsHelper.setIfUnselected('')
            })
        }
        fwdFormDataTable.appendChild(tbody);
    }

    /**
     * Sets the hidden field value of the form with the given id. The value is set to the given list of selected post items.
     * @param {ObjectArray} selectedItems - Array of post items to be submitted by the form.
     * @param {string} hiddenFormFieldId - The id of the hidden form field used to store the selected items to be submitted with the form.
     */
    static addSelectedItemsToForm(selectedItems, hiddenFormFieldId) {
        const hiddenFormField = document.getElementById(hiddenFormFieldId);
        const idList = [];
        selectedItems.forEach(item => {
            idList.push(item.id);
        });
        hiddenFormField.value = idList.join(',');
    }

    /**
     * Appends a row containing a post item's information to the bulk collect modal's item list table body.
     * @param {HTML Element} bulkCollectList - The HTML element of the table body that displays the post items to be marked as collected, inside the bulk collect modal as a list.
     * @param {Object} item - The post item that will be added to the list.
     */
    static appendBulkCollectRow(bulkCollectList, item) {
        const tableRow = document.createElement('tr');
        ModalToolsHelper.addCellToRow(tableRow, item.type, 'td');
        ModalToolsHelper.addCellToRow(tableRow, item.sender, 'td');
        ModalToolsHelper.addCellToRow(tableRow, ModalToolsHelper.formatDateTimeString(item.dtc), 'td');
        bulkCollectList.appendChild(tableRow);
    }

    /**
     * Sets the company name label inside the bulk collect modal to match the company of the given selected post item list.
     * @param {ObjectArray} selectedItems - The list of selected post items (with matching companies) from which to get the company name.
     */
    static setBulkCollectCompanyName(selectedItems) {
        const companyLabel = document.getElementById('bulkCollectCompanyNameLabel');
        companyLabel.innerHTML = selectedItems[0].companyName;
    }

    /**
     * Sets up the list of selected post items inside the bulk collect modal.
     * @param {HTML Element} bulkCollectList - The HTML element of the table body to be setup with a list of selected post items.
     * @param {ObjectArray} selectedItems - The list of selected post items to populate the bulk collect modal table with.
     */
    static setupBulkCollectItemList(bulkCollectList, selectedItems) {
        selectedItems.forEach(item => {
            ModalToolsHelper.appendBulkCollectRow(bulkCollectList, item);
        });
    }
}