class StatutoryItemHelper {
    static addItemForm
    static statutoryModalSubmitButton
    static statutorySecretButton

    static initialize() {
        document.addEventListener('DOMContentLoaded', function() {
            StatutoryItemHelper.addItemForm = document.getElementById('add_item_form');
            StatutoryItemHelper.statutorySecretButton = document.getElementById('statutory_secret_button');
            StatutoryItemHelper.statutoryModalSubmitButton = document.getElementById('add_statutory_item_submit_from_modal');  
            if (StatutoryItemHelper.addItemForm) {
                StatutoryItemHelper.addItemForm.addEventListener('submit', function(event) {
                const type = event.target.elements['add_item_form_type'].value;
                if (type === 'statutory') {
                    event.preventDefault();
                    StatutoryItemHelper.showModal();
                    StatutoryItemHelper.submitStatutoryItem();
                }
                });
            }
        })
    }

    static showModal() {
        StatutoryItemHelper.statutorySecretButton.click();
        StatutoryItemHelper.statutorySecretButton.style.display = 'none';
    }

    static submitStatutoryItem() {
        StatutoryItemHelper.statutoryModalSubmitButton.addEventListener('click', function(event) {
            StatutoryItemHelper.addItemForm.submit();
        });
    }
}