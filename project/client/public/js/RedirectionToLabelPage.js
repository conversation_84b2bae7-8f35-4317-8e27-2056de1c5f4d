class RedirectionToLabelPageHelper {
    static initialize() {
        RedirectionToLabelPageHelper.checkRedirectionToLabelPage()
    }

    static checkRedirectionToLabelPage() {
        const urlParams = new URLSearchParams(window.location.search)
        const labelItemId = urlParams.get('label_item_id')
        const postageClass = urlParams.get('postage_class')
        if (labelItemId && postageClass) {
            RedirectionToLabelPageHelper.removeQueryStringsFromUrl()
            RedirectionToLabelPageHelper.openPageToPrint(labelItemId, postageClass)
        }
    }

    static removeQueryStringsFromUrl() {
        const urlWithoutParams = window.location.origin + window.location.pathname
        window.location.replace(urlWithoutParams)
    }

    static openPageToPrint(labelItemId, postageClass) {
        const url = '/direct/post/label/' + labelItemId + '/' + postageClass
        window.open(url, '_blank', 'noreferrer')
    }
}