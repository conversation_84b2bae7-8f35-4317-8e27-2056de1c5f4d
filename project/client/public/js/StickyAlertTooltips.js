class StickyAlertTooltipsHelper {
    static rigModalSubmitButton() {
        const confirmationModal = document.getElementById('forwardConfirmModal');
        const form = document.getElementById('forward_item_form');
        const submitButton = document.getElementById('forward_item_form_save');
        let submitted = false;
    
        submitButton.addEventListener('click', function(event) {
            event.preventDefault();
            submitted = true;
        });
    
        confirmationModal.addEventListener('hidden.bs.modal', function() {
            if(!submitted) {
                return;
            }
            if(form.reportValidity()) {
                if (StickyAlertTooltipsHelper.openPageToPrint(form)) {
                    form.submit();
                    StickyAlertTooltipsHelper.disableFormElements(form);
                }
            }
            submitted = false;
        });
    }
    
    static openPageToPrint(form) {
        const formData = new FormData(form);
        const postageClass = formData.get('forward_item_form[postage_class]');
        const labelItemId = form.getAttribute('data-post-item');
        if (!!postageClass && !!labelItemId) {
            const url = '/direct/post/label/' + labelItemId + '/' + postageClass
            window.open(url, '_blank', 'noreferrer')
            return true;
        }
        return false;
    }
    
    static disableFormElements(form) {
        const inputElements = form.querySelectorAll("input, button, select");
        for (let i = 0; i < inputElements.length; i++) {
            inputElements[i].disabled = true;
        }
    }
}