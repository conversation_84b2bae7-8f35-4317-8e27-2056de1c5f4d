class AddPostItemHelper {
    static cNameField
    static cNumberField
    static postItemType
    static form
    static postItemSender
    static totalWeightField
    static lengthField
    static widthField
    static heightField
    static testModeField
    static testModeRefreshRate
    static testMode
    static customPriceEnabled
    static customPrice
    static description

    static initialize() {
        document.addEventListener('DOMContentLoaded', function() {
            AddPostItemHelper.cNameField            = document.getElementById("add_item_form_company_name")
            AddPostItemHelper.cNumberField          = document.getElementById("add_item_form_company_number")
            AddPostItemHelper.postItemType          = document.getElementById('add_item_form_type');
            AddPostItemHelper.form                  = document.getElementById('add_item_form');
            AddPostItemHelper.postItemSender        = document.getElementById('add_item_form_sender');
            AddPostItemHelper.totalWeightField      = document.getElementById('add_item_form_total_weight');
            AddPostItemHelper.lengthField           = document.getElementById('add_item_form_length');
            AddPostItemHelper.widthField            = document.getElementById('add_item_form_width');
            AddPostItemHelper.heightField           = document.getElementById('add_item_form_height');
            AddPostItemHelper.customPriceEnabled    = document.getElementById('add_item_form_custom_price_enabled');
            AddPostItemHelper.customPrice           = document.getElementById('add_item_form_custom_price');
            AddPostItemHelper.description           = document.getElementById('add_item_form_description');
            AddPostItemHelper.testModeField         = document.getElementById('add_item_form_test_mode');
            AddPostItemHelper.testModeRefreshRate   = 200;
            AddPostItemHelper.testMode              = JSON.parse(localStorage.getItem('vuex')).testMode;

            AddPostItemHelper.postSenderHandler();
            AddPostItemHelper.hiddenParcelAdditionalFields();
            AddPostItemHelper.setupTestModeStateWatcher();
            AddPostItemHelper.setupCustomPriceHandler();
        })
    }

    static setupTestModeStateWatcher() {
        if (AddPostItemHelper.testModeField) {
            setInterval(
                () => {
                    if (AddPostItemHelper.hasTestModeStatusChanged()) {
                        AddPostItemHelper.setTestModeFieldValue(AddPostItemHelper.testMode);
                    }
                },
                AddPostItemHelper.testModeRefreshRate
            );
        }
    }

    static hasTestModeStatusChanged() {
        const oldValue = AddPostItemHelper.testMode;
        const newValue = AddPostItemHelper.getUpdatedTestMode();
        return oldValue !== newValue;
    }

    static highlightSelectedCompany(number) {
        const companies = document.querySelectorAll('[id^="company_"]');
        companies.forEach((company) => {
            company.classList.remove("add-post-item-selected-company");
        });
        const selectedCompany = document.getElementById(`company_${number}`);
        selectedCompany.classList.add("add-post-item-selected-company");
    }

    static setCompany(number, name){
        AddPostItemHelper.highlightSelectedCompany(number);
        AddPostItemHelper.cNameField.value   = name;
        AddPostItemHelper.cNumberField.value = number;
    }

    static addHiddenSenderElement(form) {
        const input = document.createElement("input");
        input.setAttribute("type", "hidden");
        input.setAttribute("name", "add_item_form[sender]");
        input.setAttribute("value", "OTHER");
        form.appendChild(input);
    }

    static getUpdatedTestMode() {
        return JSON.parse(localStorage.getItem('vuex')).testMode;
    }

    static setTestModeFieldValue(value = true) {
        AddPostItemHelper.testModeField.value = value ? 1 : 0;
    }

    static showParcelAdditionalFields() {
        AddPostItemHelper.setAttributesOfParcelAdditionalFields()
        if (AddPostItemHelper.totalWeightField && AddPostItemHelper.totalWeightField.closest('.form-group')) {
            AddPostItemHelper.totalWeightField.closest('.form-group').style.display = 'block';

            const dimensionsContainer = AddPostItemHelper.lengthField.closest('.mb-3');
            if (dimensionsContainer) {
                dimensionsContainer.style.display = 'block';
            }

            AddPostItemHelper.showCustomPriceFields();

            if (AddPostItemHelper.description && AddPostItemHelper.description.closest('.parcel-field')) {
                AddPostItemHelper.description.closest('.parcel-field').style.display = 'block';
                AddPostItemHelper.description.value = '';
            }
        }
    }

    static setAttributesOfParcelAdditionalFields() {
        AddPostItemHelper.lengthField.setAttribute('required', 'required');
        AddPostItemHelper.lengthField.setAttribute('type', 'number');
        AddPostItemHelper.lengthField.value = '';
        AddPostItemHelper.widthField.setAttribute('required', 'required');
        AddPostItemHelper.widthField.setAttribute('type', 'number');
        AddPostItemHelper.widthField.value = '';
        AddPostItemHelper.heightField.setAttribute('required', 'required');
        AddPostItemHelper.heightField.setAttribute('type', 'number');
        AddPostItemHelper.heightField.value = '';
        AddPostItemHelper.totalWeightField.setAttribute('required', 'required');
        AddPostItemHelper.totalWeightField.setAttribute('type', 'number');
        AddPostItemHelper.totalWeightField.value = '';
    }

    static removeAttributesOfParcelAdditionalFields() {
        AddPostItemHelper.lengthField.removeAttribute('required');
        AddPostItemHelper.widthField.removeAttribute('required');
        AddPostItemHelper.heightField.removeAttribute('required');
        AddPostItemHelper.totalWeightField.removeAttribute('required');
    }

    static hiddenParcelAdditionalFields() {
        if(AddPostItemHelper.lengthField) {
            AddPostItemHelper.removeAttributesOfParcelAdditionalFields()
            if (AddPostItemHelper.totalWeightField && AddPostItemHelper.totalWeightField.closest('.form-group')) {
                AddPostItemHelper.totalWeightField.closest('.form-group').style.display = 'none';

                const dimensionsContainer = AddPostItemHelper.lengthField.closest('.mb-3');
                if (dimensionsContainer) {
                    dimensionsContainer.style.display = 'none';
                }

                AddPostItemHelper.hideCustomPriceFields();

                if (AddPostItemHelper.description && AddPostItemHelper.description.closest('.parcel-field')) {
                    AddPostItemHelper.description.closest('.parcel-field').style.display = 'none';
                    AddPostItemHelper.description.value = '';
                }
            }
        }
    }

    static postSenderHandler() {
        if(AddPostItemHelper.postItemType) {
            AddPostItemHelper.postItemType.addEventListener('change', function() {
                const typeValue = this.value;
                if (AddPostItemHelper.isParcelSpecialNStatutoryType(typeValue)) {
                    AddPostItemHelper.postItemSender.value = 'OTHER';
                    AddPostItemHelper.postItemSender.disabled = true;
                } else {
                    AddPostItemHelper.postItemSender.value = '';
                    AddPostItemHelper.postItemSender.disabled = false;
                }
                if (typeValue === 'parcel') {
                    AddPostItemHelper.showParcelAdditionalFields()
                } else {
                    AddPostItemHelper.hiddenParcelAdditionalFields()
                }
            });
        }
    }

    static isParcelSpecialNStatutoryType(typeValue) {
        return ['parcel', 'special', 'non-statutory'].includes(typeValue);
    }

    static setupCustomPriceHandler() {
        if (AddPostItemHelper.customPriceEnabled && AddPostItemHelper.customPrice) {
            const customPriceContainer = AddPostItemHelper.customPrice.closest('.parcel-field');
            if (customPriceContainer) {
                customPriceContainer.style.display = 'none';
            }

            AddPostItemHelper.customPriceEnabled.addEventListener('change', function() {
                if (this.checked) {
                    if (customPriceContainer) {
                        customPriceContainer.style.display = 'block';
                    }
                    AddPostItemHelper.customPrice.disabled = false;
                    AddPostItemHelper.customPrice.required = true;
                } else {
                    if (customPriceContainer) {
                        customPriceContainer.style.display = 'none';
                    }
                    AddPostItemHelper.customPrice.disabled = true;
                    AddPostItemHelper.customPrice.required = false;
                    AddPostItemHelper.customPrice.value = '';
                }
            });
        }
    }

    static showCustomPriceFields() {
        if (AddPostItemHelper.customPriceEnabled && AddPostItemHelper.customPrice) {
            const customPriceEnabledContainer = AddPostItemHelper.customPriceEnabled.closest('.parcel-field');
            if (customPriceEnabledContainer) {
                customPriceEnabledContainer.style.display = 'block';
            }

            const customPriceContainer = AddPostItemHelper.customPrice.closest('.parcel-field');
            if (customPriceContainer) {
                customPriceContainer.style.display = 'none';
            }

            AddPostItemHelper.customPriceEnabled.checked = false;
            AddPostItemHelper.customPrice.disabled = true;
            AddPostItemHelper.customPrice.value = '';
        }
    }

    static hideCustomPriceFields() {
        if (AddPostItemHelper.customPriceEnabled && AddPostItemHelper.customPrice) {
            const customPriceEnabledContainer = AddPostItemHelper.customPriceEnabled.closest('.parcel-field');
            if (customPriceEnabledContainer) {
                customPriceEnabledContainer.style.display = 'none';
            }

            const customPriceContainer = AddPostItemHelper.customPrice.closest('.parcel-field');
            if (customPriceContainer) {
                customPriceContainer.style.display = 'none';
            }

            AddPostItemHelper.customPriceEnabled.checked = false;
            AddPostItemHelper.customPrice.disabled = true;
            AddPostItemHelper.customPrice.value = '';
        }
    }
}