class AddItemParcelHelper {
    static confirmationModal
    static confirmationModalDialog
    static addItemForm
    static hiddenLogRtsField
    static submitButton
    static secretButton
    static modalLogRTSButton
    static modalSubmitButton
    static hiddenForceSubmitField
    static urlParams
    static idCheck
    static mfService
    static companyNumber
    static companyName
    static type
    static sender

    static initialize() {
        document.addEventListener('DOMContentLoaded', function() {
            AddItemParcelHelper.confirmationModal          = document.getElementById('add_item_parcel_modal');
            AddItemParcelHelper.confirmationModalDialog    = document.getElementById('parcel_dialog');
            AddItemParcelHelper.addItemForm                = document.getElementById('add_item_form');
            AddItemParcelHelper.hiddenLogRtsField          = document.getElementById('add_item_form_log_rts');
            AddItemParcelHelper.submitButton               = document.getElementById('add_item_form_save');
            AddItemParcelHelper.secretButton               = document.getElementById('secret_button');
            AddItemParcelHelper.modalLogRTSButton          = document.getElementById('add_item_log_RTS_button');
            AddItemParcelHelper.modalSubmitButton          = document.getElementById('add_item_submit_from_modal');
            AddItemParcelHelper.hiddenForceSubmitField     = document.getElementById('add_item_form_force_submit');
            AddItemParcelHelper.urlParams                  = new URLSearchParams(window.location.search);
            AddItemParcelHelper.idCheck                    = AddItemParcelHelper.urlParams.get('activeIDCheck');
            AddItemParcelHelper.mfService                  = AddItemParcelHelper.urlParams.get('activeMFService');
            AddItemParcelHelper.companyNumber              = AddItemParcelHelper.urlParams.get('formCompanyNumber');
            AddItemParcelHelper.companyName                = AddItemParcelHelper.urlParams.get('formCompanyName');
            AddItemParcelHelper.type                       = AddItemParcelHelper.urlParams.get('formType');
            AddItemParcelHelper.sender                     = AddItemParcelHelper.urlParams.get('formSender');

            if (AddItemParcelHelper.parcelCheck()) {
                AddItemParcelHelper.rigRTSButton();
                AddItemParcelHelper.rigAddItemButton();
                AddItemParcelHelper.rigModalOnHide();
                AddItemParcelHelper.fillOutForm();
                AddItemParcelHelper.confirmationModalDialog.innerHTML = AddItemParcelHelper.getModalContent();
                AddItemParcelHelper.bringUpModal();
            }
        })
    }

    /**
     * Rigs the RTS button to change the value of the hidden field so that symfony adds the item and logs it to be RTS.
     */
    static rigRTSButton() {
        AddItemParcelHelper.modalLogRTSButton.addEventListener('click', function(event) {
            AddItemParcelHelper.hiddenLogRtsField.value = 1;
        });
    }

    /**
     * Rigs the add item button to change the value of the hidden force submit field so, the form can be saved from the modal.
     */
    static rigAddItemButton() {
        AddItemParcelHelper.modalSubmitButton.addEventListener('click', function(event) {
            AddItemParcelHelper.hiddenForceSubmitField.value = 1;
        });
    }

    /**
     * Rigs the modal's hide event to, if the user clicked on "Log RTS", submit the form with the given values. It then disables the form fields to avoid user misclicking.
     */
    static rigModalOnHide() {
        AddItemParcelHelper.confirmationModal.addEventListener('hidden.bs.modal', function() {

            if (AddItemParcelHelper.hiddenLogRtsField.value != 1 && AddItemParcelHelper.hiddenForceSubmitField.value != 1) {
                return;
            }
            if (AddItemParcelHelper.addItemForm.reportValidity()) {
                AddItemParcelHelper.addItemForm.submit();
                AddItemParcelHelper.disableFormFields();
                return;
            }
            AddItemParcelHelper.hiddenLogRtsField.value = 0;
            AddItemParcelHelper.hiddenForceSubmitField.value = 0;
        });
    }

    /**
     * Returns a proper status message based on the status of the company, with the reason why it can't receive parcels.
     * @returns A string with the status message.
     */
    static getModalContent() {
        const idDialog = `The company "${AddItemParcelHelper.companyName} - ${AddItemParcelHelper.companyNumber}" does not have a valid ID check.`;
        const mfDialog = `The company "${AddItemParcelHelper.companyName} - ${AddItemParcelHelper.companyNumber}" does not have a mail forwarding plan.`;
        const bothDialog = `The company "${AddItemParcelHelper.companyName} - ${AddItemParcelHelper.companyNumber}" does not have a valid ID check and does not have a mail forwarding plan.`;
        return AddItemParcelHelper.idCheck == 0 ? (mfService == 0 ? bothDialog : idDialog) : mfDialog;
    }

    /**
     * Brings up the modal alerting the user that the company cannot receive parcels, and prompting the user to log the item to be RTS.
     */
    static bringUpModal() {
        AddItemParcelHelper.secretButton.click();
        AddItemParcelHelper.secretButton.style.display = "none";
    }

    /**
     * Fills out the form fields with the info already provided before the page gets refreshed.
     */
    static fillOutForm() {
        AddItemParcelHelper.addItemForm.elements['add_item_form[company_number]'].value = AddItemParcelHelper.companyNumber;
        AddItemParcelHelper.addItemForm.elements['add_item_form[company_name]'].value = AddItemParcelHelper.companyName;
        AddItemParcelHelper.addItemForm.elements['add_item_form[type]'].value = AddItemParcelHelper.type;
        AddItemParcelHelper.addItemForm.elements['add_item_form[sender]'].value = AddItemParcelHelper.sender;
    }

    /**
     * Disable all form elements.
     */
    static disableFormFields() {
        Array.from(AddItemParcelHelper.addItemForm.elements).forEach(function(element) {
            element.disabled = true;
        })
    }

    static parcelCheck() {
        return AddItemParcelHelper.idCheck == 0 || AddItemParcelHelper.mfService == 0 || AddItemParcelHelper.companyName != null
    }
}
