# This is a dotenv file exclusively for Vuejs and Frontend environment variables
# It should have only the default values and development environment values

NODE_ENV=development

BASE_URL=

VUE_APP_SENTRY_DSN=

VUE_APP_CUSTOM_TOKEN_ENCRYPTION_KEY=
VUE_APP_UNIVERSAL_LOGIN_URL=

VUE_APP_BUILD_DATE=
VUE_APP_BUILD_COMMIT=local

VUE_APP_DEV_AUTH_DOMAIN=
VUE_APP_MSG_AUTH_DOMAIN=

# Base64 encoded Firebase credentials key (json)
VUE_APP_FIREBASE_KEY=

VUE_APP_IMAGE_STORAGE_URL=
VUE_APP_PDF_STORAGE_URL=

VUE_APP_MAILROOM_API_ACKNOWLEDGE_URL='/api/acknowledge/'
VUE_APP_MAILROOM_API_PULL_STATUS_URL='/api/pull-status/'
VUE_APP_MAILROOM_SEARCH_API=

VUE_APP_COMPANIES_MADE_SIMPLE_AUTHORIZATION=

VUE_APP_MANTLE_API_BUCKET_URL=
VUE_APP_MANTLE_API_BUCKET_AUTH_TOKEN=

VUE_APP_MANTLE_API_MATCH_URL=
VUE_APP_MANTLE_API_MATCH_AUTH_TOKEN=
