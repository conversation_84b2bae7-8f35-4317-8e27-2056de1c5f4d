{"name": "mailroom", "version": "3.0.0", "description": "Fronted client for Made Simple Group mailroom", "private": true, "scripts": {"watch": "vue-cli-service build --watch --no-clean --mode=development", "serve": "vue-cli-service serve", "build": "vue-cli-service build --no-clean", "build-dev": "vue-cli-service build --no-clean --mode=development"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.1.2", "@fortawesome/free-solid-svg-icons": "^6.1.2", "@fortawesome/vue-fontawesome": "^3.0.3", "@meforma/vue-toaster": "^1.3.0", "@popperjs/core": "^2.11.6", "@sentry/tracing": "^7.120.3", "@sentry/vue": "^9.39.0", "@types/lodash": "^4.14.157", "@types/node": "^20.8.7", "axios": "^1.5.1", "bootstrap": "^5.1.3", "firebase": "^9.6.11", "firebaseui": "^6.0.1", "lodash": "^4.17.15", "register-service-worker": "^1.7.2", "validator": "^13.7.0", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0", "workbox-cacheable-response": "^6.5.4", "workbox-core": "^6.5.4", "workbox-expiration": "^6.5.4", "workbox-precaching": "^6.5.4", "workbox-routing": "^6.5.4", "workbox-strategies": "^6.5.4", "workbox-sw": "^6.5.4", "workbox-webpack-plugin": "^6.5.4", "workbox-window": "^6.5.4"}, "devDependencies": {"@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "~4.5.5"}}