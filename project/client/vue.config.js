module.exports = {
    outputDir: '../public/',
    publicPath: '/',
    filenameHashing: false,
    // chainWebpack: config => {
    //     config.plugins.delete('html')
    // },
    configureWebpack: {
        plugins: [
        ]
    },
    devServer: {
        proxy: 'http://localhost:8080/'
    },
    pwa: {
        name: "mailroom",
        // configure the workbox plugin
        workboxPluginMode: "InjectManifest",
        workboxOptions: {
            // swSrc is required in InjectManifest mode.
            swSrc: "./serviceWorker.js", //path to your own service-worker file
        },
    },
}
