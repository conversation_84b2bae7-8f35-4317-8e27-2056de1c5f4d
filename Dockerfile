ARG IMAGE_REGISTRY=
ARG PROJECT_NAME=mailroom
ARG IMAGE_TAG=latest
FROM ${IMAGE_REGISTRY}${PROJECT_NAME}/base-image:${IMAGE_TAG}

ARG NODE_ENV=development
ENV NODE_ENV ${NODE_ENV}

ARG NODE_ENV_SHORT=dev
ENV NODE_ENV_SHORT ${NODE_ENV_SHORT}

ARG BUILD_DATE
ENV VUE_APP_BUILD_DATE ${BUILD_DATE}

ARG BUILD_COMMIT=local
ENV VUE_APP_BUILD_COMMIT ${BUILD_COMMIT}

COPY entrypoint.sh /etc/entrypoint.sh

COPY project/ .

# use this line instead of the one below it for setting up local environments
# RUN export COMPOSER_ALLOW_SUPERUSER=1 && composer install --optimize-autoloader
RUN composer install --optimize-autoloader

# Cache warmup and permission fix
RUN php bin/console cache:clear --no-warmup && \
    php bin/console cache:warmup && \
    chmod -R 2777 temp/cache && \
    chmod -R 2777 temp/log


# https://docs.npmjs.com/cli/v10/commands/npm-install
# When the NODE_ENV environment variable is set to production, npm will not install modules listed in devDependencies.
# In this case, to install all modules listed in both dependencies and devDependencies we need to use --production=false.
RUN rm -Rf client/node_modules/
RUN cd client/ && \
    npm install --production=false && \
    npm run build

ARG HTTP_PORT=8080
EXPOSE ${HTTP_PORT}

ENTRYPOINT ["bash", "/etc/entrypoint.sh"]
