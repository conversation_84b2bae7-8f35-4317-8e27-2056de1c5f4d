#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option] [environment]"
  echo "OPTIONS: "
  echo "   --base-image     Builds the base image (environments: base, default: base)"
  echo "   --app-image      Builds the application image (environments: dev|prod|staging, default: dev)"
  echo "   -h|--help        Shows this help message."
}

build_base_image() {
  source_path=./docker/base
  cloud_build_config_path="$source_path/cloudbuild.yml"
  image_name=base-image

  echo ">>> Building base image (source: $source_path)..."

  gcloud builds submit "$source_path" --project="$GCP_PROJECT_ID" --region="$GCP_PROJECT_REGION" \
    --config="$cloud_build_config_path" \
    --substitutions=_PROJECT_NAME="$PROJECT_NAME",_IMAGE_NAME="$image_name",_TAG="$IMAGE_TAG",_COMMIT_SHA="$CI_COMMIT_SHORT_SHA"
}

build_app_image() {
  source_path=./
  cloud_build_config_path=./cloudbuild.yml
  image_name="$ENVIRONMENT-image"

  echo ">>> Building app image (source: $source_path, environment: $ENVIRONMENT)..."

  case $ENVIRONMENT in
    dev)
      node_env="development"
      ;;
    prod)
      node_env="production"
      ;;
    *)
      node_env="$ENVIRONMENT"
      ;;
  esac

  gcloud builds submit "$source_path" --project="$GCP_PROJECT_ID" --region="$GCP_PROJECT_REGION" \
    --config="$cloud_build_config_path" \
    --substitutions="_PROJECT_NAME=$PROJECT_NAME,_IMAGE_NAME=$image_name,_TAG=$IMAGE_TAG,_COMMIT_SHA=$CI_COMMIT_SHORT_SHA,_BUILD_DATE=$CI_JOB_STARTED_AT,_NODE_ENV=$node_env"
}

ACTION=${1:-"--base-image"}
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=1

[ $DEBUG -eq 1 ] && set -x
[ $EXIT_ON_ERROR -eq 1 ] && set -e

case $ACTION in
  --base-image)
    ENVIRONMENT=${2:-base}
    ;;
  *)
    ENVIRONMENT=${2:-dev}
    ;;
esac

if [ "$ENVIRONMENT" != "base" ]; then
  ./bin/prepare.sh --all "$ENVIRONMENT"
fi

export $(grep -v '^#' .env | xargs -r);

if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
  CI_COMMIT_SHORT_SHA=$(date -u | sha256sum - | cut -d " " -f 1 | head -c8)
fi

if [ -z "$CI_JOB_STARTED_AT" ]; then
  CI_JOB_STARTED_AT=$(date -u +'%Y-%m-%d %H:%M:%S %z')
fi

if [ -z "$IMAGE_TAG" ]; then
  IMAGE_TAG=latest
fi

case $ACTION in
  --base-image)
    build_base_image
    ;;
  --app-image)
    build_app_image
    ;;
  -h|--help)
    print_usage
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage
    exit 1
    ;;
esac

exit 0
