#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option]"
  echo "OPTIONS: "
  echo "   --dev         Deploy project to Cloud Run using development environment"
  echo "   --prod        Deploy project to Cloud Run using production environment"
  echo "   --staging     Deploy project to Cloud Run using staging environment"
  echo "   -h|--help     Shows this help message"
}

deploy_to_cloud_run() {
  service_name=${1:-"$PROJECT_NAME-$CI_MERGE_REQUEST_IID"}
  image_tag=${2:-"$CI_COMMIT_SHORT_SHA"}
  image_full_path="$GCP_PROJECT_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$PROJECT_NAME/$ENVIRONMENT-image:$image_tag"

  echo ">>> Deploying project using '$ENVIRONMENT' environment..."

  if [ "$ENVIRONMENT" = "prod" ]; then
    gcloud run deploy "$service_name" --image="$image_full_path" --project="$GCP_PROJECT_ID" \
      --region="$GCP_PROJECT_REGION" --platform=managed --allow-unauthenticated \
      --min-instances=1 --max-instances=20 --cpu=2 --memory="1Gi" --timeout=60
  else
    gcloud run deploy "$service_name" --image="$image_full_path" --project="$GCP_PROJECT_ID" \
      --region="$GCP_PROJECT_REGION" --platform=managed --allow-unauthenticated \
      --max-instances=15 --timeout=60
  fi

  sleep 5

  gcloud run services describe "$service_name" --project="$GCP_PROJECT_ID" --region="$GCP_PROJECT_REGION"
}

deploy_dev() {
  deploy_to_cloud_run "$PROJECT_NAME-$CI_MERGE_REQUEST_IID" "$CI_COMMIT_SHORT_SHA"
}

deploy_production() {
  deploy_to_cloud_run "$PROJECT_NAME-prod" "$CI_COMMIT_SHORT_SHA"
}

deploy_staging() {
  deploy_to_cloud_run "$PROJECT_NAME-staging" "$CI_COMMIT_SHORT_SHA"
}

ACTION=${1:-"--dev"}
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=0

[ $DEBUG -eq 1 ] && set -x
[ $EXIT_ON_ERROR -eq 1 ] && set -e

if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
  CI_COMMIT_SHORT_SHA=$(date -u | sha256sum - | cut -d " " -f 1 | head -c8)
fi

if [ -z "$CI_MERGE_REQUEST_IID" ]; then
  CI_MERGE_REQUEST_IID="$CI_COMMIT_SHORT_SHA"
fi

if [ -z "$ENVIRONMENT" ]; then
  ENVIRONMENT=$(echo "$ACTION" | tr -d '-')
fi

case $ACTION in
  --dev)
    deploy_dev
    ;;
  --prod)
    deploy_production
    ;;
  --staging)
    deploy_staging
    ;;
  -h|--help)
    print_usage
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage
    exit 1
    ;;
esac

exit 0
