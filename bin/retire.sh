#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option]"
  echo "OPTIONS: "
  echo "   --dev         Delete the service on development environment"
  echo "   --old         Delete old services on development environment (older than 7 days)"
  echo "   --prod        Delete the service on production environment"
  echo "   --staging     Delete the service on staging environment"
  echo "   -h|--help     Shows this help message"
}

retire_service() {
  service_name=${1:-"$PROJECT_NAME-$CI_MERGE_REQUEST_IID"}

  echo ">>> Deleting Cloud Run service (name: '$service_name')..."
  gcloud run services delete "$service_name" --project="$GCP_PROJECT_ID" --region="$GCP_PROJECT_REGION" --quiet
}

retire_dev() {
  retire_service "$PROJECT_NAME-$CI_MERGE_REQUEST_IID"
}

retire_old_services() {
  services_list=services-list.json

  gcloud run services list --filter="metadata.name:$PROJECT_NAME" --sort-by=metadata.creationTimestamp \
    --format="json(metadata.name,metadata.creationTimestamp)" > $services_list

  today=$(date --utc +"%Y-%m-%d")
  last_week=$(date --date "$today -7 day" +"%Y-%m-%d")

  for row in $(jq -c '.[].metadata' $services_list); do
    name=$(echo "$row" | jq '.name' | tr -d '"')

    if [ "$name" = "$PROJECT_NAME-prod" ] || [ "$name" = "$PROJECT_NAME-staging" ]; then
      continue;
    fi

    if [ "$name" = "$PROJECT_NAME-$CI_MERGE_REQUEST_IID" ]; then
      continue;
    fi

    creation_timestamp=$(echo "$row" | jq '.creationTimestamp' | tr -d '"')
    created_at=$(date --date "$creation_timestamp" +"%Y-%m-%d")

    start_date=$(date --date="$last_week" +"%s")
    end_date=$(date --date="$created_at" +"%s")

    if [ "$end_date" -le "$start_date" ]; then
      echo ">>> Retiring service '$name' (created at: $created_at)..."
      retire_service "$name"
    else
      echo ">>> Skipping service '$name' (created at: $created_at)..."
    fi
  done

  rm -f $services_list
}

retire_production() {
  retire_service "$PROJECT_NAME-prod"
}

retire_staging() {
  retire_service "$PROJECT_NAME-staging"
}

ACTION=${1:-"--dev"}
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=0

[ $DEBUG -eq 1 ] && set -x
[ $EXIT_ON_ERROR -eq 1 ] && set -e

if [ -z "$CI_MERGE_REQUEST_IID" ]; then
  CI_MERGE_REQUEST_IID=$(date -u | sha256sum - | cut -d " " -f 1 | head -c8)
fi

case $ACTION in
  --dev)
    retire_dev
    ;;
  --old)
    retire_old_services
    ;;
  --prod)
    retire_production
    ;;
  --staging)
    retire_staging
    ;;
  -h|--help)
    print_usage
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage
    exit 1
    ;;
esac

exit 0
