#!/bin/bash

if [ -z "$GCP_PROJECT_ID" ]; then
  GCP_PROJECT_ID="madesimplegroup-151616"
fi

if [ -z "$GCP_PROJECT_REGION" ]; then
  GCP_PROJECT_REGION="europe-west1"
fi

if [ -z "$PROJECT_NAME" ]; then
  PROJECT_NAME="mailroom"
fi

exit_with_error() {
  echo ">>> Error: $1"
  exit 1
}

write_local_file() {
  file_path="$1"
  content="$2"

  if [ -f "$file_path" ]; then
    echo ">>> WARN: '$file_path' already exists!"
  fi

  if [ -z "$content" ]; then
    exit_with_error "Invalid content!"
  fi

  echo ">>> Generating local file '$file_path'..."

  echo "$content" > "$file_path"
}

verify_docker() {
  exit_code=0

  docker version || exit_code="$?"

  if [ "$exit_code" -ne 0 ]; then
    echo '>>> Error: You do not have docker installed!'
    echo '>>> Please, install docker: https://docs.docker.com/engine/install/'
    exit 1
  fi
}
