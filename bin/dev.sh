#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option]"
  echo "OPTIONS: "
  echo "   --access-app      Access the bash for the app. container"
  echo "   --access-db       Access the bash for the database container"
  echo "   --up              Create and start containers for this project"
  echo "   --down            Stop and remove containers for this project"
  echo "   --setup           Build the docker images for this project"
  echo "   --reset           Stop and remove all containers, images and volumes for this project"
  echo "   --prepare         Run app. container commands to prepare the dev. environment"
  echo "   --npm-watch       Run 'npm run watch' command inside the app. container"
  echo "   --reset-git-hooks Removes the git hooks and the hash files as they are not required anymore"
  echo "   -h|--help         Shows this help message"
}

preparation_commands() {
  docker exec "$APP_CONTAINER" bash -c "composer install --optimize-autoloader"
  docker exec -w "$APP_DIR/client/" "$APP_CONTAINER" bash -c "npm install"
  docker exec -w "$APP_DIR/client/" "$APP_CONTAINER" bash -c "npm run build-dev"
  docker exec "$APP_CONTAINER" bash -c "php bin/console doctrine:migrations:migrate --no-interaction"
}

setup_environment() {
  echo '>>> Setting up development environment...'

  verify_docker

  has_base_image=$(docker images -q --all "$BASE_IMAGE")
  if [ -z "$has_base_image" ]; then
    docker compose build --no-cache project-base
  fi

  has_db_image=$(docker images -q --all "$DATABASE_IMAGE")
  if [ -z "$has_db_image" ]; then
    docker compose build project-database
  fi

  has_app_image=$(docker images -q --all "$APP_IMAGE")
  if [ -z "$has_app_image" ]; then
    docker compose build --no-cache \
      --build-arg BUILD_DATE="$(date -u +'%Y-%m-%d %H:%M:%S %z')" \
      --build-arg BUILD_COMMIT="local" \
      project-app
  fi

  docker compose up project-app -d --wait

  preparation_commands

  docker exec -it "$APP_CONTAINER" bash
}

reset_environment() {
  echo '>>> Reset environment...'

  verify_docker

  exit_code=0
  docker compose down --remove-orphans --rmi=all -v -t=1 || exit_code="$?"

  docker rmi -f "$APP_IMAGE" || exit_code="$?"
  docker rmi -f "$BASE_IMAGE" || exit_code="$?"
  docker rmi -f "$DATABASE_IMAGE" || exit_code="$?"
}

environment_up() {
  echo '>>> Running docker-compose up...'

  has_app_image=$(docker images -q --all "$APP_IMAGE")
  if [ -z "$has_app_image" ]; then
    echo ">>> You don't have the dev. environment setup..."
    setup_environment
    exit 0
  fi

  docker compose up project-app -d --wait --no-build

  npm_watch_command
}

environment_down() {
  echo '>>> Running docker-compose down...'

  docker compose down -t=1
}

access_app_container() {
  echo '>>> Accessing dev. application container...'
  docker exec -it "$APP_CONTAINER" bash
}

access_database_container() {
  echo '>>> Accessing dev. database container...'
  docker exec -it "$DATABASE_CONTAINER" bash
}

npm_watch_command() {
  docker exec -w "$APP_DIR/client/" "$APP_CONTAINER" bash -c "npm run watch"
}

remove_git_hooks_and_hash_files() {
  rm -f .git/hooks/pre-commit
  rm -f .git/hooks/post-commit

  rm -Rf git-hooks/

  rm -f project/client/hash.js
  rm -f project/public/hash.txt
}

ACTION=${1:-"--up"}
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=1

if [ ! -f .env ]; then
  bash bin/prepare.sh --all dev;
fi

export $(grep -v '^#' .env | xargs -r);

[ $DEBUG -eq 1 ] && set -x
[ $EXIT_ON_ERROR -eq 1 ] && set -e

if [ -z "$APP_DIR" ]; then
  APP_DIR="/var/www/project"
fi

APP_IMAGE="$PROJECT_NAME/app-image:latest"
BASE_IMAGE="$PROJECT_NAME/base-image:latest"
DATABASE_IMAGE="${MYSQL_IMAGE:-mysql}:${MYSQL_VERSION:-latest}"

APP_CONTAINER="$PROJECT_NAME-app"
DATABASE_CONTAINER="$PROJECT_NAME-database"

case $ACTION in
  --access-app)
    access_app_container
    ;;
  --access-db)
    access_database_container
    ;;
  --up)
    environment_up
    ;;
  --down)
    environment_down
    ;;
  --setup)
    setup_environment
    ;;
  --reset)
    reset_environment
    ;;
  --prepare)
    preparation_commands
    ;;
  --npm-watch)
    npm_watch_command
    ;;
  --reset-git-hooks)
    remove_git_hooks_and_hash_files
    ;;
  -h|--help)
    print_usage
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage
    exit 1
    ;;
esac

exit 0
