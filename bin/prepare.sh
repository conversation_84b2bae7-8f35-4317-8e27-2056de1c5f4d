#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option] [environment]"
  echo "OPTIONS: "
  echo "   -a|--all [env]    Prepare all dotenv files for the environment"
  echo "   -b|--back [env]   Prepare only the Backend dotenv files for the environment"
  echo "   -d|--docker [env] Prepare only the Docker dotenv files for the environment"
  echo "   -f|--front [env]  Prepare only the Frontend dotenv files for the environment"
  echo "   -h|--help         Shows this help message."
}

generate_frontend_env_file() {
  environment="${1:-dev}"
  file_path="project/client/.env"

  if [ "$environment" != "prod" ]; then
    write_local_file "$file_path" "$FRONTEND_DEV_ENV_FILE_CONTENT"
    return 0
  fi

  write_local_file "$file_path" "$FRONTEND_PROD_ENV_FILE_CONTENT"
}

generate_backend_env_file() {
  environment="${1:-dev}"
  file_path="project/.env"

  if [ "$environment" != "prod" ]; then
    write_local_file "$file_path" "$BACKEND_DEV_ENV_FILE_CONTENT"
    return 0
  fi

  write_local_file "$file_path" "$BACKEND_PROD_ENV_FILE_CONTENT"
}

generate_docker_app_env_file() {
  environment="${1:-dev}"
  file_path=".env"

  if [ "$environment" != "prod" ]; then
    write_local_file "$file_path" "$DOCKER_DEV_ENV_FILE_CONTENT"
    return 0
  fi

  write_local_file "$file_path" "$DOCKER_PROD_ENV_FILE_CONTENT"
}

ACTION=${1:-"--all"}
ENVIRONMENT="${2:-dev}"
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=1

[ "$DEBUG" -eq 1 ] && set -x
[ "$EXIT_ON_ERROR" -eq 1 ] && set -e

if [ -z "$FRONTEND_DEV_ENV_FILE_CONTENT" ]; then
  exit_with_error "The variable 'FRONTEND_DEV_ENV_FILE_CONTENT' must be defined!"
fi

if [ -z "$BACKEND_DEV_ENV_FILE_CONTENT" ]; then
  exit_with_error "The variable 'BACKEND_DEV_ENV_FILE_CONTENT' must be defined!"
fi

if [ -z "$DOCKER_DEV_ENV_FILE_CONTENT" ]; then
  exit_with_error "The variable 'DOCKER_DEV_ENV_FILE_CONTENT' must be defined!"
fi

if [ "$ENVIRONMENT" = "prod" ]; then
  if [ -z "$FRONTEND_PROD_ENV_FILE_CONTENT" ]; then
    exit_with_error "The variable 'FRONTEND_PROD_ENV_FILE_CONTENT' must be defined!"
  fi

  if [ -z "$BACKEND_PROD_ENV_FILE_CONTENT" ]; then
    exit_with_error "The variable 'BACKEND_PROD_ENV_FILE_CONTENT' must be defined!"
  fi

  if [ -z "$DOCKER_PROD_ENV_FILE_CONTENT" ]; then
    exit_with_error "The variable 'DOCKER_PROD_ENV_FILE_CONTENT' must be defined!"
  fi
fi

case $ACTION in
  -a|--all)
    generate_docker_app_env_file "$ENVIRONMENT";
    generate_backend_env_file "$ENVIRONMENT";
    generate_frontend_env_file "$ENVIRONMENT";
    ;;
  -b|--back)
    generate_backend_env_file "$ENVIRONMENT";
    ;;
  -d|--docker)
    generate_docker_app_env_file "$ENVIRONMENT";
    ;;
  -f|--front)
    generate_frontend_env_file "$ENVIRONMENT";
    ;;
  -h|--help)
    print_usage;
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage;
    exit 1
    ;;
esac
