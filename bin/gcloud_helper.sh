#!/bin/bash

source bin/utils.sh

print_usage() {
  echo "Usage: ./$PROGNAME [option]"
  echo "OPTIONS: "
  echo "   --prepare         Authenticate to gcloud CLI"
  echo "   --create-repo     Create artifact repository for this project"
  echo "   --full            Authenticate to gcloud CLI and create artifact repository for this project"
  echo "   -h|--help         Shows this help message."
}

create_artifact_repository() {
  gcloud artifacts repositories describe "$PROJECT_NAME" \
    --location="$GCP_PROJECT_REGION" --project="$GCP_PROJECT_ID" || \
    gcloud artifacts repositories create "$PROJECT_NAME" --repository-format=docker \
    --location="$GCP_PROJECT_REGION" --project="$GCP_PROJECT_ID" --description="$PROJECT_NAME"
}

prepare_GCP() {
  apk add jq coreutils

  if [ -z "$GCLOUD_DEPLOY_KEY_FILE_PRODUCTION" ]; then
    gcloud auth login
  else
    if [ -z "$GCP_AUTH_KEY_FILE_PATH" ]; then
      key_file_name="${CI_PIPELINE_ID:-gcp_auth_key_file}"
      GCP_AUTH_KEY_FILE_PATH="/tmp/$key_file_name.json"
    fi

    echo "$GCLOUD_DEPLOY_KEY_FILE_PRODUCTION" > "$GCP_AUTH_KEY_FILE_PATH"
    gcloud auth activate-service-account --key-file "$GCP_AUTH_KEY_FILE_PATH"
  fi

  gcloud config set project "$GCP_PROJECT_ID"
}

ACTION=${1:-"--prepare"}
PROGNAME=$(basename "$0")
EXIT_ON_ERROR=1
DEBUG=1

[ $DEBUG -eq 1 ] && set -x
[ $EXIT_ON_ERROR -eq 1 ] && set -e

if [ -z "$GCP_PROJECT_ID" ]; then
  exit_with_error "The variable 'GCP_PROJECT_ID' must be defined!"
fi

if [ -z "$GCP_PROJECT_REGION" ]; then
  exit_with_error "The variable 'GCP_PROJECT_REGION' must be defined!"
fi

if [ -z "$PROJECT_NAME" ]; then
  exit_with_error "The variable 'PROJECT_NAME' must be defined!"
fi

case $ACTION in
  --prepare)
    prepare_GCP
    ;;
  --create-repo)
    create_artifact_repository
    ;;
  --full)
    prepare_GCP
    create_artifact_repository
    ;;
  -h|--help)
    print_usage
    ;;
  *)
    echo ">>> Error: Action '$ACTION' is not available!"
    print_usage
    exit 1
    ;;
esac

exit 0
