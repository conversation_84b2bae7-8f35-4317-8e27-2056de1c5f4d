steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'build', '--no-cache',
        '-t', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_TAG}',
        '-t', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_COMMIT_SHA}',
        '.'
      ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'push', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_TAG}',
      ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [
        'push', '${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_PROJECT_NAME}/${_IMAGE_NAME}:${_COMMIT_SHA}',
      ]
substitutions:
  _PROJECT_NAME: mailroom
  _IMAGE_NAME: base-image
  _TAG: latest
  _COMMIT_SHA: latest
