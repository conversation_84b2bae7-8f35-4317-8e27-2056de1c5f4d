ARG PHP_VERSION=8.0-fpm
FROM php:${PHP_VERSION}

# OS update and install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    default-libmysqlclient-dev \
    default-libmysqld-dev \
    git \
    gnupg \
    libicu-dev \
    libzip-dev \
    nano \
    nginx \
    openssl \
    unzip \
    zip && \
    apt-get clean autoclean && \
    apt-get autoremove --yes && \
    rm -rf /var/lib/{apt,dpkg,cache,log}/

# Install PHP extensions
RUN docker-php-ext-install intl pdo pdo_mysql
RUN pecl install apcu && docker-php-ext-enable apcu

# Install nodejs and npm
ARG NODE_VERSION=20
RUN mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
ENV NODE_MAJOR=${NODE_VERSION}
RUN echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list
RUN apt-get update && apt-get install -y nodejs
RUN npm install -g npm@10.2.5 && npm install -g @vue/cli

# Install PHP composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set NGINX config.
COPY nginx-server.conf /etc/nginx/sites-available/default

# Set PHP init config and session timeout
RUN cp $PHP_INI_DIR/php.ini-production $PHP_INI_DIR/php.ini
ARG SESSION_TIMEOUT=3600
RUN sed -i "s/^\(session.gc_maxlifetime *= *\).*/\1$SESSION_TIMEOUT/" $PHP_INI_DIR/php.ini

ARG APP_DIR=/var/www/project
RUN mkdir -p ${APP_DIR}
WORKDIR ${APP_DIR}
